<?php

namespace App\Rules\User;

use App\Models\CountryModel;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class CheckNricValidPeriodRule implements ValidationRule
{
    const PATTERN = '/^(19|20)\d{2}.((0[1-9])|(10|11|12)).(([0-2][1-9])|10|20|30|31)$/';

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (env('COUNTRY') == CountryModel::COUNTRY_ID_CHINA) {
            $dateArr = explode('-', $value);
            if (empty($dateArr[0]) || empty($dateArr[1])) {
                $fail(__('incorrect format of ID card expiration date'));
            }
            $dateArr[0] = trim($dateArr[0]);
            $dateArr[1] = trim($dateArr[1]);
            if (!preg_match(self::PATTERN, $dateArr[0])
                || (!preg_match(self::PATTERN, $dateArr[1]) && $dateArr[1] != '长期')) {
                $fail(__('incorrect format of ID card expiration date'));
            }
            $now = strtotime('today');
            $startTime = strtotime(str_replace('.', '-', $dateArr[0]));
            if (!$startTime || $startTime > $now) {
                $fail(__('incorrect format of ID card expiration date'));
            }
            if ($dateArr[1] != '长期') {
                $endTime = strtotime(str_replace('.', '-', $dateArr[1]));
                if (!$endTime || $startTime > $endTime) {
                    $fail(__('incorrect format of ID card expiration date'));
                }
                if ($endTime <= $now) {
                    $fail(__('ID card has expired'));
                }
            }
        }
    }
}
