<?php
namespace App\Http\Controllers\Api\v1\Activity;

use App\Http\Controllers\Controller;
use App\Models\Activity;
use App\Models\AiManFile;
use App\Models\AiManVideo;
use App\Models\VisitorApply;
use App\Models\VisitorAttach;
use App\Models\VisitorLogin;
use App\Models\VisitorReport;
use App\Models\VisitorSignin;
use App\Rules\Mobile\CheckMobileRule;
use App\Services\OssService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class ActivityController extends Controller
{

    // 获取活动信息
    public function info(Request $request)
    {
        $id = (int)$request->input('id');
        if (empty($id)) {
            return responseFail(__('missing parameter', ['param'=>'id']));
        }

        $info = Activity::find($id);
        if (empty($info)) {
            return responseFail(__('info no exist'));
        }

        return responseSuccess($info);
    }
}