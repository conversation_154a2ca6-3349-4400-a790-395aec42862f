<?php

namespace App\Services;

use Firebase\JWT\JWT;
use Illuminate\Support\Facades\Http;

class UserCenterService
{
    private static function getAuth()
    {
        $payload = [
            'exp' => time() + (60 * 15),
        ];
        return JWT::encode($payload, env('JWT_KEY'), 'HS256');
    }

    public static function login($body)
    {
        $path = '/api/login';
        $url = env('USER_CENTER') . $path;
        $response = Http::/* withoutVerifying()-> */withHeader('Authorization', 'Bearer '.self::getAuth())
            ->post($url, $body);
        if ($response->successful()) {
            $res = $response->json();
            return (isset($res['code']) && $res['code'] == 200)? true: false;
        }
        return false;
    }

    public static function syncUser($body)
    {
        $path = '/api/syncUser';
        $url = env('USER_CENTER') . $path;
        $response = Http::/* withoutVerifying()-> */withHeader('Authorization', 'Bearer '.self::getAuth())
            ->post($url, ['data' => json_encode($body, JSON_UNESCAPED_UNICODE)]);
        if ($response->successful()) {
            $res = $response->json();
            return (isset($res['code']) && $res['code'] == 200)? $res['data']: false;
        }
        return false;
    }
}