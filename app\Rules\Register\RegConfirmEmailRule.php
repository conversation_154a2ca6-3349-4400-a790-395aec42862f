<?php

namespace App\Rules\Register;

use App\Models\EmailDataModel;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use App\Models\ProfileInfoModel;

class RegConfirmEmailRule implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (ProfileInfoModel::existsUserByEmail($value)) {
            $fail('该邮箱已被使用');
        }
        $isEmailVerify = EmailDataModel::where('emailAddress', $value)
            ->where('emailScene', config('email.verification_code_scene.register_verify'))
            ->where('emailState', 1)
            ->exists();
        if (!$isEmailVerify) {
            $fail('该邮箱未通过验证');
        }
    }
}
