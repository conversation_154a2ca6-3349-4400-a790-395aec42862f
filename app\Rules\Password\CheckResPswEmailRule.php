<?php

namespace App\Rules\Password;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use App\Models\ProfileInfoModel;

class CheckResPswEmailRule implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $profileInfoModel = new ProfileInfoModel;
        $data = $profileInfoModel::select('profileID')->where('profileEmail', $value)->first();
        if (!$data) {
            $fail('邮件发送失败');
        }
    }


}
