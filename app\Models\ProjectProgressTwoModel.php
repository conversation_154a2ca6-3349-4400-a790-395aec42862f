<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProjectProgressTwoModel extends Model
{
    use HasFactory;

    protected $table = 'cna_project_progress_two';
    protected $guarded = [];

    public function getProgressTwoInfo($companyID, $userID)
    {
        return self::where('companyID', $companyID)->where('profileID', $userID)->first();
    }
}
