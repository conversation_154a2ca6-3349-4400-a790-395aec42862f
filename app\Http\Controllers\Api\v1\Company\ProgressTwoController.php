<?php

namespace App\Http\Controllers\Api\v1\Company;

use App\Models\ActiveLog;
use App\Services\DocService;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Requests\Company\ProgressTwo\ProgressTwoCommitRequest;
use App\Models\ProjectProgressOneModel;
use App\Models\ProjectProgressTwoModel;
use App\Models\CompanyModel;
use App\Models\BankModel;
use App\Models\PaymentCategoryModel;
use Illuminate\Support\Facades\DB;

class ProgressTwoController extends Controller
{

    private $request;
    private $projectProgressTwoModel;
    private $companyModel;
    private $projectProgressOneModel;
    const ACTION = 'progress_two_view_file'; //场景

    public function __construct(
        Request $request, 
        ProjectProgressTwoModel $projectProgressTwoModel, 
        CompanyModel $companyModel,
        ProjectProgressOneModel $projectProgressOneModel
    )
    {
        $this->request = $request;
        $this->projectProgressTwoModel = $projectProgressTwoModel;
        $this->companyModel = $companyModel;
        $this->projectProgressOneModel = $projectProgressOneModel;
    }
    
    /**
     * Method 支付列表
     *
     * @return void
     */
    public function payments()
    { 
        $data = PaymentCategoryModel::get();
        return responseSuccess($data);
    }
    
    /**
     * Method 银行列表
     *
     * @return void
     */
    public function banks()
    {
        $data = BankModel::select('bankID', 'bankCountry', 'bankName', 'bankSwiftCode')->get();
        return responseSuccess($data);
    }

    public function commitProgressTwo(ProgressTwoCommitRequest $request)
    {
        $user = $request->attributes->get('user');
        $body = $request->all();
        $companyID = $body['companyID'];
        $bankID = $body['bankID'];
        $paymentCategoryID = $body['paymentCategoryID'];
        $paymentCode = $body['paymentCode'];
        $file = $request->file('file');

        try {
            DB::beginTransaction();

            //校验公司
            $company = $this->companyModel->verificationUserCompany($companyID, $user['profileID']);
            if (($company->companyState != 0 && $company->companyState != 3) || $company->companyProgressState != 2) {
                return responseFail();
            }            

            //上传文件
            // $resource = $file->store('files/project/progressTwo/upload', 'public');
            $resource = $file->storeAs('files/project/progressTwo/upload', generateUploadFilename($file->getClientOriginalExtension()), 'public');

            //更新企业状态
            $company->companyState = 1;
            $company->save();

            //更新或插入数据
            $updateOrCreate = [
                'companyID' => $companyID,
                'profileID' => $user['profileID'],
                'bankID' => $bankID,
                'paymentID' => $paymentCategoryID,
                'paymentCode' => $paymentCode,
                'file' => $resource
            ];
            $this->projectProgressTwoModel::updateOrCreate(['companyID' => $companyID], $updateOrCreate);
            //记录活动日志
            ActiveLog::log($user['profileID'], ActiveLog::ACTIVE_UPDATE, ActiveLog::API_V1_COMPANY_PROGRESSTWO_COMMIT,
                $companyID, $updateOrCreate);
            DB::commit();

            return responseSuccess();
        } catch (\Exception $e) {
            DB::rollBack();
            return responseFail();
        }
        
    }

    public function getProgressTwoInfo(Request $request)
    {
        $companyID = $request->input('companyID');
        $user = $request->attributes->get('user');
        $userID = $user['profileID'];
        $company = $this->companyModel->verificationUserCompany($companyID, $userID);
        $bankID = null;
        $paymentCategoryID = null;
        $paymentCode = null;
        $progressOne = $this->projectProgressOneModel->getProgressOneInfo($companyID, $userID);
        $progressTwo = $this->projectProgressTwoModel->getProgressTwoInfo($companyID, $userID);
        $totalPrice = $progressOne->companyServicePrice;
        $currency = $progressOne->serviceCurrency;

        if ($progressTwo) {
            $bankID = $progressTwo->bankID;
            $paymentCategoryID = $progressTwo->paymentID;
            $paymentCode = $progressTwo->paymentCode;
        }
        $compact = compact('bankID', 'paymentCategoryID', 'paymentCode', 'totalPrice', 'currency');

        return responseSuccess($compact);
    }

    public function generateViewFileKey()
    {
        $user = $this->request->attributes->get('user');
        $companyID = $this->request->input('companyID', 0);
        $userID = $user['profileID'];
        $company = $this->companyModel->verificationUserCompany($companyID, $userID);
        $progressTwo = $this->projectProgressTwoModel->getProgressTwoInfo($companyID, $userID);
        //校验进度二数据
        if (!$progressTwo) {
            return responseFail();
        }
        //生成密钥
        $token = DocService::generateToken($progressTwo->file, self::ACTION);

        return responseSuccess($token);
    }

    public function preview()
    {
        $token = $this->request->route('token');

        return DocService::preview($token, self::ACTION);
    }
}
