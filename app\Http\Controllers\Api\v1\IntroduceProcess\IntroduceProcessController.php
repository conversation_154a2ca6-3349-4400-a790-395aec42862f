<?php

namespace App\Http\Controllers\Api\v1\IntroduceProcess;

use App\Http\Controllers\Controller;
use App\Models\IntroduceProcess;
use App\Models\IntroduceProcessModel;
use App\Models\TeamRank;
use App\Services\DocService;
use Illuminate\Http\Request;
use App\Models\BillModel;

class IntroduceProcessController extends Controller
{

    // 提交
    public function done(Request $request)
    {
        $user = $request->attributes->get('user');
        IntroduceProcessModel::updateOrCreate(
            ['profileID' => $user['profileID']]);

        return responseSuccess();
    }

    // 查看
    public function check(Request $request)
    {
        $user = $request->attributes->get('user');
        $data = IntroduceProcessModel::query()->where('profileID', $user['profileID'])->first();
        if ($data) {
            return responseSuccess(['status'=>1]);
        } else {
            return responseSuccess(['status'=>0]);
        }
    }
}
