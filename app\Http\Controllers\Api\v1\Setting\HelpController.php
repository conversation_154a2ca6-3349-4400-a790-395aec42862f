<?php

namespace App\Http\Controllers\Api\v1\Setting;

use App\Http\Controllers\Controller;
use App\Services\DocService;
use Illuminate\Http\Request;

class HelpController extends Controller
{
    const ACTION = 'help_doc'; //场景
    const DOC_PRIVACY = 1; //隐私政策
    const DOC_SERVICE = 2; //服务条款
    const DOC_COOKIES = 3; //Cookies条款
    const DOC_PATH = [
        self::DOC_PRIVACY => 'files/help/privacy.pdf',
        self::DOC_SERVICE => 'files/help/service.pdf',
        self::DOC_COOKIES => 'files/help/cookies.pdf',
    ];
    private $request;

    public function __construct(Request $request)
    {
        $this->request = $request;
    }

    public function generateDocToken()
    {
        $id = $this->request->input('id', 0);
        if (!isset(self::DOC_PATH[$id])) {
            return responseFail();
        }
        $token = DocService::generateToken(self::DOC_PATH[$id], self::ACTION, 3600);

        return responseSuccess($token);
    }

    public function previewDoc()
    {
        $token = $this->request->route('token');

        return DocService::preview($token, self::ACTION);
    }
}
