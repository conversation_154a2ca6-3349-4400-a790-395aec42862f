<?php
namespace App\Http\Controllers\Admin\v1;

use App\Http\Controllers\Controller;
use App\Models\ActiveLog;
use App\Models\CompanyModel;
use App\Models\InquiryExecutedModel;
use App\Models\InquiryLawsModel;
use App\Models\InquiryReportModel;
use App\Models\InquiryTrademarkModel;
use App\Services\OssService;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Symfony\Component\HttpFoundation\Request;

class InquiryLawsController extends Controller
{
    /**
     * 添加国内外法律法规
     * @param Request $request
     * @return void
     */
    public function store(Request $request)
    {
        $param = $request->all();

        // 下载文件
        $file_url = '';
        if ($param['file_path']) {
            $resource = OssService::uploadWebFile($param['file_path']);
            $file_url = $resource ?? '';
        }

        $result = InquiryLawsModel::query()->create([
            'title'                 => $param['title'],
            'issuing_authority'     => $param['issuing_authority'],
            'status'                => $param['status'],
            'effective_date'        => $param['effective_date'],
            'publish_date'          => $param['publish_date'],
            'hierarchy'             => $param['hierarchy'],
            'created_at'            => $param['created_at'],
            'file_path'             => $file_url,
        ]);


        if ($result) {
            return responseSuccess();
        } else {
            return responseFail(__('save failed'));
        }

    }
}