<?php

namespace App\Http\Requests\Setting\LanguageArea;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use App\Exceptions\DefaultException;

class UpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'settingLanguage'   => ['required', 'in:ZH,ZT,EN,MS'],
            'settingTimezone'   => ['required'],
            'settingDateFormat' => ['required'],
            'settingTimeFormat' => ['required'],
            'settingCurrency'   => ['required'],
        ];
    }

    public function messages()
    {
        return [
            'settingLanguage.required'   => '请设置系统语言',
            'settingTimezone.required'   => '请设置时区',
            'settingDateFormat.required' => '请设置日期格式',
            'settingTimeFormat.required' => '请设置时间格式',
            'settingCurrency.required'   => '请设置货币',
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        $error= $validator->errors()->all();
        throw new DefaultException($error[0]);
    }
}
