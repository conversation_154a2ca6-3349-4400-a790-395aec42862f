<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProjectServiceModel extends Model
{
    use HasFactory;

    protected $table = 'cna_project_service_data';
    protected $guarded = [];

    // 指定日期前-缴付加盟费可豁免的项目(加盟开户费,AI账号开通费,AI账号开通费)
    const JOIN_FREE_PROJECT = [48, 50, 52];

    // 收费项目
    const PAY_REGISTER = 47; // 合伙人加盟费
    const PAY_PRE_BOND = 4; // 预审保证金
    const PAY_COMPANY_APPLY = 7; // 企业入驻平台申请费
    const PAY_COMPANY_OPEN = 6; // 企业入驻平台申请费
    const PAY_COMPANY_AIOPEN = 9; // 企业入驻平台申请费
}
