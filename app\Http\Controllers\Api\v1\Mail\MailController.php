<?php

namespace App\Http\Controllers\Api\v1\Mail;

use App\Http\Controllers\Controller;
use App\Http\Requests\Register\CommitVerifyEmail;
use App\Http\Requests\Register\VerifyEmail;
use App\Services\EmailService;
use Illuminate\Http\Request;

class Mail<PERSON>ontroller extends Controller
{
    private $request;
    private $emailService;

    public function __construct(Request $request, EmailService $emailService)
    {
        $this->request = $request;
        $this->emailService = $emailService;
    }

    /**
     * Method 发送验证码
     *
     * @param VerifyEmail $request [explicite description]
     *
     * @return void
     */
    public function sendCode(VerifyEmail $request)
    {
        $body = $request->all();
        $nationalityID = $request->input('nationalityID', '');
        $language = getLanguageByNationalityID($nationalityID);
        $user = $request->attributes->get('user');

        return $this->emailService->sendVerificationCodeEmail($body['email'], $body['scene'], $language,
            $user['profileID']);
    }

    /**
     * Method 检查验证码
     *
     * @param CommitVerifyEmail $request [explicite description]
     *
     * @return void
     */
    public function verifyCode(CommitVerifyEmail $request)
    {
        $body = $request->all();
        $user = $request->attributes->get('user');
        $this->emailService->verifyEmail($body['email'], $body['code'], $body['scene'], $user['profileID']);

        return responseSuccess();
    }

}
