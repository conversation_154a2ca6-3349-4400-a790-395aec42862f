<?php

namespace App\Http\Middleware;

use App\Models\LoginLog;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use App\Models\ProfileInfoModel;

class AdminAuth
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        //获取头部鉴权密钥
        $authorization = $request->header('authorization');
        if (!$authorization) {
            return responseFail('Unauthorized', 401);
        }

        try {
            //解密密钥
            $token = explode(' ', $authorization);
            $token = trim($token[1]);
            $decodeToken = JWT::decode($token, new Key(env('ADMIN_JWT_KEY'), 'HS256'));

            //校验
            if (isset($decodeToken->action)) {
                if ($decodeToken->action == 'admin_auth') {
                    $profileInfoModel = new ProfileInfoModel;
                    $profileID = $decodeToken->user_id;
                    //检验token是否被用户手动退出
                    $tokenList = LoginLog::where('profile_id', $profileID)->where('token_status', 0)->where('token', $token)->first();
                    if ($tokenList) {
                        return responseFail('Unauthorized', 401);
                    }
                    $user = $profileInfoModel->getRecord($profileID)->toArray();
                    $request->attributes->set('user', $user);
                } else {
                    return responseFail('Unauthorized', 401);
                }
            } else {
                return responseFail('Unauthorized', 401);
            }
        } catch (\Exception $e) {
            return responseFail('Unauthorized'.$e->getMessage(), 401);
        }

        return $next($request);
    }

}

