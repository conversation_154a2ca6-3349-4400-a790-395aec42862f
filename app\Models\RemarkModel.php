<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class RemarkModel extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'cna_remark_info';
    protected $guarded = [];
    protected $primaryKey = 'remarkID'; // 自定义主键

    public function profileInfo()
    {
        return $this->belongsTo(ProfileInfoModel::class, 'objID');
    }

    public function companyInfo()
    {
        return $this->belongsTo(companyModel::class, 'objID');
    }

    public function editUser()
    {
        return $this->belongsTo(ProfileInfoModel::class, 'editUser', 'profileID');
    }
}
