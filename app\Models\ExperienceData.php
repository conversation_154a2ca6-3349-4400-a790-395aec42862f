<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ExperienceData extends Model
{
    protected $table = 'cna_experience_data';
    protected $guarded = [];

    const FILE_PATH = 'files/experience';
    //类型
    const TYPE_CREDENTIALS = 1; //资格证书
    const TYPE_SKILL = 2; //专业技能
    const TYPE_CLIENT = 3; //客户资源

    const TYPE_ARR = [
        self::TYPE_CREDENTIALS,
        self::TYPE_SKILL,
        self::TYPE_CLIENT,
    ];

    /**
     * 关联经历
     */
    public function experience()
    {
        return $this->belongsTo(Experience::class, 'experience_id', 'id');
    }

    /**
     * 关联国家
     */
    public function country()
    {
        return $this->belongsTo(CountryModel::class, 'country_id', 'countryID');
    }

    /**
     * 关联行业
     */
    public function industry()
    {
        return $this->belongsTo(Industry::class, 'industry_id', 'id');
    }
}
