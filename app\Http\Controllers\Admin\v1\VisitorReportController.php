<?php

namespace App\Http\Controllers\Admin\v1;

use App\Http\Controllers\Controller;
use App\Models\AiManFile;
use App\Models\CompanyGsp;
use App\Models\CompanyGspReport;
use App\Models\CountryModel;
use App\Models\GspOption;
use App\Models\VisitorApply;
use App\Models\VisitorAttach;
use App\Models\VisitorLogin;
use App\Models\VisitorReport;
use App\Models\VisitorSignin;
use App\Services\DocService;
use App\Services\OssService;
use App\Services\ZipService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class VisitorReportController extends Controller
{

    /**
     * 报告列表
     * @param Request $request
     * @return null
     */
    public function index(Request $request)
    {
        $keyword = $request->get('keyword', '');
        $pageSize = $request->get('page_size', 10);
        $start_date = $request->get('start_date', '');;  // 开始时间
        $end_date = $request->get('end_date', '');;  // 结束时间

        $list = VisitorReport::query()
            ->when($keyword != '', function ($q) use ($keyword) {

                $q->where(function ($q) use ($keyword) {
                    $q->orWhere('report_author', 'like', '%' . $keyword . '%')
                        ->orWhere('visitor_purpose', 'like', '%' . $keyword . '%')
                        ->orWhere('content', 'like', '%' . $keyword . '%');
                });


            })->when($start_date !='', function ($q) use ($start_date) {
                $q->where('visitor_date', '>=', $start_date);
            })->when($end_date !='', function ($q) use ($end_date) {
                $q->where('visitor_date', '<=', $end_date);
            })
            ->orderBy('id', 'desc')
            ->paginate($pageSize);

        $items = $list->items();



        $paginate = [
            'page_size' => $pageSize,
            'total' => $list->total(),
            'total_page' => $list->lastPage(),
        ];

        return responseSuccess(compact('items', 'paginate'));
    }

    /**
     * 查看会谈结果
     * @param Request $request
     * @return void
     */
    public function detail(Request $request)
    {
        $id = $request->input('id');
        $type = $request->input('type');
        if (empty($id)) {
            return responseFail(__('missing parameter', ['param' => 'id']));
        }


        // 查看
        $data = VisitorReport::where('id', $id)->first();

        // 企业附件
        $arrAttach = [];
        $attachs = VisitorAttach::query()->where('obj_id', $id)->where('type', 3)->get()->toArray();
        if ($attachs) {
            foreach ($attachs as $attach) {
                $arrAttach[] = OssService::link($attach['file_path']);;
            }
            $data['attach'] = $arrAttach;
        }


        return responseSuccess($data);
    }

    /**
     * 查看会谈结果
     * @param Request $request
     * @return void
     */
    public function report(Request $request)
    {
        $id = $request->input('id');
        $type = $request->input('type');
        if (empty($id)) {
            return responseFail(__('missing parameter', ['param' => 'id']));
        }

        if (empty($type)) {
            return responseFail(__('missing parameter', ['param' => 'type']));
        }

        // 查看
        $data = VisitorReport::where('visitor_id', $id)->where('type', $type)->first();


        return responseSuccess($data);
    }



}
