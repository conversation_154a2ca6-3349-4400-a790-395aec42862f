<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class BillDescription extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'cna_bill_descriptions';

    protected $fillable = [
        'descriptionZH',
        'descriptionZT',
        'descriptionMS',
        'descriptionEN',
    ];

    public function bills()
    {
        return $this->hasMany(BillModel::class, 'billDescriptionId');
    }
}
