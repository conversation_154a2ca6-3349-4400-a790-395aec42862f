<?php

namespace App\Http\Requests\Company\ProgressOne;

use App\Rules\Company\ProgressOne\CheckValueAddedServiceRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use App\Exceptions\DefaultException;

class ProgressOneCommit extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'companyID'       => ['required'],
            'companyServices' => ['required', new CheckValueAddedServiceRule],
            'preAuditForm'    => ['required', 'file', 'mimes:pdf', 'max:'.(env('ALLOW_FILE_SIZE')*1024)]
        ];
    }

    public function messages()
    {
        return [
            'companyID.required'       => __('operation failed'),
            'companyServices.required' => __('please select value added services'),
            'preAuditForm.required'    => __('please upload pre review form'),
            'preAuditForm.file'        => __('please upload pre review form'),
            'preAuditForm.mimes'       => __('please upload the pre review form in PDF format'),
            'preAuditForm.max'         => __('exceed size file', ['limit' => env('ALLOW_FILE_SIZE').'M']),
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        $error= $validator->errors()->all();
        throw new DefaultException($error[0]);
    }
}
