<?php

namespace App\Http\Controllers\Admin\v1;

use App\Http\Controllers\Controller;
use App\Models\Activity;
use App\Models\ActivitySignup;
use App\Models\AiManFile;
use App\Models\CompanyGsp;
use App\Models\CompanyGspReport;
use App\Models\CountryModel;
use App\Models\GspOption;
use App\Models\VisitorApply;
use App\Models\VisitorAttach;
use App\Models\VisitorLogin;
use App\Services\DocService;
use App\Services\OssService;
use App\Services\ZipService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ActivitySignupController extends Controller
{

    /**
     * 报名信息
     * @param Request $request
     * @return null
     */
    public function signup(Request $request)
    {
        $keyword = $request->get('keyword', '');
        $pageSize = $request->get('page_size', 10);
        $id       = $request->get('id');

        $list = ActivitySignup::with(['activity'])
            ->when($id != '', function ($q) use ($id) {
                $q->where('activity_id', $id);
            })
            ->when($keyword != '', function ($q) use ($keyword) {

                $q->where(function ($q) use ($keyword) {
                    $q->orWhere('name', 'like', '%' . $keyword . '%')
                        ->orWhere('contact', 'like', '%' . $keyword . '%')
                        ->orWhere('email', 'like', '%' . $keyword . '%')
                        ->orWhere('company_name', 'like', '%' . $keyword . '%')
                        ->orWhere('company_job', 'like', '%' . $keyword . '%')
                        ->orWhere('company_area', 'like', '%' . $keyword . '%')
                        ->orWhere('company_introduction', 'like', '%' . $keyword . '%');
                });

            })->orderBy('id', 'desc')
            ->paginate($pageSize);

        $items = $list->items();

        $items = collect($items)->map(function ($item) {
            $item->active_name = isset($item->activity) ? $item->activity->active_name : null;
            $item->active_time = isset($item->activity) ? $item->activity->active_time : null;
            $item->active_place = isset($item->activity) ? $item->activity->active_place : null;
            unset($item->activity);
            return $item;
        });

        $paginate = [
            'page_size' => $pageSize,
            'total' => $list->total(),
            'total_page' => $list->lastPage(),
        ];

        return responseSuccess(compact('items', 'paginate'));
    }


}
