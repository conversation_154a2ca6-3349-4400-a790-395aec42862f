<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DocumentFileDataModel extends Model
{
    use HasFactory;

    protected $table = 'cna_document_file_data';
    protected $guarded = [];
    protected $primaryKey = 'documentXID';


    /**
     * 生成文件版本号
     * @return void
     */
/*    public function setDocumentVersionAttribute(): void
    {
        $pre = date('Ym.d');
        $version = 'v'.$pre;
        $this->attributes['documentVersion'] = $version;
    }*/
}
