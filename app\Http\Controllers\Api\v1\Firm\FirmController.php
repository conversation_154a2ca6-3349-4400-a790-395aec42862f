<?php

namespace App\Http\Controllers\Api\v1\Firm;

use App\Http\Controllers\Controller;
use App\Models\Firm;
use App\Models\FirmRelation;
use App\Models\ProfileInfoModel;
use App\Rules\Mobile\CheckMobileRule;
use App\Services\OssService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class FirmController extends Controller
{

    // 资料申请
    public function add(Request $request)
    {
        $user = $request->attributes->get('user');
        $body = $request->all();
        $validator = Validator::make($request->all(), [
            'company_name' => 'required',
            'credit_code' => 'required',
            'registered_capital' => 'required',
            'paid_capital' => 'required',
            'office_type' => 'required',
            'office_address' => 'required',
            'intro' => 'required',
            'range' => 'required',
            'main_business' => 'required',
            'leader_intro' => 'required',
            'honors' => 'required',
            'customer_name' => 'required',
            'lawsuit' => 'required',
            'income' => 'required',
            'cost' => 'required',
            'total_assets' => 'required',
            'total_liabilities' => 'required',
            'employees'    => 'required',
            'file1' => 'required',
            'file2' => 'required',
            'file3' => 'required',
            'file4' => 'required',
            'file5' => 'required',
            'file6' => 'required',
            'file7' => 'required',
             // 调整需要7个文件了
            'file8' => 'nullable',
            'file9' => 'nullable',
            'taxpayer_type' => 'required|integer|in:1,2',
            'bank_name' => 'required|string',
            'bank_account' => 'required|string',
            'tax_phone' => ['required', new CheckMobileRule],
            'tax_address' => 'required|string',
        ]);

        // 验证失败
        if ($validator->fails()) {
            $errors = $validator->errors()->all();
            return responseFail($errors[0]);
        }


        $file1 = $request->file('file1');
        $file2 = $request->file('file2');
        $file3 = $request->file('file3');
        $file4 = $request->file('file4');
        $file5 = $request->file('file5');
        $file6 = $request->file('file6');
        $file7 = $request->file('file7');
        $file8 = $request->file('file8');
        $file9 = $request->file('file9');

        $update = [
            'company_name'          => $body['company_name'],
            'credit_code'           => $body['credit_code'],
            'registered_capital'    => $body['registered_capital'],
            'paid_capital'          => $body['paid_capital'],
            'office_type'           => $body['office_type'],
            'office_address'        => $body['office_address'],
            'intro'                 => $body['intro'],
            'range'                 => $body['range'],
            'main_business'         => $body['main_business'],
            'leader_intro'          => $body['leader_intro'],
            'honors'                => $body['honors'],
            'customer_name'         => $body['customer_name'],
            'lawsuit'               => $body['lawsuit'],
            'income'                => $body['income'],
            'cost'                  => $body['cost'],
            'total_assets'          => $body['total_assets'],
            'total_liabilities'     => $body['total_liabilities'],
            'employees'             => $body['employees'],
            'status'                => 1,
            'taxpayer_type'         => $body['taxpayer_type'],
            'bank_name'             => $body['bank_name'],
            'bank_account'          => $body['bank_account'],
            'tax_phone'             => $body['tax_phone'],
            'tax_address'           => $body['tax_address'],
        ];
        if ($file1) {
            //$resource = $file1->store('files/firm', 'public');
            $resource = OssService::upload($file1);
            $resource && $update['file1'] = $resource;
        }
        if ($file2) {
            //$resource = $file2->store('files/firm', 'public');
            $resource = OssService::upload($file2);
            $resource && $update['file2'] = $resource;
        }
        if ($file3) {
            //$resource = $file3->store('files/firm', 'public');
            $resource = OssService::upload($file3);
            $resource && $update['file3'] = $resource;
        }
        if ($file4) {
            //$resource = $file4->store('files/firm', 'public');
            $resource = OssService::upload($file4);
            $resource && $update['file4'] = $resource;
        }
        if ($file5) {
            //$resource = $file5->store('files/firm', 'public');
            $resource = OssService::upload($file5);
            $resource && $update['file5'] = $resource;
        }

        if ($file6) {
            //$resource = $file5->store('files/firm', 'public');
            $resource = OssService::upload($file6);
            $resource && $update['file6'] = $resource;
        }

        if ($file7) {
            //$resource = $file5->store('files/firm', 'public');
            $resource = OssService::upload($file7);
            $resource && $update['file7'] = $resource;
        }

        if ($file8) {
            //$resource = $file5->store('files/firm', 'public');
            $resource = OssService::upload($file8);
            $resource && $update['file8'] = $resource;
        }

        if ($file9) {
            //$resource = $file5->store('files/firm', 'public');
            $resource = OssService::upload($file9);
            $resource && $update['file9'] = $resource;
        }

        Firm::updateOrCreate(
            ['profile_id' => $user['profileID']],
            $update
        );
        $fr = FirmRelation::where('profile_id', $user['profileID'])->first();
        if (empty($fr)) {
            FirmRelation::create(['profile_id' => $user['profileID']]);
        }

        return responseSuccess();
    }

    /**
     *
     * @param Request $request
     * @return void
     */
    public function status(Request $request)
    {
        $user = $request->attributes->get('user');
        $data = Firm::query()->where('profile_id', $user['profileID'])->first();
        if (empty($data)) {
            $status = 0;
        } else {
            $status = $data['status'];
        }
        return responseSuccess(['status' => $status]);
    }

    public function list(Request $request)
    {
        $request->validate([
            'page' => ['integer', 'min:1'],
            'pageSize' => ['integer', 'min:1'],
        ]);
        $body = $request->all();
        $pageSize = $body['pageSize'] ?? 10;
        $user = $request->attributes->get('user');
        $firm = FirmRelation::where('profile_id', $user['profileID'])->first();
        if (empty($firm) || (!$firm['role'] && $firm['pre_id'])) {
            return responseSuccess();
        }
        if ($firm['role']) {
            $firmOwner = $firm['pre_id'];
        } else {
            $firmOwner = $user['profileID'];
        }
        $res = FirmRelation::select('profile_id', 'role')->where('pre_id', $firmOwner)->paginate($pageSize);
        $ids = $res->getCollection()->pluck('profile_id')->toArray();
        $column = ['profileID', 'profileName', 'profilePartnerCode', 'profileAvatar'];
        $firmUsers = ProfileInfoModel::select($column)
            ->whereIn('profileID', $ids)
            ->whereIn('status', [ProfileInfoModel::STATUS_VERIFYING, ProfileInfoModel::STATUS_ACTIVE])
            ->get()
            ->toArray();
        $firmUsers = array_column($firmUsers, null, 'profileID');
        if (count($ids) != count($firmUsers)) {
            //去掉非正常状态的用户
            $filteredData = $res->getCollection()->reject(function ($item) use ($firmUsers) {
                return !in_array($item->profile_id, array_keys($firmUsers));
            });
            $res->setCollection($filteredData);
            $res->total -= 1;
            $res->count -= 1;
        }
        foreach ($res as &$item) {
            foreach ($firmUsers[$item['profile_id']] as $k => $v) {
                $item[$k] = $v;
            }
            $item['profileAvatar'] = storageUrl($item['profileAvatar'] ?: ProfileInfoModel::AVATAR_PATH);
            $item['profilePartnerCode'] = $item['profilePartnerCode'] ?: '';
        }

        return responseSuccess($res);
    }

    /**
     * 申请信息
     * @param Request $request
     * @return void
     */
    public function info(Request $request)
    {
        $user = $request->attributes->get('user');
        $data = Firm::query()->where('profile_id', $user['profileID'])->first();
        return responseSuccess($data);
    }

    public function setAdmin(Request $request)
    {
        $body = $request->validate([
            'profile_id' => ['required', 'integer', 'min:1'],
            'role' => ['required', 'in:0,1'],
        ]);
        $user = $request->attributes->get('user');
        $firmUser = FirmRelation::where('profile_id', $user['profileID'])->first();
        if (empty($firmUser) || (!$firmUser['role'] && $firmUser['pre_id'])) {
            return responseFail();
        }
        if ($firmUser['role']) {
            $firmOwner = $firmUser['pre_id'];
        } else {
            $firmOwner = $firmUser['profile_id'];
        }
        FirmRelation::where('profile_id', $body['profile_id'])->where('pre_id', $firmOwner)
            ->update(['role' => $body['role']]);
        
        return responseSuccess();
    }

    public function quit(Request $request)
    {
        $body = $request->validate([
            'profile_id' => ['integer', 'min:1'],
        ]);
        $profileId = $body['profile_id'] ?? 0;
        $user = $request->attributes->get('user');
        //创建者或管理员踢出成员
        if ($profileId) {
            if ($profileId == $user['profileID']) {
                return responseFail();
            }
            $firmUser = FirmRelation::where('profile_id', $user['profileID'])->first();
            //发起人不是创建者或管理员
            if (empty($firmUser) || (!$firmUser['role'] && $firmUser['pre_id'])) {
                return responseFail();
            }
            $quitFirmUser = FirmRelation::where('profile_id', $profileId)->first();
            //被踢人不在联号事务所或是创建者
            if (empty($quitFirmUser) || !$quitFirmUser['pre_id']
                || ($quitFirmUser['pre_id'] != $firmUser['pre_id'] && $quitFirmUser['pre_id'] != $user['profileID'])) {
                return responseFail();
            }
            //管理员不能踢出管理员
            if ($firmUser['role'] && $quitFirmUser['role']) {
                return responseFail();
            }
        }
        FirmRelation::where('profile_id', $profileId ?: $user['profileID'])->where('pre_id', '>', 0)
            ->delete();
        
        return responseSuccess();
    }
}