<?php

namespace App\Rules\Register;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use App\Models\CountryModel;

class NationalityRule implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $countryModel = new CountryModel;
        if (!$countryModel->getRecord($value)) {
            $fail('请选择国籍');
        }
    }
}
