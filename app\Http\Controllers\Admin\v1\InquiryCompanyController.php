<?php
namespace App\Http\Controllers\Admin\v1;

use App\Http\Controllers\Controller;
use App\Models\ActiveLog;
use App\Models\CompanyModel;
use App\Models\InquiryCompanyModel;
use App\Models\InquiryReportModel;
use Illuminate\Support\Facades\Validator;
use Symfony\Component\HttpFoundation\Request;

class InquiryCompanyController extends Controller
{

    /**
     * 添加公司基础信息
     * @param Request $request
     * @return void
     */
    public function store(Request $request)
    {

        $param = $request->all();

        $result = InquiryCompanyModel::query()->create([
            'unified_social_credit_code'    => $param['unified_social_credit_code'],
            'company_name'                  => $param['company_name'],
            'registration_status'           => $param['registration_status'],
            'legal_representative'          => $param['legal_representative'],
            'registered_capital'            => $param['registered_capital'],
            'paid_in_capital'               => $param['paid_in_capital'],
            'taxpayer_number'               => $param['taxpayer_number'],
            'taxpayer_qualification'        => $param['taxpayer_qualification'],
            'import_export_code'            => $param['import_export_code'],
            'establishment_date'            => $param['establishment_date'],
            'business_term'                 => $param['business_term'],
            'company_type'                  => $param['company_type'],
            'staff_size'                    => $param['staff_size'],
            'registration_authority'        => $param['registration_authority'],
            'english_name'                  => $param['english_name'],
            'registered_address'            => $param['registered_address'],
            'postal_code'                   => $param['postal_code'],
            'business_scope'                => $param['business_scope'],

        ]);

        if ($result) {
            return responseSuccess();
        } else {
            return responseFail(__('save failed'));
        }

    }
}