<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Setting extends Model
{
    protected $table = 'cna_setting';
    protected $guarded = [];
    //设置-信息与通知，通知方式
    const NOTIFY_TYPE_EMAIL = 1;
    const NOTIFY_TYPE = [
        self::NOTIFY_TYPE_EMAIL => '电子邮件',
    ];

    /**
     * 获取用户语言
     * @param $userID
     * @param $case 0 大写, 1小写
     * @return string
     */
    public static function getLang($userID, $case = 0)
    {
        $lang = self::where('profile_id', $userID)->value('language');
        $lang = empty($lang) ? 'zh' : $lang;
        $lang = empty($case) ? strtoupper($lang) : strtolower($lang);
        return $lang;
    }
}
