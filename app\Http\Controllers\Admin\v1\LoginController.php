<?php
namespace App\Http\Controllers\Admin\v1;

use App\Http\Controllers\Controller;
use App\Models\ActiveLog;
use App\Models\ActiveLogModel;
use App\Models\LoginLog;
use App\Models\ProfileInfoModel;
use App\Models\Setting;
use Firebase\JWT\JWT;
use Illuminate\Support\Facades\Validator;
use Jenssegers\Agent\Agent;
use Symfony\Component\HttpFoundation\Request;

class LoginController extends Controller
{

    /**
     * 用户登录
     * @param Login $request
     * @return null
     */
    public function login(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email'   => 'required',
            'password' => 'required',
            'captcha_key' => 'required',
            'captcha_code'   => 'required',
        ]);

        // 验证失败
        if ($validator->fails()) {
            return responseFail($validator->errors()->all());
        }

        $email          = $request->email;
        $password       = $request->password;
        $captchaKey     = $request->captcha_key;
        $captchaCode    = $request->captcha_code;

        // 验证码校验
        if (!captcha_api_check($captchaCode, $captchaKey)) {
            return responseFail(__('captcha code error')); // 图形验证码错误
        }

        // 账户校验
        $hashPassword = hashPassword($password);
        $profileInfo = ProfileInfoModel::where('profileEmail', $email)->first();
        if (!$profileInfo || !ProfileInfoModel::checkPassword($hashPassword, $profileInfo['profilePassword'])) {
            return responseFail(__('account or password error')); // 账号或密码错误
        } else if ($profileInfo->profileRole !=2 && $profileInfo->profileName != 'admin') {
            return responseFail(__('The role no allow login')); // 没有相应权限
        } else if ($profileInfo->status != ProfileInfoModel::STATUS_ACTIVE) {
            return responseFail(__('User not allowed log in')); // 用户禁止登录
        } else {
            // 生成token
            $payload = [
                'exp' => time() + (3600 * 24 * 7),
                'iat' => time(),
                'action' => 'admin_auth', // 行政后台登录
                'user_id' => $profileInfo->profileID,
            ];
            $token = JWT::encode($payload, env('ADMIN_JWT_KEY'), 'HS256');

            $responseData = ['token' => 'Bearer '.$token];

            //写入登录日志
            $agent = new Agent();
            $agent->setUserAgent($request->userAgent());
            $device = ($agent->platform() ?: '') . ' ' . ($agent->browser() ?: '');
            $insert = [
                'profile_id' => $profileInfo->profileID,
                'token' => $token,
                'device' => trim($device),
                'ip' => $request->ip(),
            ];
            LoginLog::create($insert);

            return responseSuccess($responseData); //登录成功
        }

    }

    /**
     *  设置语言
     * @return void
     */
    public function setLanguage(\Illuminate\Http\Request $request)
    {
        $user = $request->attributes->get('user');
        $languageCode = $request->input('languageCode');
        $res = ProfileInfoModel::where('profileID', $user['profileID'])->update([
            'settingLanguage' => $languageCode
        ]);
        if ($res) {
            return responseSuccess();
        } else {
            return responseFail(__('save failed'));
        }
    }

    /**
     *  安全设置
     * @return void
     */
    public function setSecurity(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'currentPassword'   => 'required',
            'newPassword'       => 'required',
            'passwordConfirmation' => 'required',
        ]);

        // 验证失败
        if ($validator->fails()) {
            return responseFail($validator->errors()->all());
        }

        $user = $request->attributes->get('user');
        $currentPassword      = $request->currentPassword;
        $newPassword          = $request->newPassword;
        $passwordConfirmation = $request->passwordConfirmation;
        $currentHashPassword = hashPassword($currentPassword);
        $newHashPassword = hashPassword($newPassword);
        $confirmationHashPassword = hashPassword($passwordConfirmation);
        //确认新密码
        if ($newHashPassword != $confirmationHashPassword) {
            return responseFail(__('Two passwords are inconsistent'));
        }
        //检验旧密码
        $user = ProfileInfoModel::where('profileID', $user['profileID'])->first();
        if (!ProfileInfoModel::checkPassword($currentHashPassword, $user['profilePassword'])) {
            return responseFail(__('old password error'));
        }
        //修改密码、注册设置进度状态
        $user->profilePassword = $newHashPassword;
        $user->save();

        return responseSuccess();
    }

    /**
     * Method 用户设置
     * @return void
     */
    public function setProfile(Request $request)
    {
        $file = $request->file('file');
        $user = $request->attributes->get('user');
        //上传照片
        $resource = $file->storeAs('images/avatar', generateUploadFilename($file->getClientOriginalExtension()),
            'public');
        //更新用户信息
        ProfileInfoModel::where('profileID', $user['profileID'])->update([
            'profileAvatar' => $resource
        ]);

        return responseSuccess();
    }

    // 登录行政用户的信息 
    public function me()
    {
        $user = request()->attributes->get('user');
        // 用户的权限信息
        $user['permissions'] = ProfileInfoModel::getAdminPermissions($user['profileID']);
        // 用户的设置信息
        $setting = Setting::where('profile_id', $user['profileID'])->first();
        if ($setting) {
            $user['settingLanguage'] = $setting['language'];
            $user['settingTimezone'] = $setting['timezone'];
            $user['settingDateFormat'] = $setting['date_format'];
            $user['settingTimeFormat'] = $setting['time_format'];
            $user['settingCurrency'] = $setting['currency'];
            $user['settingNotifyType'] = $setting['notify_type'];
            $user['settingNotifyEmergency'] = $setting['notify_emergency'];
            $user['settingNotifySuspiciousOperation'] = $setting['notify_suspicious_operation'];
            $user['settingNotifySafeUpdated'] = $setting['notify_safe_updated'];
            $user['settingNotifyRecPrivateMsg'] = $setting['notify_rec_private_msg'];
            $user['settingNotifyImportanceUpdate'] = $setting['notify_importance_update'];
            $user['settingNotifySystemUpdate'] = $setting['notify_system_update'];
            $user['settingNotifyJoinInvestigate'] = $setting['notify_join_investigate'];
            $user['settingNotifyBill'] = $setting['notify_bill'];
        }
        return responseSuccess($user);
    }
}