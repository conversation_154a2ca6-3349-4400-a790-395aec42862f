<?php

namespace App\Rules\Project;

use App\Models\ProjectCategoryModel;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class CheckProjectCategoryRule implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $model = new ProjectCategoryModel;
        if (!$model->getRecord($value)) {
            $fail('请选择项目');
        }
    }
}
