<?php

namespace App\Http\Controllers\Api\v1\Company;

use App\Http\Controllers\Controller;
use App\Http\Requests\Company\ProgressFour\ProgressFourUploadFileRequest;
use App\Models\ActiveLog;
use App\Services\DocService;
use Illuminate\Http\Request;
use App\Models\ProjectProgressFourUserFileModel;
use App\Models\ProjectProgressFourFileModel;
use App\Models\CompanyModel;
use Illuminate\Support\Facades\Storage;

class ProgressFourController extends Controller
{
    private $request;
    private $projectProgressFourUserFileModel;
    private $projectProgressFourFileModel;
    private $companyModel;
    const ACTION = 'progress_four_view_file'; //场景

    public function __construct(
        Request $request, 
        ProjectProgressFourUserFileModel $projectProgressFourUserFileModel, 
        ProjectProgressFourFileModel $projectProgressFourFileModel,
        CompanyModel $companyModel
        )
    {
        $this->request = $request;
        $this->projectProgressFourUserFileModel = $projectProgressFourUserFileModel;
        $this->projectProgressFourFileModel = $projectProgressFourFileModel;
        $this->companyModel = $companyModel;
    }
    
    /**
     * Method 获取进度四信息
     *
     * @return void
     */
    public function getProgressFourInfo()
    {
        $user = $this->request->attributes->get('user');
        $userID = $user['profileID'];
        $companyID = $this->request->input('companyID');
        
        //校验公司
        $company = $this->companyModel->verificationUserCompany($companyID, $userID);

        //进度四信息
        $progressFour = $this->projectProgressFourUserFileModel->getProgressFourInfo($companyID, $userID);
        
        //初始数据结构
        $structure = $this->projectProgressFourFileModel->groupStructure();

        //重组数据
        if ($progressFour) {
            foreach ($progressFour as $v) {
                $structure[$v->fileID]['isUpload'] = 1;
                $structure[$v->fileID]['state'] = $v->state;
                $structure[$v->fileID]['reason'] = !$v->reason ? null : $v->reason;
            }
        }
        $structure = array_values($structure);

        return responseSuccess($structure);
    }
    
    /**
     * Method 提交进度四数据
     *
     * @return void
     */
    public function progressFourCommit()
    {
        $user = $this->request->attributes->get('user');
        $userID = $user['profileID'];
        $companyID = $this->request->input('companyID');

        //校验公司
        $company = $this->companyModel->verificationUserCompany($companyID, $userID);
        if (($company->companyState != 0 && $company->companyState != 3) || $company->companyProgressState != 4) {
            return responseFail();
        }

        //用户上传文件数据
        $userUploadFileIDs = $this->projectProgressFourUserFileModel->getUserUploadFileIDs($companyID, $userID);

        //系统必选文件数据
        $systemRequiredFileIDs = $this->projectProgressFourFileModel->getRequiredUploadFiles();

        //重新排序
        sort($userUploadFileIDs);
        sort($systemRequiredFileIDs);

        //校验用户上传必选文件是否匹配
        if ($userUploadFileIDs != $systemRequiredFileIDs) {
            return responseFail(__('please upload complete file'));
        }

        //更新company
        $company->companyState = 1;
        $company->save();
        $update = ['companyState' => 1];
        //记录活动日志
        ActiveLog::log($user['profileID'], ActiveLog::ACTIVE_UPDATE, ActiveLog::API_V1_COMPANY_PROGRESSFOUR_COMMIT,
            $companyID, $update);

        return responseSuccess();
    }

    public function upload(ProgressFourUploadFileRequest $request)
    {
        $user = $request->attributes->get('user');
        $userID = $user['profileID'];
        $body = $request->all();
        $companyID = $body['companyID'];
        $fileID = $body['fileID'];
        $file = $request->file('file');

        //记录数据
        $record = $this->projectProgressFourUserFileModel->getRecord($fileID, $userID, $companyID);

        //上传文件
        // $resource = $file->store('files/project/progressFour/upload', 'public');
        $resource = $file->storeAs('files/project/progressFour/upload', generateUploadFilename($file->getClientOriginalExtension()), 'public');

        //更新或新增数据
        if ($record) {
            //记录状态处于审核中是不能修改，未提交或审核不通过可修改
            if ($record->state != 3) {
                return responseFail();
            }

            //删除旧文件
            $oldResource = $record->file;
            Storage::disk('public')->delete($oldResource);

            //更新数据
            $record->state = 1;
            $record->file = $resource;
            $record->save();
            $update = ['state' => 1, 'file' => $resource];
            //记录活动日志
            ActiveLog::log($user['profileID'], ActiveLog::ACTIVE_UPDATE, ActiveLog::API_V1_COMPANY_PROGRESSFOUR_UPLOAD,
                $companyID, $update);
        } else {
            $insert = [
                'fileID' => $fileID,
                'profileID' => $userID,
                'companyID' => $companyID,
                'file' => $resource,
                'state' => 1
            ];
            $this->projectProgressFourUserFileModel::create($insert);
            //记录活动日志
            ActiveLog::log($user['profileID'], ActiveLog::ACTIVE_CREATE, ActiveLog::API_V1_COMPANY_PROGRESSFOUR_UPLOAD,
                $companyID, $insert);
        }

        return responseSuccess();
    }
    
    /**
     * Method 生成文件密钥
     *
     * @return void
     */
    public function generateFileKey()
    {
        $user = $this->request->attributes->get('user');
        $companyID = $this->request->input('companyID', 0);
        $fileID = $this->request->input('fileID', 0);
        $userID = $user['profileID'];
        $company = $this->companyModel->verificationUserCompany($companyID, $userID);
        $record = $this->projectProgressFourUserFileModel->getRecord($fileID, $userID, $companyID);
        //校验进度二数据
        if (!$record) {
            return responseFail();
        }
        //生成密钥
        $token = DocService::generateToken($record->file, self::ACTION);

        return responseSuccess($token);
    }

    /**
     * Method 预览文件
     *
     * @return void
     */
    public function preview()
    {
        $token = $this->request->route('token');

        return DocService::preview($token, self::ACTION);
    }

    /**
     * Method 下载文件
     *
     * @return void
     */
    public function download()
    {
        $token = $this->request->route('token');

        return DocService::download($token, self::ACTION);
    }
}
