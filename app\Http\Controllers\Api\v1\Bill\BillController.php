<?php

namespace App\Http\Controllers\Api\v1\Bill;

use App\Http\Controllers\Controller;
use App\Models\TeamRank;
use App\Services\DocService;
use Illuminate\Http\Request;
use App\Models\BillModel;

class BillController extends Controller
{
    private $request;
    private $billModel;

    public function __construct(Request $request, BillModel $billModel)
    {
        $this->request = $request;
        $this->billModel = $billModel;
    }

    public function billList()
    {
        $user = $this->request->attributes->get('user');
        $pageSize = $this->request->input('pageSize', 10);
        $keyword = $this->request->input('keyword', '');
        $where = [];

        if ($keyword) {
            $where = [['billCompanyName', 'like', '%'.$keyword.'%']];
        }

        $data = $this->billModel::with(['billCompany', 'billDescription'])
                                ->select('billID', 'billCompanyName', 'billDescription', 'billAmount', 'billState')
                                // ->where($where)
                                ->when($keyword != '',  function ($q) use ($keyword) {
                                    $q->whereHas('billCompany', function ($q) use ($keyword) {
                                        $q->where('nameZH', 'like', '%'.$keyword.'%')
                                          ->orWhere('nameZT', 'like', '%'.$keyword.'%')
                                          ->orWhere('nameMS', 'like', '%'.$keyword.'%')
                                          ->orWhere('nameEN', 'like', '%'.$keyword.'%');
                                    });
                                })
                                ->where('billProfileID', $user['profileID'])
                                ->orderByDesc('created_at')
                                ->paginate($pageSize);
        $bills = $data->items();

/*        foreach ($bills as $k => &$item) {
            $item['billAmount'] = $item['billAmount'].'(CNY)';

        }*/

        $currentPage = $data->currentPage();
        $perPage = $data->perPage();
        $totalRecord = $data->total();
        $totalPage = $data->lastPage();
        $paginate = [
            'currentPage' => $currentPage,
            'perPage' => $perPage,
            'totalRecord' => $totalRecord,
            'totalPage' => $totalPage
        ];  
        
        $compact = compact('bills', 'paginate');

        return responseSuccess($compact);
    }
}
