<?php

namespace App\Http\Controllers\Api\v1\Company;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\ProjectProgressThreeModel;
use App\Models\CompanyModel;

class ProgressThreeController extends Controller
{
    private $request;
    private $projectProgressThreeModel;
    private $companyModel;

    public function __construct(Request $request, ProjectProgressThreeModel $projectProgressThreeModel, CompanyModel $companyModel)
    {
        $this->request = $request;
        $this->projectProgressThreeModel = $projectProgressThreeModel;
        $this->companyModel = $companyModel;
    }
    
    /**
     * Method 获取进度三信息
     *
     * @return void
     */
    public function getProgressThreeInfo()
    {
        $companyID = $this->request->input('companyID');
        $user = $this->request->attributes->get('user');

        //校验公司
        $company = $this->companyModel->verificationUserCompany($companyID, $user['profileID']);

        //校验状态，若状态非3、4则禁止访问
        if ($company->companyProgressState != 3 && $company->companyProgressState != 4) {
            return responseFail();
        }

        //进度三信息
        $progressThree = $this->projectProgressThreeModel->getProgressThreeInfo($companyID, $user['profileID']);

        //整合数据
        $username = null;
        $password = null;
        $loginState = null;
        $url = 'https://www.greensmartplanet.cn/account';
        if ($progressThree) {
            $username = $progressThree->username;
            $password = $progressThree->password;
            $loginState = $progressThree->loginState;
        }
        $compact = compact('username', 'password', 'loginState', 'url');
        
        return responseSuccess($compact);
    }
}
