<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CurrencyRatesModel extends Model
{
    use HasFactory;

    protected $table = 'cna_currency_rates';

    protected $fillable = [
        'currency',
        'currency_code',
        'rate',
        'is_default'
    ];
    
    protected $appends = ['default_remark'];

    const IS_DEFAULT = 1;
    const NOT_DEFAULT = 0;

    const DEFAULT_REMARK = [
        self::IS_DEFAULT => '默认货币',
        self::NOT_DEFAULT => '可选货币',
    ];

    public function getDefaultRemarkAttribute()
    {
        return self::DEFAULT_REMARK[$this->is_default] ?? '';
    }

    public function logs()
    {
        return $this->hasMany(CurrencyRateLog::class, 'currency_id');
    }
}
