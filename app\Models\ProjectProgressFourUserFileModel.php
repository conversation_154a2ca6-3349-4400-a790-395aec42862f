<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProjectProgressFourUserFileModel extends Model
{
    use HasFactory;

    protected $table = 'cna_project_progress_four_user_files';
    protected $guarded = [];

    public function files()
    {
        return $this->belongsTo(ProjectProgressFourFileModel::class, 'fileID', 'id');
    }
    
    /**
     * Method 获取进度四信息
     *
     * @param $companyID $companyID [explicite description]
     * @param $userID $userID [explicite description]
     *
     * @return void
     */
    public function getProgressFourInfo($companyID, $userID)
    {
        return self::where('companyID', $companyID)->where('profileID', $userID)->get();
    }
    
    /**
     * Method 获取用户上传的文件ID列表
     *
     * @param $companyID $companyID [explicite description]
     * @param $userID $userID [explicite description]
     *
     * @return void
     */
    public function getUserUploadFileIDs($companyID, $userID)
    {
        return self::where('companyID', $companyID)->where('profileID', $userID)->pluck('fileID')->toArray();
    }

    /**
     * Method 获取单条记录
     *
     * @param $fileID $fileID [文件id]
     * @param $profileID $profileID [用户id]
     * @param $companyID $companyID [企业id]
     *
     * @return void
     */
    public function getRecord($fileID, $profileID, $companyID)
    {
        return self::where('fileID', $fileID)
                    ->where('profileID', $profileID)
                    ->where('companyID', $companyID)
                    ->first();
    }
}
