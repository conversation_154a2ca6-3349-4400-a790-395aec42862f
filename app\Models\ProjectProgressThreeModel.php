<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProjectProgressThreeModel extends Model
{
    use HasFactory;

    protected $table = 'cna_project_progress_three';
    protected $guarded = [];
    
    /**
     * Method 获取项目进度三信息
     *
     * @param $companyID $companyID [explicite description]
     * @param $userID $userID [explicite description]
     *
     * @return void
     */
    public function getProgressThreeInfo($companyID, $userID)
    {
        return self::where('companyID', $companyID)->where('profileID', $userID)->first();
    }
}
