<?php

namespace App\Services;

use Intervention\Image\ImageManagerStatic as Image;

class PdfService
{
    private $publicBase;
    private $moduleBase;
    private $fontColor;
    private $fontSize;
    private $companyNamePosition;
    private $companyRegisterCodePosition;

    public function __construct()
    {
        $this->publicBase = storage_path('app/public/files/project/progressOne/temp/'); //公共目录
        $this->moduleBase = storage_path('app/public/files/project/progressOne/'); //模块目录
        $this->fontColor  = '#3A3A3A'; //字体颜色
        $this->fontSize   = 24; //字体大小
        $this->companyNamePosition = [148, 910]; //企业名称位置
        $this->companyRegisterCodePosition = [1380, 910]; //企业注册号码位置
    }
    
    /**
     * Method 创建预审表格
     *
     * @param $companyName $companyName [explicite description]
     * @param $companyRegisterCode $companyRegisterCode [explicite description]
     * @param $companyCategory $companyCategory [explicite description]
     *
     * @return void
     */
    public function createForm($companyName, $companyRegisterCode, $companyCategory)
    {
        $checkedIconPath = $this->moduleBase.'checked-icon.png'; //checkbox选中样式icon
        $formImagePath = $this->moduleBase.'form.png'; //预审表格图片
        $fangSongFontPath = $this->moduleBase.'fangsong.ttf'; //仿宋字体

        $image = Image::make($formImagePath); // 加载图像
        $checkedIcon = Image::make($checkedIconPath); //加载插入选中icon图像
        
        //设置预审目标企业名称
        $image->text($companyName, $this->companyNamePosition[0], $this->companyNamePosition[1], function ($font) use ($fangSongFontPath) {
            $font->file($fangSongFontPath);
            $font->size($this->fontSize);
            $font->color($this->fontColor);
        });

        //设置注册号码
        $image->text($companyRegisterCode, $this->companyRegisterCodePosition[0], $this->companyRegisterCodePosition[1], function ($font) use ($fangSongFontPath) {
            $font->file($fangSongFontPath);
            $font->size($this->fontSize);
            $font->color($this->fontColor);
        });

        //插入选中样式icon
        $formImageTempPath = $this->createImagePath(); //pdf临时图片，转为pdf后删除 

        if ($companyCategory == 1) {
            $position = [155, 990];
        } elseif ($companyCategory == 2) {
            $position = [273, 990];
        } elseif ($companyCategory == 3) {
            $position = [412, 990];
        } elseif ($companyCategory == 4) {
            $position = [550, 990];
        }
        $image->insert($checkedIcon, 'top-left', $position[0], $position[1]);
        $image->save($formImageTempPath);

        //创建pdf实例
        $pdf = new \FPDF();

        //创建页面
        $pdf->AddPage();

        //设置pdf内容位置
        $pdf->Image($formImageTempPath, 0, 0, 210);

        //下载pdf
        $pdfPath = $this->createPdfPath();
        $pdf->Output('F', $pdfPath['fullPath']); 

        //删除pdf临时图片
        @unlink($formImageTempPath);

        return $pdfPath['subPath'];
    }
    
    /**
     * Method 创建图片资源路径
     *
     * @return void
     */
    private function createImagePath():string
    {
        $filename = md5(uniqid().microtime(true).'images').'.png';
        return $this->publicBase.$filename;
    }
    
    /**
     * Method 创建pdf资源路径
     *
     * @return void
     */
    private function createPdfPath()
    {
        $filename = md5(uniqid().microtime(true).'pdf').'.pdf';
        $fullPath = $this->moduleBase.'download/'.$filename;
        $subPath = 'files/project/progressOne/download/'.$filename;
        return compact('fullPath', 'subPath');
    }
}