<?php

namespace App\Http\Controllers\Api\v1\Config;

use App\Http\Controllers\Controller;
use App\Models\BankModel;
use App\Models\CurrencyModel;
use App\Models\DateModel;
use App\Models\GlobalLanguagesModel;
use App\Models\LanguageModel;
use App\Models\ProfessionModel;
use App\Models\TimeModel;
use App\Models\TimezoneModel;
use Illuminate\Support\Facades\Cache;
use App\Models\StructureModel;
use App\Models\CountryModel;
use App\Models\Industry;
use App\Services\AddressService;
use Illuminate\Http\Request;

class ConfigController extends Controller
{

    public function redisDriver()
    {
        $redis = Cache::store('redis');
        return $redis;
    }

    public function configurations()
    {
        $languageModel = new LanguageModel;
        $timezoneModel = new TimezoneModel;
        $currencyModel = new CurrencyModel;
        $dateModel = new DateModel;
        $timeModel = new TimeModel;

        $languageList = $languageModel->getData();
        $timezoneList = $timezoneModel->getData(); 
        $currencyList = $currencyModel->getData(); 
        $dateFormatList = $dateModel->getData();
        $timeFormatList = $timeModel->getData();
        $countryID = env('COUNTRY');
        $allLanguages = $languageModel->getAll();

        $data = compact('languageList','timezoneList', 'currencyList', 'dateFormatList',
            'timeFormatList', 'countryID', 'allLanguages');

        return responseSuccess($data);
    }

    public function structure()
    {
        $data = StructureModel::select(
            'structureCode',
            'structureEN',
            'structureMS',
            'structureZH',
            'structureZT'
        )->get();

        return responseSuccess($data);
    }

    public function countryList()
    {
        $data = CountryModel::select('countryID', 'countryZH')->get();
        return responseSuccess($data);
    }

    public function mobilePrefixList()
    {
        $data = CountryModel::select('countryID AS prefixID', 'countryZH AS country', 'countryCode AS prefixCode', 'countryISOCode2')
                // 国家的电话代码 暂时只需要中国，香港，马来西亚，新加坡，英国        
                ->whereIn('countryZH', ['中国', '香港', '马来西亚', '新加坡', '英国'])
                ->get();
        return responseSuccess($data);
    }

    public function bankList()
    {
        $data = BankModel::select('ID', 'bankName', 'bankCountry', 'bankSwiftCode')->get();
        return responseSuccess($data);
    }

    public function countries()
    {
        $data = CountryModel::select('countryID', 'countryEN', 'countryMS', 'countryZH', 'countryZT', 'countryCode', 'countryISOCode2')->get();
        return responseSuccess($data);
    }

    public function divisionCnList()
    {
        return responseSuccess(AddressService::getDivisionCn());
    }

    public function industryList()
    {
        $data = Industry::get();
        return responseSuccess($data);
    }

    public function globalLanguages()
    {
        $data = GlobalLanguagesModel::get();
        return responseSuccess($data);
    }

    /**
     * 职业列表
     * @return void
     */
    public function professionList(Request $request)
    {
        $keyword = $request->get('keyword');
        $data = ProfessionModel::query()->when($keyword != '', function ($query) use ($keyword) {
            $query->orWhere('nameEN', 'like', '%' . $keyword . '%')
                ->orWhere('nameMS', 'like', '%' . $keyword . '%')
                ->orWhere('nameZH', 'like', '%' . $keyword . '%')
                ->orWhere('nameZT', 'like', '%' . $keyword . '%');
        })->orderByDesc('order')->get();
        if ($data) {
            foreach ($data as $key => $val) {
                $val['image'] = $val->image ? storageUrl($val->image) : null;
                $data[$key] = $val;
            }
        }
        return responseSuccess($data);
    }
}
