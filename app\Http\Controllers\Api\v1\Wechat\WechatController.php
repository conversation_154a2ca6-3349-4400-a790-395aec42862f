<?php

namespace App\Http\Controllers\Api\v1\Wechat;

use App\Events\UserPaid;
use App\Http\Controllers\Controller;
use App\Models\PaymentModel;
use App\Models\ProfileInfoModel;
use App\Models\ProjectCategoryModel;
use App\Models\ProjectServiceModel;
use App\Models\TeamRank;
use Illuminate\Http\Request;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use WeChatPay\Builder;
use WeChatPay\Crypto\Rsa;
use WeChatPay\Util\PemUtil;
use App\Services\Wechat\AesUtil;
use App\Services\Wechat\WechatService;
use App\Models\WechatOrderModel;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\DB;
use App\Models\WechatUserModel;
use App\Services\EmailService;


class WechatController extends Controller
{
    private $request;
    private $profileInfoModel;
    private $wechatService;
    private $wechatOrderModel;
    private $emailService;

    public function __construct(Request $request, ProfileInfoModel $profileInfoModel, WechatService $wechatService, WechatOrderModel $wechatOrderModel, EmailService $emailService)
    {
        $this->request = $request;
        $this->profileInfoModel = $profileInfoModel;
        $this->wechatService = $wechatService;
        $this->wechatOrderModel = $wechatOrderModel;
        $this->emailService = $emailService;
    }

    public function transaction()
    {
        $token = $this->request->input('token', '');
        if (!$token) {
            return responseFail();
        }
        
        try {
            $decodeToken = JWT::decode($token, new Key(env('JWT_KEY'), 'HS256'));
            if (!isset($decodeToken->user_id) || !$decodeToken->user_id) {
                return responseFail();
            }
            if (!in_array($decodeToken->action, [ACTION_PROFILE_REGISTER, ACTION_PROFILE_EXPIRE])) {
                return responseFail();
            }
            $user = $this->profileInfoModel::where('profileID', $decodeToken->user_id)->first();
            $payState = ($user->status == ProfileInfoModel::STATUS_PAYING) ? false : true;
            $res = ['payment_state' => $payState];
            if ($payState) {
                //生成token
                $action = ACTION_PROFILE_AUTH;
                $payload = [
                    'exp' => time() + (3600 * 24 * 7),
                    'iat' => time(),
                    'action' => $action,
                    'user_id' => $user['profileID'],
                ];
                $token = JWT::encode($payload, env('JWT_KEY'), 'HS256');
                setcookie(
                    "Authorization",
                    "Bearer $token",
                    time() + 3600 * 24 * 7,
                    "/"
                );
                $res['token'] = 'Bearer ' . $token;
                $res['register_setting'] = ProfileInfoModel::initialRes($user);
            }
            return responseSuccess($res);
        } catch (\Exception $e) {
            Log::error('Wechat_transaction: ' . $e->getMessage());
            return responseFail();
        }
    }

    public function wechatNative()
    {
        $nativePayToken = $this->request->input('token', '');
      	$decodeNativePayData = $this->wechatService->decodeNativePayData($nativePayToken);
      	$total = env('WECHAT_FEE') * 100;
        $merchantPrivateKeyFilePath = file_get_contents(env('WECHAT_PRIVATE_KEY_PATH'));
        $merchantPrivateKeyInstance = Rsa::from($merchantPrivateKeyFilePath, Rsa::KEY_TYPE_PRIVATE);
        $platformCertificateFilePath = file_get_contents(env('WECHAT_CERT_PATH'));
        $platformPublicKeyInstance = Rsa::from($platformCertificateFilePath, Rsa::KEY_TYPE_PUBLIC);

        $platformCertificateSerial = PemUtil::parseCertificateSerialNo($platformCertificateFilePath);

        $instance = Builder::factory([
            'mchid'      => config('wechat.mchid'),
            'serial'     => config('wechat.serial'),
            'privateKey' => $merchantPrivateKeyInstance,
            'certs'      => [
                $platformCertificateSerial => $platformPublicKeyInstance,
            ]
        ]);

        try {

            $outTradeNo = $this->wechatService->generateWechatOutTradeNo();
          
          	//创建订单
          	$generateOrderRes = $this->wechatOrderModel->generateOrder([
            	'out_trade_no' => $outTradeNo,
              	'total' => $total,
              	'user_id' => $decodeNativePayData->user_id
            ]);
          	if (!$generateOrderRes) {
            	return responseSuccess('GENERATE_ORDER_ERROR');
            }
            
            $resp = $instance
                ->chain('v3/pay/transactions/native')
                ->post(['json' => [
                    'mchid'        => config('wechat.mchid'),
                    'out_trade_no' => $outTradeNo,
                    'appid'        => config('wechat.app_id'),
                    'description'  => config('wechat.description'),
                    'notify_url'   => env('WECHAT_NOTIFY_URL'),
                    'amount'       => [
                        'total'    => $total,
                        'currency' => 'CNY'
                    ],
                ]]);

            $data = json_decode($resp->getBody()->getContents(), true);
            $img = \SimpleSoftwareIO\QrCode\Facades\QrCode::format('png')->size(150)->generate($data['code_url']);
            $img = 'data:image/png;base64,' . base64_encode($img);

          	return responseSuccess(['qrcode' => $img]);

        } catch (\Exception $e) {
          	//return responseFail('PAY ERROR');
          	return responseFail('line:'.$e->getLine().' '.$e->getMessage());
        }
    }

    public function wechatJSApi()
    {
        try {

            //校验token
            $token = $this->request->input('token', '');
            if (!$token) {
                return responseFail('token not exist');
            }

            //提取token中的user_id
            $decodeToken = JWT::decode($token, new Key(env('JWT_KEY'), 'HS256'));
            if (!isset($decodeToken->action)) {
                return responseFail('action not exist');
            } elseif ($decodeToken->action != ACTION_PROFILE_REGISTER) {
                return responseFail('action error');
            }
            $userId = $decodeToken->user_id;
            $openid = WechatUserModel::where('wechatUserPlatformId', $userId)->value('wechatUserOpenid');
            $total = env('WECHAT_FEE') * 100;
            $merchantPrivateKeyFilePath = file_get_contents(env('WECHAT_PRIVATE_KEY_PATH'));
            $merchantPrivateKeyInstance = Rsa::from($merchantPrivateKeyFilePath, Rsa::KEY_TYPE_PRIVATE);
            $platformCertificateFilePath = file_get_contents(env('WECHAT_CERT_PATH'));
            $platformPublicKeyInstance = Rsa::from($platformCertificateFilePath, Rsa::KEY_TYPE_PUBLIC);

            $platformCertificateSerial = PemUtil::parseCertificateSerialNo($platformCertificateFilePath);

            $instance = Builder::factory([
                'mchid'      => config('wechat.mchid'),
                'serial'     => config('wechat.serial'),
                'privateKey' => $merchantPrivateKeyInstance,
                'certs'      => [
                    $platformCertificateSerial => $platformPublicKeyInstance,
                ]
            ]);

            $outTradeNo = $this->wechatService->generateWechatOutTradeNo();
          
          	//创建订单
          	$generateOrderRes = $this->wechatOrderModel->generateOrder([
            	'out_trade_no' => $outTradeNo,
              	'total' => $total,
              	'user_id' => $userId,
                // 'user_id' => 1
            ]);
          	if (!$generateOrderRes) {
            	return responseSuccess('GENERATE_ORDER_ERROR');
            }
            
            $resp = $instance
                ->chain('v3/pay/transactions/jsapi')
                ->post(['json' => [
                    'mchid'        => config('wechat.mchid'),
                    'out_trade_no' => $outTradeNo,
                    'appid'        => config('wechat.app_id'),
                    'description'  => config('wechat.description'),
                    'notify_url'   => env('WECHAT_NOTIFY_URL'),
                    'amount'       => [
                        'total'    => $total,
                        'currency' => 'CNY'
                    ],
                    'payer'        => [
                        'openid' => $openid
                    ]
                ]]);

            $data = json_decode($resp->getBody()->getContents(), true);
            $signature = $this->wechatService->generateJSApiSignature($data['prepay_id']);

          	return responseSuccess($signature);

        } catch (\Exception $e) {
          	return responseFail('line:'.$e->getLine().' '.$e->getMessage());
        }
    }

    public function notify()
    {
        $response = file_get_contents("php://input");
        $apiv3 = 'A1b2C3d4E5f6G7h8I9j0K1l2M3n4O5p6';
      
        if ($response) {
            $json = json_decode($response, true);
            if (isset($json['event_type']) && $json['event_type'] === 'TRANSACTION.SUCCESS') {
                if (isset($json['resource']) && $json['resource']) {
                    $ciphertext = $json['resource']['ciphertext'];
                    $associatedData = $json['resource']['associated_data'];
                    $nonce = $json['resource']['nonce'];
                    $aes = new AesUtil($apiv3);
                    $data = $aes->decryptToString($associatedData, $nonce, $ciphertext);
                  	$data = json_decode($data, true);
                    if ($data) {
                        if ($data['trade_state'] == 'SUCCESS') {
                            try {
                                $order = $this->wechatOrderModel->getOrderByOutTradeNo($data['out_trade_no']);
                                if ($order) {
                                    $order->mchid = $data['mchid'];
                                    $order->appid = $data['appid'];
                                    $order->out_trade_no = $data['out_trade_no'];
                                    $order->transaction_id = $data['transaction_id'];
                                    $order->trade_type = $data['trade_type'];
                                    $order->trade_state = $data['trade_state'];
                                    $order->trade_state_desc = $data['trade_state_desc'];
                                    $order->bank_type = $data['bank_type'];
                                    $order->attach = $data['attach'];
                                    $order->success_time = $data['success_time'];
                                    $order->openid = $data['payer']['openid'];
                                    $order->total = $data['amount']['total'];
                                    $order->payer_total = $data['amount']['payer_total'];
                                    $order->currency = $data['amount']['currency'];
                                    $order->payer_currency = $data['amount']['payer_currency'];
                                    $order->payment_state = 1;                                
                                    $order->save();
                                    event(new UserPaid($order->user_id, $data['amount']['total']));                            
                                } else {	
                                    return response()->json(['code' => 1, 'msg' => 'order not exist']);
                                }
                            } catch (\Exception $exception) {
                                DB::rollBack();
                                Log::info($exception->getTrace());
                            }
                        }
                    }
                } else {
                    return response()->json(['code' => 1, 'msg' => 'not resource']);
                }
            } else {
                return response()->json(['code' => 1, 'msg' => 'pay fail']);
            }
        } else {
            return response()->json(['code' => 1, 'msg' => 'not response data']);
        }

    }

    /**
     * 本地测试回调,不是真实数据
     * @return \Illuminate\Http\JsonResponse|void
     */
    public function notifyTest()
    {

        $data = [
            'trade_state'   => 'SUCCESS',
            'out_trade_no'  => '65777587704786680357340531676956',
            'mchid'         => '**********',
            'appid'         => 'wx944d0f3709b4b7cd',
            'transaction_id' => '4200002437202410031571023190',
            'trade_type'    => 'NATIVE',
            'trade_state_desc' => '支付成功',
            'bank_type'     => 'OTHERS',
            'attach'        => '',
            'success_time'  => '2024-10-08T11:53:32+08:00',
            'payer'         => [
                'openid'    => ''
            ],
            'amount'        => [
                'total'     => 1,
                'payer_total' => 1,
                'currency'    => 'CNY',
                'payer_currency' => 'CNY'
            ]
        ];
        if ($data) {
            if ($data['trade_state'] == 'SUCCESS') {
                try {
                    $order = $this->wechatOrderModel->getOrderByOutTradeNo($data['out_trade_no']);

                    if ($order) {

                        DB::beginTransaction();

                        $order->mchid = $data['mchid'];
                        $order->appid = $data['appid'];
                        $order->out_trade_no = $data['out_trade_no'];
                        $order->transaction_id = $data['transaction_id'];
                        $order->trade_type = $data['trade_type'];
                        $order->trade_state = $data['trade_state'];
                        $order->trade_state_desc = $data['trade_state_desc'];
                        $order->bank_type = $data['bank_type'];
                        $order->attach = $data['attach'];
                        $order->success_time = $data['success_time'];
                        $order->openid = $data['payer']['openid'];
                        $order->total = $data['amount']['total'];
                        $order->payer_total = $data['amount']['payer_total'];
                        $order->currency = $data['amount']['currency'];
                        $order->payer_currency = $data['amount']['payer_currency'];
                        $order->payment_state = 1;
                        $order->save();


                        $user = ProfileInfoModel::where('profileID', $order->user_id)->first();
                        if ($user->status == ProfileInfoModel::STATUS_PAYING) {
                            $update = ['status' => ProfileInfoModel::STATUS_VERIFYING];
                            ProfileInfoModel::where('profileID', $order->user_id)->update($update);
                            if (empty($user['profilePassword'])) {
                                //生成初始密码
                                $password = generateRandomString(8);
                                $hashPsw = hashPasswordNoKey($password);
                                $user->profilePassword = ProfileInfoModel::INITIAL_PASSWORD_PREFIX . hashPassword($hashPsw);
                                $user->save();
                                // 获取邮箱模板
                                $templateData  = $this->emailService->getTemplateData('', 'InitialPassword', $order->user_id);
                                $template = $templateData['htmlBody'];
                                $subject = $templateData['subject'];
                                $body = str_replace('{{password}}', $password, $template);
                                $this->emailService->sendEmail($user['profileEmail'], $subject, $body);
                            }


                            // 加盟费用(已付)
                            $projectId = ProjectCategoryModel::query()->where('type', 1)->value('projectCategoriesID');
                            $projectDetail = ProjectServiceModel::query()
                                ->where('projectId', $projectId)
                                ->where('remark', 'register.partner.fees')->first();


                            $total =  bcdiv($data['amount']['total'], 100, 2);
                            PaymentModel::query()->create([
                                'profileID'       => $user['profileID'],
                                'companyID'       => 0,
                                'type'            => 2,
                                'projectId'       => $projectId,  // 合伙人注册加盟
                                'detailId'        => $projectDetail['id'],  // 合伙人加盟申请费
                                'fee'             => $total,  // 金额
                                'paytime'         => date('Y-m-d H:i:s'),
                                'createtime'      => date('Y-m-d H:i:s'),
                            ]);

                            // 合伙人加盟注册其它待付项目明细
                            $noPayProjectDetail = ProjectServiceModel::query()
                                ->where('projectId', $projectId)
                                ->where('remark', '<>','register.partner.fees')->get();
                            if ($noPayProjectDetail) {
                                foreach ($noPayProjectDetail as $projectDetailVal) {
                                    PaymentModel::query()->create([
                                        'profileID'       => $user['profileID'],
                                        'companyID'       => 0,
                                        'type'            => 2,
                                        'projectId'       => $projectId,  // 合伙人注册加盟
                                        'detailId'        => $projectDetailVal['id'],  // 合伙人加盟申请费
                                        'fee'             => $projectDetailVal['price'],  // 金额
                                        'createtime'      => date('Y-m-d H:i:s'),
                                    ]);

                                }
                            }


                            // 查询是否是管理合伙人带进来
                            $pre_id = $user['pre_id'];
                            $last_time = strtotime('2024-12-31 23:59:59');
                            if ($pre_id > 0 && time() <= $last_time) { // 上级管理合伙人在12月31号前成功拉了一位合伙人,则免其它三张费用
                                PaymentModel::query()->where('profileID', $pre_id)
                                    ->where('companyID', 0)
                                    ->where('projectId', $projectId)
                                    ->whereNull('paytime')
                                    ->update([
                                        'paytime'      => date('Y-m-d H:i:s'),
                                    ]);
                            }

                            // 通知上级加盟成功
                            if ($pre_id > 0) {
                                // 发通知
                                $this->preNotification($pre_id, $user['profileName']);
                                // 发邮件
                                $this->preEmail($pre_id, $user['profileName']);
                            }


                            $user->uuid = Str::uuid()->toString();
                            $user->profileRole = 1;
                            $user->profilePartnerCode = $this->profileInfoModel->createPartnerCode($user['profileAddressCountry'], 1);
                            $user->save();


                        } else if ($user->status == ProfileInfoModel::STATUS_EXPIRE) {
                            ProfileInfoModel::where('profileID', $order->user_id)
                                ->update(['status' => ProfileInfoModel::STATUS_ACTIVE]);
                        }

                        // 站内通知
                        $data = [
                            'targetUser' =>  $user['profileID'],
                        ];
                        \App\Jobs\SendNotice::dispatch($data, 'PartnerPass', $user)->onQueue('SendNoticeJob');

                        DB::commit();

                    } else {
                        return response()->json(['code' => 1, 'msg' => 'order not exist']);
                    }
                } catch (\Exception $exception) {
                    DB::rollBack();
                    Log::info($exception->getMessage());
                }
            }
        }

    }

    /**
     * 管理合伙人和三三制入驻成功通知
     * @return void
     */
    public function preNotification($pre_id, $profileName)
    {
        $preUser = ProfileInfoModel::where('profileID', $pre_id)->first();
        $count = ProfileInfoModel::where('pre_id', $pre_id)->whereIn('status', [ProfileInfoModel::STATUS_VERIFYING, ProfileInfoModel::STATUS_ACTIVE])->count();

        // 查看上级身份
        $isTeam = ProfileInfoModel::getGroupById($pre_id);
        if ($isTeam == 3) { // 三三制

            // 下级等级
            $team_rank = $preUser['team_rank']-1;
            // 通知一：三三制加盟通知
            $data = [
                'targetUser'  =>  $pre_id,
                'profileName' =>  $profileName,
                'teamName'    =>  TeamRank::where('id', $team_rank)->value('job')
            ];
            \App\Jobs\SendNotice::dispatch($data, 'ChildRegister', $preUser)->onQueue('SendNoticeJob');

            if ($count == 3) { // 三三制完成KPI
                $data = [
                    'targetUser'  =>  $pre_id,
                    'teamName'    =>  TeamRank::where('id', $preUser['team_rank'])->value('job')
                ];
                \App\Jobs\SendNotice::dispatch($data, 'ChildSuccessKPI', $preUser)->onQueue('SendNoticeJob');
            }
        } else if ($isTeam == 2) { // 管理合伙人

            // 通知一：合伙人加盟通知
            $data = [
                'targetUser' =>  $pre_id,
                'profileName' =>  $profileName
            ];
            \App\Jobs\SendNotice::dispatch($data, 'ManagePartner2', $preUser)->onQueue('SendNoticeJob');

        }


        // 通知二：合伙人完成KPI
        if ($count == 1) { // 第一个进来
            $data = [
                'targetUser' =>  $pre_id,
            ];
            \App\Jobs\SendNotice::dispatch($data, 'ManageSuccessKPI', $preUser)->onQueue('SendNoticeJob');

        }

    }

    /**
     * 管理合伙人和三三制入驻成功发邮件
     * @return void
     */
    public function preEmail($pre_id, $profileName)
    {
        $preUser = ProfileInfoModel::where('profileID', $pre_id)->first();
        $count = ProfileInfoModel::where('pre_id', $pre_id)->whereIn('status', [ProfileInfoModel::STATUS_VERIFYING, ProfileInfoModel::STATUS_ACTIVE])->count();

        // 查看上级身份
        $isTeam = ProfileInfoModel::getGroupById($pre_id);
        if ($isTeam == 3) { // 三三制

            // 下级等级
            $team_rank = $preUser['team_rank']-1;

            // 邮件一：三三制加盟通知
            $teamName = TeamRank::where('id', $team_rank)->value('job');
            // 获取邮箱模板
            $templateData  = $this->emailService->getTemplateData('', 'ChildRegister', $pre_id);
            $template = $templateData['htmlBody'];
            $subject = $templateData['subject'];
            $body = str_replace('{{teamName}}', $teamName, $template);
            $body = str_replace('{{profileName}}', $profileName, $body);
            $this->emailService->sendEmail($preUser['profileEmail'], $subject, $body);

            if ($count == 3) {
                // 邮件二：三三制完成KPI
                $teamName = TeamRank::where('id', $preUser['team_rank'])->value('job');
                $templateData  = $this->emailService->getTemplateData('', 'ChildSuccessKPI', $pre_id);
                $template = $templateData['htmlBody'];
                $subject = $templateData['subject'];
                $body = str_replace('{{teamName}}', $teamName, $template);
                $this->emailService->sendEmail($preUser['profileEmail'], $subject, $body);

            }
        } else if ($isTeam == 2) { // 管理合伙人

            // 邮件一：合伙人加盟通知
            $templateData  = $this->emailService->getTemplateData('', 'ManagePartner2', $pre_id);
            $template = $templateData['htmlBody'];
            $subject = $templateData['subject'];
            $body = str_replace('{{profileName}}', $profileName, $template);
            $this->emailService->sendEmail($preUser['profileEmail'], $subject, $body);
        }


        // 邮件二：合伙人完成KPI
        if ($count == 1) { // 第一个进来

            $templateData  = $this->emailService->getTemplateData('', 'ManageSuccessKPI', $pre_id);
            $template = $templateData['htmlBody'];
            $subject = $templateData['subject'];
            $this->emailService->sendEmail($preUser['profileEmail'], $subject, $template);

        }

    }

    /**
     * Method 微信网页授权
     *
     * @return void
     */
    public function oauth()
    {
        try {
            $code = $this->request->input('code', '');
            $state = $this->request->input('state', '');

            //校验code参数
            if (!$code) {
                return responseFail(__('missing parameter', ['param' => 'code']));
            }

            //检验state
            if (!$state) {
                return responseFail('state not exist');
            }

            //提取token中的user_id
            $decodeToken = JWT::decode($state, new Key(env('JWT_KEY'), 'HS256'));
            if (!isset($decodeToken->action)) {
                return responseFail('action not exist');
            } elseif ($decodeToken->action != ACTION_PROFILE_REGISTER) {
                return responseFail('action error');
            }
            $userId = $decodeToken->user_id;
            
            //换取网页授权access_token
            $requestData = [
                'appid' => config('wechat.app_id'),
                'secret' => config('wechat.app_secret'),
                'code' => $code,
                'grant_type' => 'authorization_code'
            ];
            $query = http_build_query($requestData);
            $url = 'https://api.weixin.qq.com/sns/oauth2/access_token?'.$query;    
            $client = new Client();
            $response = $client->get($url);
            $content = $response->getBody()->getContents();
            $content = json_decode($content, true);

            //网页授权错误处理
            if (isset($content['errcode'])) {
                return responseFail($content['errmsg']);
            }
            
            $accessToken = $content['access_token'];
            $openId = $content['openid'];

            //获取微信用户信息
            $requestData = [
                'access_token' => $accessToken,
                'openid' => $openId,
                'lang' => 'zh_CN'
            ];
            $query = http_build_query($requestData);
            $url = 'https://api.weixin.qq.com/sns/userinfo?'.$query;
            $response = $client->get($url);
            $content = $response->getBody()->getContents();
            $content = json_decode($content, true);

            //微信用户信息存入数据库
            $updateData = [
                'wechatUserOpenid' => $content['openid'],
                'wechatUserAccessToken' => $accessToken,
                'wechatUserExpireTime' => time() + 7200,
                'wechatUserNickname' => $content['nickname'],
                'wechatUserSex' => $content['sex'],
                'wechatUserProvince' => $content['province'],
                'wechatUserCity' => $content['city'],
                'wechatUserCountry' => $content['country'],
                'wechatUserHeadimgurl' => $content['headimgurl'],
                'wechatUserPlatformId' => $userId
            ];
            WechatUserModel::updateOrCreate(['wechatUserOpenid' => $content['openid']], $updateData);

            return responseSuccess(['openid' => $content['openid']]);

        } catch (\Exception $e) {
            return responseFail($e->getMessage());
        }
    }

    /**
     * 微信 H5 下单
     * https://pay.weixin.qq.com/docs/merchant/apis/h5-payment/direct-jsons/h5-prepay.html
     * https://pay.weixin.qq.com/docs/merchant/apis/h5-payment/h5-transfer-payment.html
     */
    public function h5()
    {
        $nativePayToken = $this->request->input('token', '');
        $decodeNativePayData = $this->wechatService->decodeNativePayData($nativePayToken);
        $merchantPrivateKeyFilePath = file_get_contents(config('wechat.merchant_private_key_filepath'));
        $merchantPrivateKeyInstance = Rsa::from($merchantPrivateKeyFilePath, Rsa::KEY_TYPE_PRIVATE);
        $platformCertificateFilePath = file_get_contents(config('wechat.platform_certificate_filepath'));
        $platformPublicKeyInstance = Rsa::from($platformCertificateFilePath, Rsa::KEY_TYPE_PUBLIC);
        $platformCertificateSerial = PemUtil::parseCertificateSerialNo($platformCertificateFilePath);
        $instance = Builder::factory([
            'mchid'      => config('wechat.mchid'),
            'serial'     => config('wechat.serial'),
            'privateKey' => $merchantPrivateKeyInstance,
            'certs'      => [
                $platformCertificateSerial => $platformPublicKeyInstance,
            ]
        ]);

        $total = env('WECHAT_FEE') * 100;
        $outTradeNo = $this->wechatService->generateWechatOutTradeNo();

        try {
            //创建订单
            $generateOrderRes = $this->wechatOrderModel->generateOrder([
                'out_trade_no' => $outTradeNo,
                'total' => $total,
                'user_id' => $decodeNativePayData->user_id
            ]);
            if (!$generateOrderRes) {
                return responseFail(__('order failed'));
            }

            $resp = $instance
                ->chain('v3/pay/transactions/h5')
                ->post(['json' => [
                    'mchid'        => config('wechat.mchid'),
                    'out_trade_no' => $outTradeNo,
                    'appid'        => config('wechat.app_id'),
                    'description'  => config('wechat.description'),
                    'notify_url'   => env('WECHAT_NOTIFY_URL'),
                    'amount'       => [
                        'total'    => $total,
                        'currency' => 'CNY'
                    ],
                    'scene_info' => [
                        'payer_client_ip' => $this->request->getClientIp(),
                        'h5_info' => [
                            'type' => 'H5',
                        ],
                    ],
                ]]);

            $data = json_decode($resp->getBody()->getContents(), true);
            return responseSuccess($data);
        } catch (\Exception $e) {
            return responseFail('line:' . $e->getLine() . ' ' . $e->getMessage());
        }
    }

    public function checkRegisterOrder()
    {
        $profileId = $this->request->get('profileID');
        if (!$profileId || !is_numeric($profileId) || $profileId < 0) {
            return responseFail();
        }
        WechatOrderModel::checkRegisterOrder($profileId);
        return responseSuccess();
    }
}
