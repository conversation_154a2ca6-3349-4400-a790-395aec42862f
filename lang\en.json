{"param error": ":param error", "please upload complete file": "please upload complete file", "captcha code error": "captcha code error", "account or password error": "account or password error", "login not pay": "To ensure the smooth progress of the application review, please complete the payment as soon as possible.", "login review ing": "Your partner's application has not been reviewed yet, please be patient. The audit is expected to take 1-3 working days, please pay attention to email notifications during this period.", "login reject": "Your partner's application has not been approved, please be patient and wait for the refund to be received.", "login success": "login success", "old password error": "old password error", "register already active": "Your account has been activated, please log in to access your account", "confirm new password": "please confirm your new password", "team phone exist": "This phone number has already been registered, and you cannot use it again for registration.", "team email exist": "This email address has already been registered, and you cannot use it again for registration.", "team phone/email exist": "This phone number or email address has already been registered, and you cannot use it again for registration.", "team phone error": "phone number error", "team son out of limit": "The account has exceeded the limit of 3 people, and you cannot continue to add new members or register.", "team recommend not found": "The recommender does not exist", "incorrect format img": "Please upload images in the correct format", "exceed size img": "Please upload a picture within :limit size", "image upload failed": "image upload failed", "please select professional qualification": "please choose a professional qualification", "please select personal skills": "please select personal skills", "missing parameter": "missing :param parameter", "order failed": "order failed", "please upload file": "please upload file", "incorrect format file": "Please upload file in the correct format", "exceed size file": "The file size cannot exceed :limit", "operation failed": "operation failed", "please select value added services": "please select value added services", "please upload pre review form": "please upload pre review form", "please upload the pre review form in PDF format": "please upload the pre review form in PDF format", "please select bank": "please select bank", "please select payment method": "please select payment method", "please fill in the payment number": "please fill in the payment number", "please upload payment proof": "please upload payment proof", "please select project": "please select project", "please fill in the company name": "please fill in the company name", "please fill in the enterprise registration number": "please fill in the enterprise registration number", "please select company category": "please select company category", "please select country": "please select country", "please fill in the correct email": "please fill in the correct email", "please select phone area code": "please select phone area code", "please fill in the phone": "please fill in the phone", "please fill in the password": "please fill in the password", "missing captcha key": "missing captcha key", "please select language": "please select language", "please fill in the current password": "please fill in the current password", "please fill in the new password": "please fill in the new password", "please fill in the confirmation password": "please fill in the confirmation password", "please fill in the verification code": "please fill in the verification code", "phone number format error": "phone number format error", "email exist": "Email address already exists", "please fill in the name": "please fill in the name", "please fill in the ID number": "please fill in the ID number", "please select gender": "please select gender", "please fill in the birthdate": "please fill in the birthdate", "please fill in the unit": "please fill in the unit", "please fill in the street": "please fill in the street", "please fill in the district": "please fill in the district", "please fill in the city": "please fill in the city", "please fill in the state": "please fill in the state", "please fill in the postcode": "please fill in the postcode", "please select address - country": "please select address - country", "sms verification code error": "sms verification code error", "account error": "account error", "save failed": "Save failed", "edit failed": "Editing failed", "delete failed": "Delete failed", "info no exist": "Information does not exist", "ID number error": "ID number error", "birthdate error": "birthdate error", "birthdate does not match ID card": "birthdate does not match ID card", "postcode error": "postcode error", "benefit delete error": "Already applied and cannot be deleted temporarily", "ID card photo error": "please upload a clear and valid photo of your ID card", "incorrect format of ID card expiration date": "incorrect format of ID card expiration date", "ID card has expired": "ID card has expired", "Authorization has expired": "Login status expired, please log in again", "Authorization has logout": "The current account has been logged out, please log in again", "Authorization failed": "Authentication failed", "Authorization error": "API authentication error", "Manual": "Handbook", "Not submitted": "Not submitted for review", "Check failed": "Audit failed", "The role no allow login": "The current role does not allow login", "User not allowed log in": "User not allowed log in", "Two passwords are inconsistent": "Two passwords are inconsistent", "Validity less the current time": "Validity period is less than the current time", "data no exist": ":param Information does not exist", "passport photo error": "please upload a clear and valid photo of your passport", "passport has expired": "passport has expired", "incorrect format of passport expiration date": "incorrect format of passport expiration date", "payment remark exempt": "Exempt", "payment remark deductible": "Deductible", "payment remark exempted": "Has been exempted", "payment remark already deducted": "Already deducted", "bank card photo error": "please upload a clear and valid photo of your bank card", "bank not support": "This bank is not currently supported", "bank card has expired": "bank card has expired", "missing param": "missing param :param", "gsp register paid one": "Preliminary review deposit has been paid", "gsp register paid two": "Business account opening fee has been paid", "gsp register paid three": "AI account activation fee has been paid"}