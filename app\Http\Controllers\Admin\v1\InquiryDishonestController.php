<?php
namespace App\Http\Controllers\Admin\v1;

use App\Http\Controllers\Controller;
use App\Models\ActiveLog;
use App\Models\CompanyModel;
use App\Models\InquiryDishonestModel;
use App\Models\InquiryExecutedModel;
use App\Models\InquiryReportModel;
use Illuminate\Support\Facades\Validator;
use Symfony\Component\HttpFoundation\Request;

class InquiryDishonestController extends Controller
{

    /**
     *
     * @param Request $request
     * @return void
     */
    public function store(Request $request)
    {

        $param = $request->all();


        $result = InquiryDishonestModel::query()->create([
            'debtor_name'               => $param['debtor_name'],
            'id_number'                 => $param['id_number'],
            'legal_representative'      => $param['legal_representative'],
            'court'                     => $param['court'],
            'province'                  => $param['province'],
            'case_number'               => $param['case_number'],
            'filing_date'               => $param['filing_date'],
            'case_code'                 => $param['case_code'],
            'enforcement_agency'        => $param['enforcement_agency'],
            'obligations'               => $param['obligations'],
            'performance'               => $param['performance'],
            'behavior'                  => $param['behavior'],
            'publish_date'              => $param['publish_date'],

        ]);

        if ($result) {
            return responseSuccess();
        } else {
            return responseFail(__('save failed'));
        }
    }
}