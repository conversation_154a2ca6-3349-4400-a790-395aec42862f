<?php

namespace App\Rules\Mobile;

use App\Models\CountryModel;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class CheckMobileRule implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (env('COUNTRY') == CountryModel::COUNTRY_ID_CHINA) {
            if (!preg_match('/^1[3-9]\d{9}$/', $value)) {
                $fail(__('phone number format error'));
            }
        }
    }
}
