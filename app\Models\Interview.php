<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Interview extends Model
{
    protected $table = 'cna_interview';
    protected $guarded = [];

    //面试状态
    const STATUS_ING = 0; //面试中
    const STATUS_REVIEW = 1; //面试完成，审核中
    const STATUS_PASS = 2; //通过
    const STATUS_FAIL = 3; //未通过

    //创建面试题
    public static function add($userId)
    {
        $questionId = [];
        //选10题，5组题目每组选2题
        for ($i = 1; $i <= 5; $i++) {
            $ids = InterviewQuestionBase::where('module', $i)->pluck('id')->toArray();
            if (count($ids) <= 2) {
                $questionId = array_merge($questionId, $ids);
                continue;
            }
            $randKeys = array_rand($ids, 2);
            $questionId[] = $ids[$randKeys[0]];
            $questionId[] = $ids[$randKeys[1]];
        }
        $res = Interview::create([
            'user_id' => $userId,
            'question_id' => implode(',', $questionId),
        ]);

        return $res;
    }
}
