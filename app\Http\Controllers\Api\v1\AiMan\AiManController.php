<?php

namespace App\Http\Controllers\Api\v1\AiMan;

use App\Http\Controllers\Controller;
use App\Models\AiManFile;
use App\Models\AiManVideo;
use App\Services\OssService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class AiManController extends Controller
{
    const FILE_PATH = 'files/aiMan';
    const ACTION = 'ai_video'; //场景

    public function upload(Request $request)
    {
        $body = $request->all();
        $rules = [
            'pic' => ['file', 'mimes:jpg,jpeg,png,webp,bmp,tiff', 'max:' . (env('ALLOW_FILE_SIZE') * 1024)],
            'pic1' => ['file', 'mimes:jpg,jpeg,png,webp,bmp,tiff', 'max:' . (env('ALLOW_FILE_SIZE') * 1024)],
            'pic2' => ['file', 'mimes:jpg,jpeg,png,webp,bmp,tiff', 'max:' . (env('ALLOW_FILE_SIZE') * 1024)],
            'audio' => ['file', 'mimes:mp3,wav,flac,aac,ogg,m4a,aiff'],
            'video' => ['file', 'mimes:mp4,avi,mkv,wmv,flv,mov,webm'],
        ];
        $messages = [
            'pic.mimes' => __('incorrect format img'),
            'pic.max' => __('exceed size img', ['limit' => env('ALLOW_FILE_SIZE') . 'M']),
            'pic1.mimes' => __('incorrect format img'),
            'pic1.max' => __('exceed size img', ['limit' => env('ALLOW_FILE_SIZE') . 'M']),
            'pic2.mimes' => __('incorrect format img'),
            'pic2.max' => __('exceed size img', ['limit' => env('ALLOW_FILE_SIZE') . 'M']),
            'audio.mimes' => __('incorrect format file'),
            'video.mimes' => __('incorrect format file'),
        ];
        $validator = Validator::make($body, $rules, $messages);
        if ($validator->fails()) {
            $errors = $validator->errors()->all();
            return responseFail($errors[0]);
        }
        $pic = $request->file('pic');
        $pic1 = $request->file('pic1');
        $pic2 = $request->file('pic2');
        $audio = $request->file('audio');
        $video = $request->file('video');
        if (empty($pic) && empty($pic1) && empty($pic2) && empty($audio) && empty($video)) {
            return responseFail();
        }
        $update = [];
        if ($pic || $pic1 || $pic2) {
            if ($pic) {
                //$resource = $pic->store(self::FILE_PATH, 'public');
                $resource = OssService::upload($pic);
                $resource && $update['pic'] = $resource;
            }
            if ($pic1) {
                //$resource = $pic1->store(self::FILE_PATH, 'public');
                $resource = OssService::upload($pic1);
                $resource && $update['pic1'] = $resource;
            }
            if ($pic2) {
                //$resource = $pic2->store(self::FILE_PATH, 'public');
                $resource = OssService::upload($pic2);
                $resource && $update['pic2'] = $resource;
            }
        } else if ($audio) {
            //$resource = $audio->store(self::FILE_PATH, 'public');
            $resource = OssService::upload($audio);
            $resource && $update = ['audio' => $resource];
        } else if ($video) {
            //$resource = $video->store(self::FILE_PATH, 'public');
            $resource = OssService::upload($video);
            $resource && $update = ['video' => $resource];
        }
        if (empty($update)) {
            return responseFail();
        }
        $user = $request->attributes->get('user');
        $record = AiManFile::where('profile_id', $user['profileID'])->first();
        if ($record) {
            AiManFile::where('profile_id', $user['profileID'])->update($update);
        } else {
            $update['profile_id'] = $user['profileID'];
            AiManFile::create($update);
        }

        return responseSuccess(['path' => OssService::link($resource)]);
    }

    public function videoList(Request $request)
    {
        $user = $request->attributes->get('user');
        $data = AiManVideo::where('profile_id', $user['profileID'])->get();
        foreach ($data as &$item) {
            $item['path'] && $item['path'] = OssService::link($item['path']);
        }

        return responseSuccess($data);
    }

    public function uploadRes(Request $request)
    {
        $user = $request->attributes->get('user');
        $data = AiManFile::where('profile_id', $user['profileID'])->first();

        return responseSuccess($data);
    }

    /**
     * 删除文件
     * todo 删除实体文件
     * @param Request $request
     */
    public function deleteFile(Request $request)
    {
        $request->validate([
            'field' => 'required|string|in:pic,pic1,pic2,audio,video',
        ]);

        $user = $request->attributes->get('user');

        $existedAiFiles = AiManFile::where('profile_id', $user['profileID'])->firstOrFail();

        $existedAiFiles->{$request->input('field')} = '';
        $existedAiFiles->save();

        return responseSuccess();
    }
}
