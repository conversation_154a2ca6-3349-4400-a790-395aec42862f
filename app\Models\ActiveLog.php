<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ActiveLog extends Model
{
    protected $table = 'cna_active_log';
    protected $guarded = [];

    const SYSTEM_CNA = 1; //合伙人平台
    const SYSTEM_CNA_ADMIN = 2; //合伙人行政后台
    const ACTIVE_CREATE = 1; //操作类型。添加
    const ACTIVE_UPDATE = 2; //操作类型。修改
    const ACTIVE_DELETE = 3; //操作类型。删除
    //操作目标
    const API_V1_CALENDAR_ADD = 1;
    const API_V1_COMPANY_CREATECOMPANY = 2;
    const API_V1_COMPANY_PROGRESSONE_COMMIT = 3;
    const API_V1_COMPANY_PROGRESSTWO_COMMIT = 4;
    const API_V1_COMPANY_PROGRESSFOUR_COMMIT = 5;
    const API_V1_COMPANY_PROGRESSFOUR_UPLOAD = 6;
    const API_V1_PASSWORD_FORGOTPSW_RESET = 7;
    const API_V1_PASSWORD_UPDATE = 8;
    const API_V1_SETTING_LANGUAGEAREA_UPDATE = 9;
    const API_V1_SETTING_NOTIFICATION_UPDATE = 10;
    const API_V1_USER_UPDATEPROFILE = 11;
    const API_V1_USER_UPDATEAVATAR = 12;
    const API_V1_USER_CHANGEEMAILVERIFY = 13;
    const API_V1_USER_CHANGEPHONEVERIFY = 14;
    const ADMIN_API_V1_ASSOCIATE = 653;
    const ADMIN_API_V1_CLIENT = 656;
    const ADMIN_API_V1_NOTIFY = 225;
    const ADMIN_API_V1_DOCUMENT = 226;
    const ADMIN_API_V1_WELFARE = 229;
    const ADMIN_API_V1_WELFARE_SET = 660;
    const ADMIN_API_V1_TEAM = 655;
    const ADMIN_API_V1_OA = 654;
    const ADMIN_API_V1_RETURN_BILL = 657;
    const ADMIN_API_V1_CREATE = 438;
    const ADMIN_API_V1_PROFILE_PASSWORD = 245;
    const ADMIN_API_V1_PROFILE_LANG_REGION= 310;
    const ADMIN_API_V1_PROFILE_NOTIFY = 311;
    const ADMIN_API_V1_POWER = 662;
    const ADMIN_API_V1_REMARK_ASSOCIATE = 663;
    const ADMIN_API_V1_REMARK_OA = 664;
    const ADMIN_API_V1_REMARK_CLIENT = 665;
    const ADMIN_API_V1_PAYMENT_INVOICE = 725;
    const ADMIN_API_V1_EDIT = 267;
    const ADMIN_API_V1_DELETE = 403;
    const ADMIN_API_V1_SET= 309;
    const ADMIN_API_V1_CHECK = 659;


    public static function log($profileId, $activeType, $objType, $objId, $desc, $system = self::SYSTEM_CNA)
    {
        self::create([
            'system' => $system,
            'profile_id' => $profileId,
            'active_type' => $activeType,
            'obj_type' => $objType,
            'obj_id' => $objId,
            'desc' => is_array($desc)? json_encode($desc, JSON_UNESCAPED_UNICODE): $desc,
        ]);
    }

    /**
     * 后台日志操作类型多语言
     * @return array
     */
    public static function activeTypeLang()
    {
        $activeType = StructureModel::whereIn('structureID', [
            self::ADMIN_API_V1_CREATE,
            self::ADMIN_API_V1_EDIT,
            self::ADMIN_API_V1_DELETE,
            self::ADMIN_API_V1_SET,
            self::ADMIN_API_V1_CHECK
        ])->get()->toArray();

        $arrActiveType = [];
        foreach ($activeType as $val) {
            $arrActiveType[$val['structureID']] = $val;
        }

        return $arrActiveType;
    }

    /**
     * 后台日志操作对象多语言
     * @return array
     */
    public static function objTypeLang()
    {
        $objType = StructureModel::whereIn('structureID', [
            self::ADMIN_API_V1_ASSOCIATE,
            self::ADMIN_API_V1_CLIENT,
            self::ADMIN_API_V1_RETURN_BILL,
            self::ADMIN_API_V1_NOTIFY,
            self::ADMIN_API_V1_DOCUMENT,
            self::ADMIN_API_V1_WELFARE,
            self::ADMIN_API_V1_TEAM,
            self::ADMIN_API_V1_OA,
            self::ADMIN_API_V1_WELFARE_SET,
            self::ADMIN_API_V1_PROFILE_PASSWORD,
            self::ADMIN_API_V1_PROFILE_LANG_REGION,
            self::ADMIN_API_V1_PROFILE_NOTIFY,
            self::ADMIN_API_V1_POWER,
            self::ADMIN_API_V1_REMARK_ASSOCIATE,
            self::ADMIN_API_V1_REMARK_CLIENT,
            self::ADMIN_API_V1_REMARK_OA,
            self::ADMIN_API_V1_PAYMENT_INVOICE
        ])->get()->toArray();

        $arrObjType = [];
        foreach ($objType as $val) {
            $arrObjType[$val['structureID']] = $val;
        }

        return $arrObjType;

    }

}
