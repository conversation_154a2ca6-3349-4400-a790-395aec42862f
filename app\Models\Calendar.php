<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Calendar extends Model
{
    protected $table = 'cna_calendar';
    protected $guarded = [];

    //通知时间选项
    const NOTIFY_TYPE_NO = 0; //不通知
    const NOTIFY_TYPE_1_DAY = 1; //提前1天
    const NOTIFY_TYPE_2_DAY = 2; //提前2天
    const NOTIFY_TYPE_ARR = [
        self::NOTIFY_TYPE_NO,
        self::NOTIFY_TYPE_1_DAY,
        self::NOTIFY_TYPE_2_DAY,
    ];
}
