<?php

namespace App\Listeners;

use App\Events\ProfileBusinessProcess;
use App\Models\IcbcOrderModel;
use App\Models\PaymentModel;
use App\Models\ProfileInfoModel;
use App\Models\ProjectCategoryModel;
use App\Models\ProjectServiceModel;
use App\Services\ProfileBusinessProcessServices;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ProfileBusinessProcessListener
{
    private $profileInfoModel;

    private $profileBusinessProcessServices;

    public function __construct(ProfileInfoModel $profileInfoModel,ProfileBusinessProcessServices $profileBusinessProcessServices)
    {
        $this->profileInfoModel = $profileInfoModel;
        $this->profileBusinessProcessServices = $profileBusinessProcessServices;
    }

    public function handle(ProfileBusinessProcess $event): void
    {
        try{
            $orderId = $event->orderId;
            $orderInfo = IcbcOrderModel::find($orderId);
            if(empty($orderInfo)){
                throw new \Exception('订单信息获取失败');
            }
            $projectInfo = ProjectServiceModel::find($orderInfo->project);
            if(empty($projectInfo)){
                throw new \Exception('项目信息获取失败');
            }
            $profileId = $orderInfo->user_id;
            //获取用户信息 对应的 业务状态
            $user = ProfileInfoModel::where('profileID',$profileId )->first();
            if(empty($user)){
                throw new \Exception('合伙人信息获取失败');
            }
            $process = $this->profileBusinessProcessServices->getByProfileID($profileId,$projectInfo['code']);
            if(empty($process)){
                throw new \Exception('业务进程获取失败');
            }
            //判断结束业务
            $this->profileBusinessProcessServices->finished($process);
            //收款明细记录
            if ( $orderInfo->pay_status ==  1 ) {//记录收款明细
                $projectId = ProjectCategoryModel::query()->where('type', 1)->value('projectCategoriesID');
                $total = bcdiv($event->totalAmount, 100, 2);
                $paymentNumber = PaymentModel::createPaymentNumber('WL', 2);
                $res = PaymentModel::query()->create([
                    'profileID' => $profileId,
                    'companyID' => 0,
                    'type' => 1,
    //                'pay_type' => 3,
                    'projectId' => $projectId,  // 合伙人注册加盟
                    'detailId' => $process['project_id'],  // 合伙人加盟申请费
                    'fee' => $total,  // 金额
                    'put_amount' => $total,  // 收款金额
                    'paymentNumber' => $paymentNumber, // 单号
                    'paytime' => now(),
                    'currency' => $orderInfo->currency,
                    'currency_code' => $orderInfo->currency_code,
                    'currency_rate' => $orderInfo->currency_rate,
                    'createtime' => now(),
                ]);
            }
        }catch(\Exception $e){
            DB::table('cna_debug_log')->insert([
                'content' => "监听报错".json_encode($e->getTrace())
            ]);
        }
    }

}
