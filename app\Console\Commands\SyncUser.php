<?php
namespace App\Console\Commands;

use App\Models\ProfileInfoModel;
use App\Services\UserCenterService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class UserExpireNotify extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync_user';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '同步用户到用户中心';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }
    
    public function handle()
    {
        if (env('APP_ENV') != 'dev') { //todo
            return;
        }
        $nowTime = time();
        $startDate = date('Y-m-d H:i:s', $nowTime - 60);
        $endDate = date('Y-m-d H:i:s', $nowTime);
        $data = ProfileInfoModel::whereBetween('updated_at', [$startDate, $endDate])->get();
        if (empty($data)) {
            return;
        }
        $keys = [
            'profile_id' => 'profileID',
            'name' => 'profileName',
            'gender' => 'profileGender',
            'birthdate' => 'profileBirthDate',
            'email' => 'profileEmail',
            'phone' => 'profileContact',
            'password' => 'profilePassword',
            'address_unit' => 'profileAddressUnit',
            'address_street' => 'profileAddressStreet',
            'address_district_id' => 'profileAddressDistrictId',
            'address_city_id' => 'profileAddressCityId',
            'address_state_id' => 'profileAddressStateId',
            'uuid' => 'uuid',
            'profile_status' => 'status',
        ];
        $req = [];
        foreach ($data as &$item) {
            foreach ($keys as $k => $v) {
                $params[$k] = $item[$v];
            }
            if ($params['gender'] == 'M') {
                $params['gender'] = 1;
            } else if ($params['gender'] == 'F') {
                $params['gender'] = 2;
            } else {
                $params['gender'] = 0;
            }
            $req[] = $params;
        }
        $res = UserCenterService::syncUser($req);
        if (!empty($res['errors'])) {
            Log::error('command_SyncUser', $res['errors']);
        }
    }
}