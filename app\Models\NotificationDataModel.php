<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class NotificationDataModel extends Model
{
    use HasFactory;

    protected $table = 'cna_notification_data';
    protected $guarded = [];
    protected $primaryKey = 'notificationXID'; // 自定义主键


    /**
     * 查看用户已读通知
     * @param $user_id // 查看指定用户数据
     * @return mixed
     */
    public function reads($user_id)
    {
        $data = $this->where('notificationProfileID', $user_id)->pluck('notificationID')->toArray();
        return $data;
    }
}
