<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class BillModel extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $table = 'cna_bills';
    protected $guarded = [];
    protected $primaryKey = 'billID';

    public function billCompany()
    {
        return $this->belongsTo(BillCompany::class, 'billCompanyId');
    }

    public function billDescription()
    {
        return $this->belongsTo(BillDescription::class, 'billDescriptionId');
    }

    public function profileInfo()
    {
        return $this->belongsTo(ProfileInfoModel::class,'billProfileID');
    }
}
