<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>计算个人所税</title>
    　<script src="https://code.jquery.com/jquery-3.1.1.min.js"></script>
</head>
<body>



<div style="display: flex; align-items: center; flex-direction: column">
    <h2>计算合伙人个人所税</h2>
    <div class="form">
        <form action="/api/v1/admin/countTax" method="post">

          {{-- <div class="row">
                <div for="income" style="margin-right:5px">当月收入次数:</div>
                <select id="income" name="income">
                    <option value="1" @if (isset($param['income']) && $param['income']==1) selected @endif>1次</option>
                    <option value="2" @if (isset($param['income']) && $param['income']==2) selected @endif>2次</option>
                    <option value="3" @if (isset($param['income']) && $param['income']==3) selected @endif>超过3次</option>
                </select>
            </div>--}}

           {{-- <div class="row" id="income1" @if (isset($param['income']) && $param['income']!=1) style="display: none" @endif>
                <div for="amount" style="margin-right:5px">金&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;额:</div>
                <input type="text" id="amount" name="amount" required  value="@if (isset($param['amount'])) value="{{$param['amount']}}" @endif">
            </div>

            <div class="row" id="income2" @if (isset($param['income']) && $param['income']!=2) style="display: none" @endif>
                <div for="amount" style="margin-right:5px">金&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;额:</div>
                <div>
                    <input type="text" id="amount1" name="amount1" required  value="@if (isset($param['amount1'])) value="{{$param['amount1']}}" @endif">
                    <input type="text" id="amount2" name="amount2" required  value="@if (isset($param['amount2'])) value="{{$param['amount2']}}" @endif">
                </div>

            </div>

            <div class="row" id="income3" @if (isset($param['income']) && $param['income']!=3) style="display: none" @endif>
                <div for="amount" style="margin-right:5px">金&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;额:</div>
                <input type="text" id="amount1" name="amount1" required  value="@if (isset($param['amount1'])) value="{{$param['amount1']}}" @endif">
                <input type="text" id="amount2" name="amount2" required  value="@if (isset($param['amount2'])) value="{{$param['amount2']}}" @endif">
                <input type="text" id="amount3" name="amount3" required  value="@if (isset($param['amount3'])) value="{{$param['amount3']}}" @endif">
            </div>
--}}
            <div class="row" id="income" >
                <div for="amount" style="margin-right:5px">当月收入:</div>
                <input type="text" id="amount" name="amount1"   value="{{$amount1}}">
                <input type="text" id="amount" name="amount2"   value="{{$amount2}}">
                <input type="text" id="amount" name="amount3"   value="{{$amount3}}">
            </div>
            <div class="row">
                @if (isset($type) && $type==1)
                    第一次个人所得税：{{$tax}}元<br>

                @endif

                    @if (isset($type) && $type==2)

                        第一次个人所得税：{{$oneTax}}元<br>
                        第二次个人所得税：{{$tax}}元<br>
                    @endif

                    @if (isset($type) && $type==3)

                        第一次个人所得税：{{$oneTax}}元<br>
                        第二次个人所得税：{{$twoTax}}元<br>
                        第三次个人所得税：{{$tax}}元<br>
                    @endif


            </div>



            <!-- 提交按钮 -->
            <div>
                <input type="submit" value="提交">
            </div>

        </form>
    </div>

    <div class="content">

        <div class="left">

        </div>

        <div class="right">



        </div>




    </div>

</div>


</body>
<style>
    .form{}
    .row{
        margin: 15px 0;
        display: flex;
    }
    .row label{
        width: 100px;
        display: block;
    }

    .content{
        margin-top:50px;

    }

    .content .left{

        margin-right: 20px;
        margin-bottom: 50px;
    }

    .content .left table td, .content .right table td{
        text-align: center;
    }

    .content .right{
        margin-bottom: 50px;
    }
</style>
<script>
    // 获取 select 元素和显示选择结果的元素
    $(function(){
        $('#income2').css('display', 'none');
        $('#income3').css('display', 'none');
        $('#income').on('change', function() {
            var value = $(this).val();
            if (value == 1) { // 1次
                $('#income1').css('display', 'block');
                $('#income2').css('display', 'none');
                $('#income3').css('display', 'none');
            } else if (value == 2) { // 2次
                $('#income1').css('display', 'none');
                $('#income2').css('display', 'block');
                $('#income3').css('display', 'none');
            } else if (value == 3) { // 超过3次
                $('#income1').css('display', 'none');
                $('#income2').css('display', 'none');
                $('#income3').css('display', 'block');
            }
        });
    })
</script>
</html>