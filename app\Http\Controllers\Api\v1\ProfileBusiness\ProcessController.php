<?php

namespace App\Http\Controllers\Api\v1\ProfileBusiness;

use App\Http\Controllers\Controller;
use App\Models\ProfileBusinessProcessModel;
use App\Models\ProfileInfoModel;
use App\Services\ProfileBusinessProcessServices;
use App\Services\ProjectServiceDataServices;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Illuminate\Http\Request;

class ProcessController extends Controller
{
    public function __construct(ProfileBusinessProcessServices $profileBusinessProcessServices)
    {
        $this->services = $profileBusinessProcessServices;
    }

    public function getUserId($token){
        $decodeToken = JWT::decode($token, new Key(env('JWT_KEY'), 'HS256'));
        if (!isset($decodeToken->action)) {
            throw new \Exception('action not exist');
        }
        if ($decodeToken->action != ACTION_PROFILE_REGISTER) {
            throw new \Exception('action error');
        }
        return $decodeToken->user_id;
    }

    public function index(Request $request)
    {
        $user = $request->attributes->get('user');
        $process = $this->services->getByProfileID($user->profileID);
        if( empty($process) ) {
            $process = [
                'status' => -1,
                'status_remark' => '当前无业务进程'
            ];
        }
        return responseSuccess($process);
    }

    public function check(Request $request){
        $param = $request->validate([
            'project_code' => 'required|string',
            'token' => 'nullable|string'
        ]);
        try{
            if (!empty($param['token'])) {
                $userId = $this->getUserId($param['token']);
            }else{
                $user = $request->attributes->get('user');
                if( empty($user) ) {
                    return responseFail('用户信息过期');
                }
                $userId = $user->profileID;
            }
            $process = $this->services->getByProfileID($userId,$param['project_code']);
            if( empty($process) ){
                $res['state'] = false;
                return responseSuccess($res,'无此业务进程');
            }
            $res['status'] = $this->services->check($process->id) ? 1 : 0;
            //注册特定内容
            if( $process['project_code'] == 'profile_register' && $res['status'] ) {
                $user = ProfileInfoModel::where('profileID', $userId)->first();
                //生成token
                $action = ACTION_PROFILE_AUTH;
                $payload = [
                    'exp' => time() + (3600 * 24 * 7),
                    'iat' => time(),
                    'action' => $action,
                    'user_id' => $user['profileID'],
                ];
                $token = JWT::encode($payload, env('JWT_KEY'), 'HS256');
                setcookie(
                    "Authorization",
                    "Bearer $token",
                    time() + 3600 * 24 * 7,
                    "/"
                );
                $res['token'] = 'Bearer ' . $token;
                $res['register_setting'] = ProfileInfoModel::initialRes($user);
            }
            return responseSuccess($res,$res['status'] ? '业务完成' : '业务进行中');
        }catch(\Exception $e){
            return responseFail($e->getMessage());
        }
    }

    public function getProjectCode(Request $request,ProjectServiceDataServices $projectServiceDataServices){
        $param = $request->validate([
            'project_code' => 'nullable|string',
            'project_name' => 'nullable|string',
        ]);
        return $projectServiceDataServices->getCodeList($param);
    }

    /**
     * 新增业务进程
     * @param Request $request
     * @return void
     */
    public function create(Request $request){
        $param = $request->validate([
            'project_code' => 'required|string',
            'token' => 'nullable|string',
        ],[
            'project_code.required' => '业务代码不能为空',
            'project_code.string' => '业务代码不正确',
        ]);
        if (!empty($param['token'])) {
            $userId = $this->getUserId($param['token']);
        }else{
            $user = $request->attributes->get('user');
            if( empty($user) ){
                return responseFail('用户信息过期');
            }
            $userId = $user->profileID;
        }

        //检查code可用性
        //检查用户进行中的业务
        $process = $this->services->getByProfileID($userId,$param['project_code']);
        if( !empty($process) && $process->status == ProfileBusinessProcessModel::STATUS_DOING ) {
//            if( $process['project_code'] == $param['project_code'] ){
            return responseSuccess($process,'业务已在办理中');
//            }
            //这里是否允许多业务并行
//            return responseFail('已有业务进行中');
        }

        if( !$this->services->create($userId,$param['project_code']) ){
            return responseFail('操作失败');
        }
        return responseSuccess(message:'创建成功');
    }

    /**
     * 新增业务进程
     * @param Request $request
     * @return void
     */
    public function withdraw(Request $request){
        $user = $request->attributes->get('user');
        $param = $request->validate([
            'project_code' => 'required|string',
        ],[
            'project_code.required' => '业务代码不能为空',
            'project_code.string' => '业务代码不正确',
        ]);
        //检查code可用性
        //检查用户进行中的业务
        $business = $this->services->getByProfileID($user->profileID,$param['project_code']);
        if( empty($business) ) {
            return responseFail('没有对应业务在进行');
        }
        $this->services->update($business['id'],['status'=>ProfileBusinessProcessModel::STATUS_CANCEL]);
        return responseSuccess();
    }
}