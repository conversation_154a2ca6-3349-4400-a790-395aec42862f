<?php
namespace App\Http\Controllers\Api\v1\ProfileContract;

use App\Http\Controllers\Controller;
use App\Models\ProfileContractModel;
use App\Services\SignatureService;
use Illuminate\Http\Request;
use App\Services\DocService;
use App\Models\BankCardCn;

class ProfileContractController extends Controller {

    const ACTION = 'contract';

    /**
     * 保存合同签名
     * @param Request $request
     * @return null
     */
    public function save(Request $request)
    {
        $type = $request->post('type', 1);
        if (!in_array($type, ProfileContractModel::TYPE_ARR)) {
            return responseFail();
        }
        try {
            $user = $request->attributes->get('user');

            $signature = $request->input('signature');
            if (!$signature) {
                return responseFail(__('missing parameter', ['param' => 'signature']));
            }

            $contract = ProfileContractModel::where('profileID', $user->profileID)->first();
            $signatureService = new SignatureService();
            $url = $signatureService->sealSign($user->profileID, $contract->contract, $signature, $type);
            $token = DocService::generateToken($url, self::ACTION);

            return responseSuccess($token);
        } catch (\Exception $e) {
            return responseFail($e->getMessage());
        }
    }
    
    /**
     * Method previewSignatureContract
     * 预览签名合同
     *
     * @param Request $request [explicite description]
     *
     * @return void
     */
    public function previewSignatureContract(Request $request)
    {
        $token = $request->route('token');
        return DocService::preview($token, self::ACTION);
    }

    
    /**
     * Method generateContractTemplateToken
     * 生成预览合同模版token
     *
     * @param Request $request [explicite description]
     *
     * @return void
     */
    public function generateContractTemplateToken(Request $request)
    {
        $type = $request->post('type', 1);
        if (!in_array($type, ProfileContractModel::TYPE_ARR)) {
            return responseFail();
        }
        try {
            $user = $request->attributes->get('user');
            $signatureService = new SignatureService();
            $contract = $signatureService->getContractTpl($user, $type);
            $token = DocService::generateToken($contract, self::ACTION);

            return responseSuccess($token);
        } catch (\Exception $e) {
            return responseFail($e->getMessage());
        }      
    }
    
    /**
     * Method previewContractTemplate
     * 预览合同模版
     *
     * @param Request $request [explicite description]
     *
     * @return void
     */
    public function previewContractTemplate(Request $request)
    {
        $token = $request->route('token');
        return DocService::preview($token, self::ACTION);
    }

    public function isSign(Request $request)
    {
        $type = $request->get('type');
        if (!in_array($type, ProfileContractModel::TYPE_ARR)) {
            return responseFail();
        }
        $user = $request->attributes->get('user');
        $data = ProfileContractModel::where('profileID', $user['profileID'])->where('type', $type)->first();
        if (empty($data['signature']) || empty($data['contract'])) {
            return responseSuccess(['res' => 0]);
        }
        return responseSuccess(['res' => 1]);
    }
}