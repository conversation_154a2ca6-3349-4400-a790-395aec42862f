<?php

namespace App\Http\Controllers\Api\v1\Question;

use App\Http\Controllers\Controller;
use App\Models\Answer;
use App\Models\Question;
use Illuminate\Http\Request;

class QuestionController extends Controller
{
    public function list(Request $request)
    {
        $body = $request->validate([
            'project_category_id' => ['required', 'integer', 'min:1'],
        ]);
        $data = Question::where('project_category_id', $body['project_category_id'])->get();
        
        return responseSuccess($data);
    }

    public function submitAnswer(Request $request)
    {
        $body = $request->validate([
            'question_id' => ['required', 'integer', 'min:1'],
            'content' => ['required'],
            'score' => ['integer', 'between:0,100'], //todo
        ]);
        $question = Question::find($body['question_id']);
        if (empty($question)) {
            return responseFail();
        }
        $user = $request->attributes->get('user');
        $body['profile_id'] = $user['profileID'];
        Answer::create($body);
        
        return responseSuccess();
    }
}
