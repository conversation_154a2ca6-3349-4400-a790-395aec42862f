<?php
namespace App\Http\Controllers\Api\v1\ICBC;

use App\Events\UserPaid;
use App\Http\Controllers\Controller;
use App\Models\IcbcOrderModel;
use App\Models\ProjectServiceModel;
use App\Services\ICBC\ICBCService;
use Illuminate\Http\Request;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ICBCController extends Controller
{

    public function createPayToken()
    {
        $payload = [
            'exp' => time() + 3600,
            'action' => ACTION_PROFILE_REGISTER,
            'user_id' => 1040,
        ];
        $token = JWT::encode($payload, env('JWT_KEY'), 'HS256');

        return responseSuccess($token);
    }

    /**
     * 生成注册支付二维码
     * @return void
     */
    public function registerPayQRCode(Request $request)
    {
        //校验token
        $token = $request->input('token', '');
        if (!$token) {
            return responseFail('token not exist');
        }

        //提取token中的user_id
        $decodeToken = JWT::decode($token, new Key(env('JWT_KEY'), 'HS256'));
        if (!isset($decodeToken->action)) {
            return responseFail('action not exist');
        } elseif ($decodeToken->action != ACTION_PROFILE_REGISTER) {
            return responseFail('action error');
        }

        $userId = $decodeToken->user_id;
        $total = env('WECHAT_FEE') * 100;
        $outTradeNo = ICBCService::generateOutTradeNo($userId);

        // 调用ICBC接口
        $domail =   $request->getSchemeAndHttpHost();
        $notify =  $domail.'/api/v1/icbc/notify';
        $result = ICBCService::generatePayQRcode($outTradeNo, $total, $notify);
        if ($result) {
            //创建订单
            $ret = IcbcOrderModel::query()->create([
                'mer_id'        => config('icbc.mer_id'),
                'app_id'        => config('icbc.app_id'),
                'user_id'       => $userId,
                'qrcode_msg_id' => $result['msg_id'],
                'out_trade_no'  => $outTradeNo,
                'qrcode'        => $result['qrcode'],
                'total_amt'     => $total,
                'attach'        => $result['attach'],
                'type'          => IcbcOrderModel::TYPE_REGISTER,
                'remark'        => '加盟商入驻',
                'project'       => ProjectServiceModel::PAY_REGISTER
            ]);

            if (!$ret) {
                return responseFail('GENERATE_ORDER_ERROR');
            }

            $img = \SimpleSoftwareIO\QrCode\Facades\QrCode::format('png')->size(150)->generate($result['qrcode']);
            $img = 'data:image/png;base64,' . base64_encode($img);
            return responseSuccess(['qrcode'=>$img]);
        }
    }


    /**
     * 回调处理
     * @return \Illuminate\Http\JsonResponse|void
     */
    public function notify()
    {
        $queryString = file_get_contents("php://input");

        // 解码查询字符串
        $decodedString = urldecode($queryString);

        // 将查询字符串解析为数组
        parse_str($decodedString, $data);   

        $response_biz_content = isset($data['biz_content'])?json_decode($data['biz_content'], true):'';

        if ($response_biz_content && $response_biz_content['return_code'] == 0) {
            try {
                $order = IcbcOrderModel::query()->where('pay_status', 0)->where('out_trade_no', $response_biz_content['out_trade_no'])->first();
                if ($order) {
                    $order->pay_msg_id = $response_biz_content['msg_id'];
                    $order->order_id   = $response_biz_content['order_id'];
                    $order->pay_time   = $response_biz_content['pay_time'];
                    $order->pay_status = 1;
                    $order->cust_id    = $response_biz_content['cust_id'];
                    $order->card_no    = $response_biz_content['card_no'];
                    $order->bank_name  = isset($response_biz_content['bank_name'])??$response_biz_content['bank_name'];
                    $order->channel    = $response_biz_content['channel'];
                    $order->attach     = $response_biz_content['attach'];
                    $order->tp_cust_id = isset($response_biz_content['tp_cust_id'])?? $response_biz_content['tp_cust_id'];
                    $order->trx_ser_no = isset($response_biz_content['trx_ser_no'])??$response_biz_content['trx_ser_no'];
                    $order->tp_order_id= isset($response_biz_content['tp_order_id'])??$response_biz_content['tp_order_id'];
                    $order->sub_open_id= isset($response_biz_content['sub_open_id'])??$response_biz_content['sub_open_id'];
                    $order->bank_type  = isset($response_biz_content['bank_type'])??$response_biz_content['bank_type'];
                    $order->tp_user_id = isset($response_biz_content['tp_user_id'])??$response_biz_content['tp_user_id'];
                    $order->save();

                    event(new UserPaid($order->user_id, $response_biz_content['total_amt']));

                }
            } catch (\Exception $exception) {
                DB::rollBack();
                Log::info($exception->getTrace());
                return response()->json(['code' => 1, 'msg' => 'pay fail']);
            }
        } else {
            return response()->json(['code' => 1, 'msg' => 'not response data']);
        }


    }

    /**
     * 检查支付订单状态
     * @return void
     */
    public function checkRegisterOrder(Request $request)
    {
        $profileId = $request->get('profileID');
        if (!$profileId || !is_numeric($profileId) || $profileId < 0) {
            return responseFail();
        }
        IcbcOrderModel::checkRegisterOrder($profileId);
        return responseSuccess();

    }

}