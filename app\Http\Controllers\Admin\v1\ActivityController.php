<?php

namespace App\Http\Controllers\Admin\v1;

use App\Http\Controllers\Controller;
use App\Models\Activity;
use App\Models\AiManFile;
use App\Models\CompanyGsp;
use App\Models\CompanyGspReport;
use App\Models\CountryModel;
use App\Models\GspOption;
use App\Models\VisitorApply;
use App\Models\VisitorAttach;
use App\Models\VisitorLogin;
use App\Services\DocService;
use App\Services\OssService;
use App\Services\ZipService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ActivityController extends Controller
{

    /**
     * 活动列表
     * @param Request $request
     * @return null
     */
    public function index(Request $request)
    {
        $keyword = $request->get('keyword', '');
        $pageSize = $request->get('page_size', 10);

        $list = Activity::query()
            ->when($keyword != '', function ($q) use ($keyword) {

                $q->orWhere('active_name', 'like', '%' . $keyword . '%')
                    ->orWhere('active_time', 'like', '%' . $keyword . '%')
                    ->orWhere('active_place', 'like', '%' . $keyword . '%');

            })->orderBy('id', 'desc')
            ->paginate($pageSize);

        $items = $list->items();


        $paginate = [
            'page_size' => $pageSize,
            'total' => $list->total(),
            'total_page' => $list->lastPage(),
        ];

        return responseSuccess(compact('items', 'paginate'));
    }

    /**
     * 添加活动
     * @param Request $request
     * @return void
     */
    public function add(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'active_name'       => 'required',
            'active_time'       => 'required',
            'active_place'      => 'required',
        ]);

        // 验证失败
        if ($validator->fails()) {
            return responseFail($validator->errors()->all());
        }

        $body = $request->all();

        Activity::insert([
            'active_name'       => $body['active_name'],
            'active_time'       => $body['active_time'],
            'active_place'      => $body['active_place'],
            'created_at'            => date('Y-m-d H:i:s')
        ]);


        return responseSuccess();
    }


    // 编辑活动
    public function edit(Request $request)
    {


        $validator = Validator::make($request->all(), [
            'id'                 => ['required', 'integer', 'min:1'],
            'active_name'       => 'required',
            'active_time'       => 'required',
            'active_place'      => 'required',
        ]);

        // 验证失败
        if ($validator->fails()) {
            return responseFail($validator->errors()->all());
        }

        $body = $request->all();

        // 获取详情
        $info = Activity::find($body['id']);
        if (empty($info)) {
            return responseFail(__('info no exist'));// 信息不存在
        }


        Activity::where('id', $body['id'])->update([
            'active_name'       => $body['active_name'],
            'active_time'       => $body['active_time'],
            'active_place'      => $body['active_place'],
            'updated_at'        => date('Y-m-d H:i:s')
        ]);

        return responseSuccess();

    }


    // 删除活动
    public function del(Request $request)
    {
        $id = $request->input('id');

        if (empty($id)) {
            return responseFail(__('param error', ['param' => 'id']));// 参数缺失:id
        }

        // 获取详情
        $info = Activity::find($id);
        if (empty($info)) {
            return responseFail(__('info no exist'));// 信息不存在
        }

        Activity::where('id', $id)->delete();

        return responseSuccess();
    }

}
