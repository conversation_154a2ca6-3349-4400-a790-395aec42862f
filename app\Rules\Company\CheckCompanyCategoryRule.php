<?php

namespace App\Rules\Company;

use App\Models\CompanyCategoryModel;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class CheckCompanyCategoryRule implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $model = new CompanyCategoryModel;
        if (!$model->getRecord($value)) {
            $fail('请选择企业类型');
        }
    }
}
