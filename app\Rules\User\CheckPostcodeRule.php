<?php

namespace App\Rules\User;

use App\Models\CountryModel;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class CheckNricRule implements ValidationRule
{
    const PATTERN = '/^[1-9]d{5}$/';

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (env('COUNTRY') == CountryModel::COUNTRY_ID_CHINA) {
            if (!preg_match(self::PATTERN, $value)) {
                $fail(__('postcode error'));
            }
        }
    }
}
