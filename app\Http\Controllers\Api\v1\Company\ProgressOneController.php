<?php

namespace App\Http\Controllers\Api\v1\Company;

use App\Http\Controllers\Controller;
use App\Models\ActiveLog;
use App\Services\DocService;
use Illuminate\Http\Request;
use App\Models\ProjectProgressOneModel;
use App\Models\CompanyModel;
use App\Models\ProjectProgressOnePdfModel;
use App\Models\ProjectServiceModel;
use App\Services\PdfService;
use Firebase\JWT\JWT;
use App\Http\Requests\Company\ProgressOne\ProgressOneCommit;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class ProgressOneController extends Controller
{
    private $projectProgressOneModel;
    private $companyModel;
    private $request;
    private $projectProgressOnePdfModel;
    private $pdfService;
    const ACTION = 'progress_one_download_pdf'; //场景

    public function __construct(
        ProjectProgressOneModel $projectProgressOneModel, 
        CompanyModel $companyModel, 
        Request $request,
        ProjectProgressOnePdfModel $projectProgressOnePdfModel,
        PdfService $pdfService
    )
    {
        $this->projectProgressOneModel = $projectProgressOneModel;
        $this->companyModel = $companyModel;
        $this->request = $request;
        $this->projectProgressOnePdfModel = $projectProgressOnePdfModel;
        $this->pdfService = $pdfService;
    }
    
    /**
     * Method 获取进度一信息
     *
     * @return void
     */
    public function getProgressOneInfo()
    {
        $user = $this->request->attributes->get('user');
        $companyID = $this->request->input('companyID', 0);
        $userID = $user['profileID'];

        //校验用户及公司合法性
        $this->companyModel->verificationUserCompany($companyID, $userID);

        //整合数据
        $progressOne = $this->projectProgressOneModel->getProgressOneInfo($companyID, $userID);
        $companyServices = [];
        $reason = '';
        if ($progressOne) {
            $companyServices = array_map('intval', explode(',', $progressOne->companyServices));
            $reason = $progressOne->reason;
        }

        return responseSuccess(compact('companyServices', 'reason'));
    }
    
    /**
     * Method 生成下载文件密钥
     *
     * @return void
     */
    public function generateDownloadFileKey()
    {
        $user = $this->request->attributes->get('user');
        $companyID = $this->request->input('companyID', 0);
        $userID = $user['profileID'];
        $company = $this->companyModel->verificationUserCompany($companyID, $userID);
        $userPdf = $this->projectProgressOnePdfModel::where('companyID', $companyID)->where('profileID', $user['profileID'])->first();

        //创建下载pdf记录，不存在则创建记录，存在则读取记录，避免重复创建pdf浪费资源
        if (!$userPdf) {
            $resource = $this->pdfService->createForm($company->companyName, $company->companyRegisterCode, $company->companyCategoriesID);
            $this->projectProgressOnePdfModel::create([
                'companyID' => $companyID,
                'profileID' => $user['profileID'],
                'file' => $resource
            ]);
        } else {
            $resource = $userPdf->file;
        }

        //生成密钥
        $payload = [
            'exp' => time() + 60,
            'action' => 'progress_one_download_pdf',
            'file' => $resource,
            'name' => '预审表格.pdf'
        ];
        $token = JWT::encode($payload, env('JWT_KEY'), 'HS256');

        return responseSuccess($token);
    }
    
    /**
     * Method 生成预览预审表格密钥
     *
     * @return void
     */
    public function generateViewFileKey()
    {
        $user = $this->request->attributes->get('user');
        $companyID = $this->request->input('companyID', 0);
        $userID = $user['profileID'];
        $company = $this->companyModel->verificationUserCompany($companyID, $userID);
        $resource = $this->projectProgressOneModel->getProgressOneInfo($companyID, $userID);
        //校验进度一数据
        if (!$resource) {
            return responseFail();
        }
        //生成密钥
        $token = DocService::generateToken($resource->upload_file, self::ACTION);

        return responseSuccess($token);
    }
    
    /**
     * Method 下载文件
     *
     * @return void
     */
    public function download()
    {
        $token = $this->request->route('token');

        return DocService::download($token, self::ACTION);
    }
    
    /**
     * Method 预览文件
     *
     * @return void
     */
    public function preview()
    {
        $token = $this->request->route('token');

        return DocService::preview($token, self::ACTION);
    }
    
    /**
     * Method 提交修改进度一
     *
     * @param ProgressOneCommit $request [explicite description]
     *
     * @return void
     */
    public function commit(ProgressOneCommit $request)
    {
        $user = $request->attributes->get('user');
        $body = $request->all();
        $companyID = $body['companyID'];
        $userID = $user['profileID'];
        $file = $request->file('preAuditForm');
        $companyServices = $body['companyServices'];

        //校验公司
        $company = $this->companyModel->verificationUserCompany($companyID, $userID);
        if (($company->companyState != 0 && $company->companyState != 3) || $company->companyProgressState != 1) {
            return responseFail();
        }

        $progressOne = $this->projectProgressOneModel->getProgressOneInfo($companyID, $userID);

        try {
            DB::beginTransaction();

            //更新企业状态
            $company->companyState = 1;
            $company->save();

            //更新进度一信息
            // $newFile = $file->store('files/project/progressOne/upload', 'public');
            $newFile = $file->storeAs('files/project/progressOne/upload', generateUploadFilename($file->getClientOriginalExtension()), 'public');
            if ($progressOne) {
                $oldFile = $progressOne->upload_file;
                $progressOne->upload_file = $newFile;
                $progressOne->save();
                Storage::disk('public')->delete($oldFile);
                $update = ['upload_file' => $newFile];
                //记录活动日志
                ActiveLog::log($user['profileID'], ActiveLog::ACTIVE_UPDATE, ActiveLog::API_V1_COMPANY_PROGRESSONE_COMMIT,
                    $companyID, $update);
            } else {
                $companyServicePrice = ProjectServiceModel::selectRaw('SUM(price) AS total_price, currency')
                    ->whereIn('id', explode(',', $companyServices))->groupBy('currency')->first();
                $insert = [
                    'companyID' => $companyID,
                    'profileID' => $userID,
                    'companyServices' => $companyServices,
                    'companyServicePrice' => $companyServicePrice->total_price,
                    'serviceCurrency' => $companyServicePrice->currency,
                    'upload_file' => $newFile
                ];
                $this->projectProgressOneModel::create($insert);
                //记录活动日志
                ActiveLog::log($user['profileID'], ActiveLog::ACTIVE_CREATE, ActiveLog::API_V1_COMPANY_PROGRESSONE_COMMIT,
                    $companyID, $insert);
            }
            
            DB::commit();
            return responseSuccess();
        } catch (\Exception $e) {
            DB::rollBack();
            return responseFail();
        }        
    }
}
