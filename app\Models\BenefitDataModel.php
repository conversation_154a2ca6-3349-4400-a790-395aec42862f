<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class BenefitDataModel extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $table = 'cna_benefit_data';
    protected $guarded = [];
    protected $primaryKey = 'benefitXID';
    public $timestamps = false;

    public function benefitInfo()
    {
        return $this->belongsTo(BenefitInfoModel::class, 'benefitID');
    }

    public function profileInfo()
    {
        return $this->belongsTo(ProfileInfoModel::class, 'userID');
    }
}