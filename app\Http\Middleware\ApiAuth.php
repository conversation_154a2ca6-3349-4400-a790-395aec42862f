<?php

namespace App\Http\Middleware;

use App\Models\LoginLog;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Symfony\Component\HttpFoundation\Response;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use App\Models\ProfileInfoModel;

class ApiAuth
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, $action = null): Response
    {
        //获取头部鉴权密钥
        $authorization = $request->header('authorization');
        if (!$authorization) {
            return responseFail(__('missing parameter', ['param' => 'authorization']), 401);
        }

        // 当前路由方法
        $currentAction = request()->path();
        $prefix = 'api/v1';
        if (Str::startsWith($currentAction, $prefix)) {
            $currentAction = substr($currentAction, strlen($prefix));
        }

        try {
            //解密密钥
            $token = explode(' ', $authorization);
            $token = trim($token[1]);
            $decodeToken = JWT::decode($token, new Key(env('JWT_KEY'), 'HS256'));

            //校验
            if (isset($decodeToken->action)) {
                if ($decodeToken->action == ACTION_PROFILE_AUTH || $decodeToken->action == ACTION_PROFILE_EXPIRE) {
                    $profileID = $decodeToken->user_id;
                    $user = ProfileInfoModel::where('profileID', $profileID)->first();
                    if (empty($user)) {
                        return responseFail(__('account error'), 401);
                    }

                    //账户过期，检查是否已续费
                    if ($decodeToken->action == ACTION_PROFILE_EXPIRE &&
                        $user['status'] == ProfileInfoModel::STATUS_EXPIRE) {
                        return responseFail(__('Authorization has expired'),401);
                    }
                    //检查初始设置是否完成
                   /* if ($action != 'register_setting' && !in_array($currentAction, config('except.register_setting'))) {
                        $initial = ProfileInfoModel::initialRes($user);
                        if ($initial) {
                            return responseFail('Unauthorized5', 401);
                        }
                    }*/
                    //检验token是否被用户手动退出
                    $tokenList = LoginLog::where('profile_id', $profileID)->where('token_status', 0)->pluck('token')
                        ->toArray();
                    if (in_array($token, $tokenList)) {
                        return responseFail(__('Authorization has logout'), 401);
                    }
                    $request->attributes->set('user', $user);
                } else {
                    return responseFail(__('Authorization failed'), 401);
                }
            } else {
                return responseFail(__('param error', ['param'=>'authorization']), 401);
            }
        } catch (\Exception $e) {
            return responseFail(__('Authorization error'), 401);
        }
        
        return $next($request);
    }

}
