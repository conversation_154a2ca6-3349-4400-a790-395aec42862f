<?php
namespace App\Console\Commands;

use App\Models\EmailDataModel;
use App\Models\ProfileInfoModel;
use App\Services\EmailService;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;

class ProfileSettingNotify extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'profile_setting_notify';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '检查下级用户是否做初始化设置';
    private $emailService;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(EmailService $emailService)
    {
        parent::__construct();
        $this->emailService = $emailService;
    }

    /**
     * Execute console command.
     *
     * @return mixed
     */
    public function handle()
    {

        $preEmail = [];
        $fourHoursAgo = Carbon::now()->subHours(4); // 4小时没登录发给上级
        $profileInfos = ProfileInfoModel::query()
            ->whereIn('status', [ProfileInfoModel::STATUS_VERIFYING, ProfileInfoModel::STATUS_ACTIVE])
            ->where('created_at', '<', $fourHoursAgo)
            ->where('pre_id', '>', 0)
            ->get();

        if ($profileInfos) {
            foreach ($profileInfos as $user) {
                // 上级用户
                $preUser = ProfileInfoModel::where('profileID', $user['pre_id'])->first();
                // 初始化设置情况
                $arrRes = ProfileInfoModel::initialRes($user);
                //  是否发送过
                $exist = EmailDataModel::query()->where('emailScene', 5)
                    ->where('emailAddress', $preUser['profileEmail'])->count();

                // 还没有走完初始化设置且还没发过邮件
                if ($arrRes && $exist==0) {
                    $preEmail[] = [
                        'preId'   => $preUser['profileID'],
                        'preName' => $preUser['profileName'],
                        'profileName' => $user['profileName'],
                        'profileCreateTime' => $user['created_at'],
                        'toEmail' => $preUser['profileEmail'],
                    ];
                }
            }
            if ($preEmail) { // 逐一发邮件通知
                foreach ($preEmail as $emailParam) {
                    // 邮件模板
                    $templateData  = $this->emailService->getTemplateData('', 'ProfileSetting', $emailParam['preId']);
                    // 替换变量
                    $subject = str_replace('{{profileName}}', $emailParam['profileName'], $templateData['subject']);
                    $body = str_replace(['{{preName}}','{{profileName}}', '{{createtime}}', '{{currentdate}}'],
                        [$emailParam['preName'], $emailParam['profileName'], $emailParam['profileCreateTime'], date('Y年m月d日')], $templateData['htmlBody']);
                    $this->emailService->sendEmail($emailParam['toEmail'], $subject, $body);
                    EmailDataModel::create([
                        'emailProfileID' => $emailParam['preId'],
                        'emailAddress' => $emailParam['toEmail'],
                        'emailCode' => 0,
                        'emailState' => 0,
                        'emailExpirationTime' => time() + config('email.email_exp_time'),
                        'emailScene' => 5,
                    ]);
                }

            }
        }

    }
}

