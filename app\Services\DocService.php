<?php

namespace App\Services;

use App\Exceptions\DefaultException;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Illuminate\Support\Facades\Storage;

class DocService
{
    //文档类型
    const TYPE_TEAM = 1; //三三制
    const TYPE_CN_SIGN = 2; //业务服务合同
    const TYPE_BANK_ACCOUNT = 3; //银行卡
    const TYPE_NRIC = 4; //身份证

    //生成文档密钥
    public static function generateToken($docPath, $action, $expire = 60, $name = '')
    {
        $payload = [
            'exp' => time() + $expire,
            'action' => $action,
            'file' => $docPath,
            'name' => $name,
        ];
        return JWT::encode($payload, env('JWT_KEY'), 'HS256');
    }

    //预览文档
    public static function preview($token, $action)
    {
        $decodeToken = self::decodeToken($token, $action);
        $file = $decodeToken->file;
        if (!Storage::disk('public')->exists($file)) {
            return abort(404, 'File not found.');
        }
        $mimeType = Storage::disk('public')->mimeType($file);
        $fileSize = Storage::disk('public')->size($file);

        return response()->stream(function () use ($file) {
            echo Storage::disk('public')->get($file);
        }, 200, ['Content-Type' => $mimeType, 'Content-Length' => $fileSize]);
    }

    //下载文档
    public static function download($token, $action)
    {
        $decodeToken = self::decodeToken($token, $action);
        if (!Storage::disk('public')->exists($decodeToken->file)) {
            return abort(404, 'File not found.');
        }
        if ($decodeToken->name !== '') {
            return Storage::disk('public')->download($decodeToken->file, $decodeToken->name);
        }
        return Storage::disk('public')->download($decodeToken->file);
    }

    //解密密钥
    private static function decodeToken($token, $action)
    {
        try {
            $decodeToken = JWT::decode($token, new Key(env('JWT_KEY'), 'HS256'));
            if (!isset($decodeToken->action)) {
                throw new DefaultException('非法操作');
            }
            if ($action != $decodeToken->action) {
                throw new DefaultException('非法操作');
            }
            if (!Storage::disk('public')->exists($decodeToken->file)) {
                throw new DefaultException('非法操作');
            }
            return $decodeToken;
        } catch (\Exception $e) {
            throw new DefaultException('非法操作');
        }
    }
}