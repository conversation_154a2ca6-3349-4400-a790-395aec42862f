<?php
namespace App\Http\Controllers\Admin\v1;

use App\Http\Controllers\Controller;
use App\Models\ActiveLog;
use App\Models\PowerInfoModel;
use App\Models\PowerUserDataModel;
use App\Models\ProfileInfoModel;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Symfony\Component\HttpFoundation\Request;

class PowerInfoController extends Controller
{
    /**
     * 权限设置信息
     * @return void
     */
    public function index(Request $request) {

        $userId = $request->user_id;
        if (empty($userId)) {
            return responseFail(__('param error', ['param' => 'id']));
        }

        // 获取用户拥有权限
        $powerUserData = PowerUserDataModel::query()->where('powerProfileID', $userId)
            ->pluck('powerActionCode', 'powerActionUserXID')->all();

        // 获取权限菜单
        $list = PowerInfoModel::where('isDisplay', 1)
            ->orderBy('powerOrder', 'asc')
            ->get();

        $items = collect($list)->map(function ($item) use ($powerUserData) {
            foreach($item->powerActions as $key => $powerAction) {
                $powerAction->check = false;
                if (in_array($powerAction->powerActionCode, $powerUserData)) { // 是否有操作权限
                    $powerAction->check = true;
                }
                $item->powerActions[$key] = $powerAction;
            }
            return $item;
        });

        return responseSuccess($items);
    }

    /**
     * 保存权限设置
     * @return void
     */
    public function savePower(Request $request)
    {
        $user_id = $request->user_id;
        $data = $request->data;
        $user = $request->attributes->get('user');

        if (empty($user_id)) {
            return responseFail(__('param error', ['param' => 'id']));
        }

        $data = json_decode($data, true);
        if (empty($data)) {
            return responseFail(__('missing parameter', ['param' => 'data']));
        }

        $savePowerData = [];
        foreach ($data as $key => $value) {
            $savePowerData[] = [
                'powerID' => $value['powerID'],
                'powerActionCode' => $value['powerActionCode'],
                'powerProfileID'  => $user_id
            ];
        }

        // 设置用户
        $profileName = ProfileInfoModel::where('profileID', $user_id)->value('profileName');

        try {
            // 开启事务
            DB::beginTransaction();

            // 删除之前设置的权限值
            PowerUserDataModel::where('powerProfileID', $user_id)->delete();
            // 新增权限值
            PowerUserDataModel::insert($savePowerData);

            // 提交
            DB::commit();

            // 记录日志
            ActiveLog::log($user['profileID'], ActiveLog::ADMIN_API_V1_EDIT, ActiveLog::ADMIN_API_V1_POWER,
                $user['profileID'],$profileName, ActiveLog::SYSTEM_CNA_ADMIN);

            return responseSuccess();

        } catch (\Exception $e) {
            DB::rollBack();
            return responseFail($e->getMessage());
        }

    }

    /**
     * 获取用户权限
     * @param Request $request
     * @return void
     */
    public function userPower(Request $request)
    {
        $user = $request->attributes->get('user');
        $user_id = $user['profileID'];

        $powerCode = [];  // 权限码
        $powerID = []; // 权限菜单ID
        $powerInfo = []; // 权限菜单
        $powerUserData = PowerUserDataModel::where('powerProfileID', $user_id)->get();
        if ($powerUserData) {
            foreach ($powerUserData as $value) {
                $powerCode[] = $value->powerActionCode;
                $powerID[] = $value->powerID;
            }
            // 权限菜单
            $powerInfo = PowerInfoModel::query()->whereIn('powerID', $powerID)->orderBy('powerOrder', 'asc')->get();

        }

        return responseSuccess(compact('powerCode', 'powerInfo'));

    }
}