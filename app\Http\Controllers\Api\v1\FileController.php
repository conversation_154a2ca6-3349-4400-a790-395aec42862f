<?php

namespace App\Http\Controllers\Api\v1;

use App\Http\Controllers\Controller;
use App\Services\OssService;
use Illuminate\Http\Request;

/**
 * 统一处理行政后台端的文件
 */
class FileController extends Controller
{
    /**
     * 文件资源（预览文件）
     */
    public function fileResource(Request $request)
    {
        $request->validate([
            'type' => 'required|string|in:local,remote',
            'path' => 'required|string',
        ]);

        if ($request->type == 'local') {
            $fileUrl = viewFileUrl($request->path);
        } else {
            $fileUrl = OssService::link($request->path);
        }

        $fileContent = file_get_contents($fileUrl);

        if (!$fileContent) {
            return responseFail(__('info no exist'));
        }

        // 获取文件MIME类型
        $finfo = new \finfo(FILEINFO_MIME_TYPE);
        $mimeType = $finfo->buffer($fileContent);

        return response()->stream(function () use ($fileContent) {
            echo $fileContent;
        }, 200, ['Content-Type' => $mimeType]);
    }

    /**
     * 下载文件
     */
    public function streamDownload(Request $request)
    {
        $request->validate([
            'type' => 'required|string|in:local,remote',
            'file_path' => 'required|string',
        ]);

        if ($request->type == 'local') {
            // local 上传文件的basename
            $fileUrl = viewFileUrl($request->file_path);
        } else {
            $fileUrl = OssService::link($request->file_path);
        }

        // 获取文件名
        $fileName = basename($fileUrl);

        // 读取文件
        $client = new \GuzzleHttp\Client();
        $response = $client->get($fileUrl, ['stream' => true]);

        // 流式返回文件内容
        return response()->streamDownload(function () use ($response) {
            $body = $response->getBody();
            while (!$body->eof()) {
                echo $body->read(1024);
            }
        }, $fileName);
    }
}
