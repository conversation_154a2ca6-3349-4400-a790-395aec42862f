<?php

namespace App\Rules\User;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class CheckBirthDateRule implements ValidationRule
{
    const PATTERN = '/^(19|20)\d{2}-((0[1-9])|(10|11|12))-(([0-2][1-9])|10|20|30|31)$/';

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (!preg_match(self::PATTERN, $value)) {
            $fail(__('birthdate error'));
        }
        if (strtotime($value) > strtotime(date("Y-m-d"))) {
            $fail(__('birthdate error'));
        }
    }
}
