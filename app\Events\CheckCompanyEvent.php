<?php

namespace App\Events;

use App\Models\CompanyModel;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class CheckCompanyEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $companyInfo;
    public $status;

    /**
     * Create a new event instance.
     */
    public function __construct(CompanyModel $companyInfo, $status)
    {
        //
        $this->companyInfo = $companyInfo;
        $this->status = $status;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('channel-name'),
        ];
    }
}
