<?php

use App\Http\Controllers\Api\v1\Pay\PayController;
use App\Http\Controllers\Api\v1\ProfileBusiness\ProcessController;
use App\Http\Controllers\Api\v1\ProfileUpgrade\ProfileUpgrade;
use App\Http\Controllers\Api\v1\Activity\ActivityController;
use App\Http\Controllers\Api\v1\Activity\ActivitySignupController;
use App\Http\Controllers\Api\v1\Bill\BillController;
use App\Http\Controllers\Api\v1\Captcha\CaptchaController;
use App\Http\Controllers\Api\v1\Community\CommunityController;
use App\Http\Controllers\Api\v1\Company\CompanyController;
use App\Http\Controllers\Api\v1\Company\ProgressOneController;
use App\Http\Controllers\Api\v1\Company\ProgressTwoController;
use App\Http\Controllers\Api\v1\Config\ConfigController;
use App\Http\Controllers\Api\v1\Document\DocumentController;
use App\Http\Controllers\Api\v1\DocumentFolders\DocumentFoldersController;
use App\Http\Controllers\Api\v1\Firm\FirmController;
use App\Http\Controllers\Api\v1\ICBC\ICBCController;
use App\Http\Controllers\Api\v1\IntroduceProcess\IntroduceProcessController;
use App\Http\Controllers\Api\v1\Login\LoginController;
use App\Http\Controllers\Api\v1\Payment\PaymentController;
use App\Http\Controllers\Api\v1\ProfileContract\ProfileContractController;
use App\Http\Controllers\Api\v1\Setting\NotificationController;
use App\Http\Controllers\Api\v1\Notification\NotificationController as NotifyController;
use App\Http\Controllers\Api\v1\Project\ProjectController;
use App\Http\Controllers\Api\v1\Register\RegisterController;
use App\Http\Controllers\Api\v1\Setting\LanguageAreaController;
use App\Http\Controllers\Api\v1\Password\PasswordController;
use App\Http\Controllers\Api\v1\User\ExperienceController;
use App\Http\Controllers\Api\v1\VisitorLogin\VisitorLoginController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\v1\User\UserController;
use App\Http\Controllers\Api\v1\User\ProfessionalController;
use App\Http\Controllers\Api\v1\User\SkillController;
use App\Http\Controllers\Api\v1\Wechat\WechatController;
use App\Http\Controllers\Api\v1\Company\ProgressThreeController;
use App\Http\Controllers\Api\v1\Company\ProgressFourController;
use App\Http\Controllers\Api\v1\Calendar\CalendarController;
use App\Http\Controllers\Api\v1\Click\ClickController;
use App\Http\Controllers\Api\v1\Setting\HelpController;
use App\Http\Controllers\Api\v1\Team\TeamController;
use App\Http\Controllers\Api\v1\Setting\ActiveLogController;
use App\Http\Controllers\Api\v1\Setting\LoginLogController;
use App\Http\Controllers\Api\v1\Ai\AiController;
use App\Http\Controllers\Api\v1\AiMan\AiManController;
use App\Http\Controllers\Api\v1\BusinessCase\BusinessCaseController;
use App\Http\Controllers\Api\v1\Qualification\QualificationController;
use App\Http\Controllers\Api\v1\Company\GspController;
use App\Http\Controllers\Api\v1\Interview\InterviewController;
use App\Http\Controllers\Api\v1\Qrcode\QrcodeController;
use App\Http\Controllers\Api\v1\Stock\StockController;
use App\Http\Controllers\Api\v1\Partner\PartnerController;

Route::group(['prefix' => 'v1', 'middleware' => ['api', 'throttle:1200,1']], function () {

    /*--- 图形验证码 ---*/
    Route::get('captcha', [CaptchaController::class, 'captcha'])->middleware('throttle:120,1');

    /*--- 用户登录 ---*/
    Route::group(['prefix' => 'login'], function () {
        Route::post('/', [LoginController::class, 'login']); //登录
        Route::post('logout', [LoginController::class, 'logout'])->middleware('api.auth'); //登出
        Route::post('sendCode', [LoginController::class, 'sendCode']); //发送短信验证码
    });

    /*--- 注册 ---*/
    Route::group(['prefix' => 'register'], function () {
        Route::post('/', [RegisterController::class, 'register']); //注册第一步
        Route::post('confirm', [RegisterController::class, 'registerConfirm']); //注册第二步
        Route::post('sendEmail', [RegisterController::class, 'sendEmail']); //发送注册邮件验证码
        Route::post('verifyEmail', [RegisterController::class, 'verifyEmail']); //验证邮箱
        Route::post('sendSms', [RegisterController::class, 'sendSms']); //发送短信
        Route::post('verifySms', [RegisterController::class, 'verifySms']); //验证短信
        Route::group(['middleware' => 'api.auth:register_setting'], function () {
            Route::post('setLanguage', [RegisterController::class, 'setLanguage']); //语言设置
            Route::post('setSecurity', [RegisterController::class, 'setSecurity']); //安全设置
            Route::post('setProfile', [RegisterController::class, 'setProfile']); //用户设置
        });
        Route::post('check', [RegisterController::class, 'registerCheck']); // 注册检查手机或邮箱是否存在
    });

    /*--- 用户 ---*/
    Route::group(['prefix' => 'user', 'middleware' => 'api.auth'], function () {
        Route::post('/', [UserController::class, 'info']); //获取个人信息
        Route::get('/info', [UserController::class, 'info']); //获取个人信息
        Route::post('profile', [UserController::class, 'profile']); //获取个人信息
        Route::post('updateProfile', [UserController::class, 'updateProfile']); //修改个人信息
        Route::post('updateAvatar', [UserController::class, 'updateUserAvatar']); //修改头像
        Route::post('changeEmail', [UserController::class, 'changeMailSendCode']); //修改邮箱发送验证码
        Route::post('changeEmailVerify', [UserController::class, 'changeMailVerifyCode']); //修改邮箱提交验证码
        Route::post('changePhone', [UserController::class, 'changePhoneSendCode']); //修改手机号发送验证码
        Route::post('changePhoneVerify', [UserController::class, 'changePhoneVerifyCode']); //修改手机号提交验证码
        Route::post('uploadNric', [UserController::class, 'uploadNric']); //上传身份证
        Route::get('registerSetting', [UserController::class, 'registerSetting']); //获取初始设置完成情况
        Route::post('uploadPassportCn', [UserController::class, 'uploadPassportCn']); //上传中国护照
        Route::post('uploadBankCardCn', [UserController::class, 'uploadBankCardCn']); //上传国内银行卡
        Route::post('confirmBankCard', [UserController::class, 'confirmBankCard']); // 确认银行卡
        Route::post('isPartnerCode', [UserController::class, 'isPartnerCode'])->withoutMiddleware('api.auth'); //合伙人编号是否存在
    });

    /*--- 密码 ---*/
    Route::group(['prefix' => 'password'], function () {
        Route::group(['prefix' => 'forgotPsw'], function () {
            Route::post('sendEmail', [PasswordController::class, 'sendForgotPasswordEmail']); //发送重置密码邮件
            Route::post('reset', [PasswordController::class, 'resetPassword']); //重置密码
        });
        Route::post('update', [PasswordController::class, 'updatePassword'])->middleware('api.auth'); //修改密码
    });

    /*--- 用户专业 ---*/
    Route::get('professionals', [ProfessionalController::class, 'professionalQualifications']); //专业资格列表
    Route::get('experiences', [ExperienceController::class, 'experiences']); //经验列表
    Route::get('skills', [SkillController::class, 'skills']); //技能列表

    /*--- 通知 ---*/
    Route::get('notifications', [NotifyController::class, 'notificationList'])->middleware('api.auth'); //用户通知列表
    Route::group(['prefix' => 'notification', 'middleware' => 'api.auth'], function () {
       Route::post('readAll', [NotifyController::class, 'readAll']); //已读全部通知
       Route::post('readSingle', [NotifyController::class, 'readSingle']); //已读一条通知
       Route::get('notificationStatusNumber', [NotifyController::class, 'notificationStatusNumber']); //获取用户通知数量
       Route::post('del', [NotifyController::class, 'del']); //删除
    });

    /*--- 资料 ---*/
    Route::get('documents', [DocumentController::class, 'documentList'])->middleware('api.auth'); //资料列表
    Route::group(['prefix' => 'document'], function () {
        Route::post('generateKey', [DocumentController::class, 'generateDocumentToken'])->middleware('api.auth'); //获取资料文件验证
        Route::get('preview/{token}', [DocumentController::class, 'previewDocument']); //获取资料文件验证
        Route::get('download/{token}', [DocumentController::class, 'downloadDocument']); //获取资料文件验证
     });

    /*----资料文件夹-----*/
    Route::group(['prefix' => 'documentFolders', 'middleware' => 'api.auth'], function () {
        Route::get('index', [DocumentFoldersController::class, 'index']); // 获取所有文件夹
        Route::get('files/{id}', [DocumentFoldersController::class, 'files']); // 获取文件夹下文件
    });


    /*--- 回酬 ---*/
    Route::get('bills', [BillController::class, 'billList'])->middleware('api.auth'); //回酬列表

    /*--- 公共配置 ---*/
    Route::get('configurations', [ConfigController::class, 'configurations']); //配置列表
    Route::group(['prefix' => 'config'], function () {
        Route::get('structure', [ConfigController::class, 'structure']); //多语言字典
        Route::get('countryList', [ConfigController::class, 'countryList']); //国家列表
        Route::get('mobilePrefixList', [ConfigController::class, 'mobilePrefixList']); //手机区号列表
        Route::get('bankList', [ConfigController::class, 'bankList']); //银行列表
        Route::get('countries', [ConfigController::class, 'countries']); //国家列表
        Route::get('divisionCnList', [ConfigController::class, 'divisionCnList']); //行政区划列表
        Route::get('industryList', [ConfigController::class, 'industryList']); //行业列表
        Route::get('globalLanguages', [ConfigController::class, 'globalLanguages']);// 语言能力
        Route::get('professionList', [ConfigController::class, 'professionList']);// 职业列表
    });

    /*--- 设置 ---*/
    Route::group(['prefix' => 'setting'], function () {
        Route::post('languageArea/update', [LanguageAreaController::class, 'update'])->middleware('api.auth'); //设置语言地区
        Route::group(['prefix' => 'notification'], function () {
            Route::get('notifyType', [NotificationController::class, 'notifyType']);
            Route::post('update', [NotificationController::class, 'update'])->middleware('api.auth');
        });
        Route::group(['prefix' => 'help'], function () {
            Route::post('generateDocToken', [HelpController::class, 'generateDocToken'])->middleware('api.auth'); //条款获取token
            Route::get('previewDoc/{token}', [HelpController::class, 'previewDoc']); //查看条款
        });
        Route::post('activeLog', [ActiveLogController::class, 'list'])->middleware('api.auth'); //获取活动日志
        Route::group(['prefix' => 'loginLog', 'middleware' => 'api.auth'], function () {
            Route::post('list', [LoginLogController::class, 'list']); //登入足迹列表
            Route::post('quit', [LoginLogController::class, 'quit']); //登入足迹退出
        });
    });

    /*--- 项目 ---*/
    Route::get('projects', [CompanyController::class, 'projects'])->middleware('api.auth'); //项目列表
    Route::group(['prefix' => 'company'], function () {
        Route::get('categories', [CompanyController::class, 'companyCategoryList']);
        Route::get('projects', [ProjectController::class, 'projectList']);
        Route::get('projects/file', [ProjectController::class, 'fileList']); // 项目相关文件
        Route::post('projects/generateFileToken', [ProjectController::class, 'generateFileToken']);
        Route::get('projects/previewFile/{token}', [ProjectController::class, 'previewFile']); //查看条款
        Route::post('createCompany', [CompanyController::class, 'createCompany'])->middleware('api.auth');
        Route::get('getCompanyInfo', [CompanyController::class, 'getCompanyInfo'])->middleware('api.auth');

        Route::group(['prefix' => 'progressOne'], function () {
            Route::get('valueAddedServices', [ProjectController::class, 'serviceList']);
            Route::get('download/{token}', [ProgressOneController::class, 'download']);
            Route::get('preview/{token}', [ProgressOneController::class, 'preview']);
            Route::group(['middleware' => 'api.auth'], function () {
                Route::get('getProgressOneInfo', [ProgressOneController::class, 'getProgressOneInfo']);
                Route::post('generateDownloadFileKey', [ProgressOneController::class, 'generateDownloadFileKey']);
                Route::post('generateViewFileKey', [ProgressOneController::class, 'generateViewFileKey']);
                Route::post('commit', [ProgressOneController::class, 'commit']);
            });
        });

        Route::get('categories', [CompanyController::class, 'companyCategoryList']);

        Route::group(['prefix' => 'progressTwo'], function () {
            Route::get('payments', [ProgressTwoController::class, 'payments']);
            Route::get('banks', [ProgressTwoController::class, 'banks']);
            Route::get('preview/{token}', [ProgressTwoController::class, 'preview']);
            Route::group(['middleware' => 'api.auth'], function () {
                Route::post('commit', [ProgressTwoController::class, 'commitProgressTwo']);
                Route::get('getProgressTwoInfo', [ProgressTwoController::class, 'getProgressTwoInfo']);
                Route::post('generateViewFileKey', [ProgressTwoController::class, 'generateViewFileKey']);
            });
        });

        Route::group(['prefix' => 'progressThree', 'middleware' => 'api.auth'], function () {
            Route::get('getProgressThreeInfo', [ProgressThreeController::class, 'getProgressThreeInfo']);
        });

        Route::group(['prefix' => 'progressFour'], function () {
            Route::get('download/{token}', [ProgressFourController::class, 'download']);
            Route::get('preview/{token}', [ProgressFourController::class, 'preview']);
            Route::group(['middleware' => 'api.auth'], function () {
                Route::get('getProgressFourInfo', [ProgressFourController::class, 'getProgressFourInfo']);
                Route::post('commit', [ProgressFourController::class, 'progressFourCommit']);
                Route::post('upload', [ProgressFourController::class, 'upload']);
                Route::post('generateFileKey', [ProgressFourController::class, 'generateFileKey']);
            });
        });

    });

    /*--- 微信 ---*/
    Route::group(['prefix' => 'wechat'], function () {
        Route::post('transaction', [WechatController::class, 'transaction'])->middleware('throttle:1000,1');
        Route::post('notify', [WechatController::class, 'notify']);
        Route::post('native', [WechatController::class, 'wechatNative']);
        Route::post('jsapi', [WechatController::class, 'wechatJSApi']);
        Route::post('oauth', [WechatController::class, 'oauth']); //微信公众号网页授权
        Route::post('h5', [WechatController::class, 'h5']); // H5 下单
        Route::post('notifyTest', [WechatController::class, 'notifyTest']);
        Route::get('checkRegisterOrder', [WechatController::class, 'checkRegisterOrder']); //查询注册支付订单
    });

    /*---工行加盟入驻收费---*/
    Route::group(['prefix' => 'icbc'], function () {
        Route::get('createPayToken', [ICBCController::class, 'createPayToken']);
        Route::post('native', [ICBCController::class, 'registerPayQRCode'])->name('registerPayCode'); // 生成支付二维码
        Route::post('notify', [ICBCController::class, 'notify']);
        Route::get('checkRegisterOrder', [ICBCController::class, 'checkRegisterOrder']); //查询注册支付订单
    });

    /*--- 社区 ---*/
    Route::group(['prefix' => 'communities', 'middleware' => 'api.auth'], function () {
        Route::get('list', [CommunityController::class, 'list']);
        Route::get('/', [CommunityController::class, 'index']);
        Route::get('/{id}', [CommunityController::class, 'show']);
    });

    /*--- 日历 ---*/
    Route::group(['prefix' => 'calendar', 'middleware' => 'api.auth'], function () {
        Route::get('list', [CalendarController::class, 'list']);
        Route::post('add', [CalendarController::class, 'add']);
        Route::post('del', [CalendarController::class, 'del']);
    });

    /*--- 点击数 ---*/
    Route::post('click', [ClickController::class, 'log'])->middleware('api.auth'); //记录点击

    /*--- 三三制 ---*/
    Route::group(['prefix' => 'team'], function () {
        Route::post('checkPhone', [TeamController::class, 'checkPhone']);
        Route::post('add', [TeamController::class, 'add']);
        Route::post('generateDocToken', [TeamController::class, 'generateDocToken']); //文档获取token
        Route::get('downloadDoc/{token}', [TeamController::class, 'downloadDoc']); //下载文件
        Route::post('inviteToken', [TeamController::class, 'inviteToken'])->middleware('api.auth'); //邀请合伙人获取token
        Route::post('inviteSend', [TeamController::class, 'inviteSend'])->middleware('api.auth'); //邀请合伙人发送邮件短信
        Route::post('getRecommendInfo', [TeamController::class, 'getRecommendInfo']); //获取推荐人信息
        Route::get('list', [TeamController::class, 'list'])->middleware('api.auth'); //团队列表
        Route::get('inviteTopCode', [TeamController::class, 'inviteTopCode']); //邀请成为最高级的营运总裁
        Route::get('rankContent', [TeamController::class, 'getTeamRankContent']); // 获取合伙人三三制开设事务所页面设置文案
    });

    /*--- AI助理 ---*/
    Route::get('ai', [AiController::class, 'link'])->middleware('api.auth'); //获取AI助理跳转链接

    /*---合伙人合同---*/
    Route::group(['prefix' => 'profileContract'], function () {
        Route::post('create', [ProfileContractController::class, 'save'])->middleware('api.auth');//保存合同签名
        Route::get('signature', [ProfileContractController::class, 'signature'])->middleware('api.auth');//获取合同签名
        Route::post('generateContractTemplateToken', [ProfileContractController::class, 'generateContractTemplateToken'])->middleware('api.auth');
        Route::get('previewContractTemplate/{token}', [ProfileContractController::class, 'previewContractTemplate']);
        Route::get('previewSignatureContract/{token}', [ProfileContractController::class, 'previewSignatureContract']);
        Route::get('isSign', [ProfileContractController::class, 'isSign'])->middleware('api.auth');
    });

    /*--- 履历 ---*/
    Route::group(['prefix' => 'experience'], function () {
        Route::get('title', [ExperienceController::class, 'title']); //履历标题列表
        Route::get('list', [ExperienceController::class, 'list']); //用户履历列表
        Route::post('edit', [ExperienceController::class, 'edit'])->middleware('api.auth'); //新增或修改
        Route::post('del', [ExperienceController::class, 'del'])->middleware('api.auth'); //删除
        Route::post('generateDocToken', [ExperienceController::class, 'generateDocToken']); //获取履历文件token
        Route::get('previewDoc/{token}', [ExperienceController::class, 'previewDoc']); //查看履历文件
    });

    /*----账单----*/
    Route::group(['prefix' => 'payment', 'middleware' => 'api.auth'], function () {
        Route::get('in', [PaymentController::class, 'in']); //收入列表
        Route::get('out', [PaymentController::class, 'out']); //支出列表
        Route::get('income', [PaymentController::class, 'income']); //合伙人分成明细
        Route::post('uploadInvoice', [PaymentController::class, 'uploadInvoice']); //合伙人上传发票
        Route::get('index', [PaymentController::class, 'index']); // 列表记录
        Route::get('total', [PaymentController::class, 'total']); // 统计流水
    });

    /*--- 股份 ---*/
    Route::group(['prefix' => 'stock', 'middleware' => 'api.auth'], function () {
        Route::get('isApply', [StockController::class, 'isApply']); //是否已申请
        Route::post('apply', [StockController::class, 'apply']); //申请
    });

    /*--- 数字人 ---*/
    Route::group(['prefix' => 'aiMan', 'middleware' => 'api.auth'], function () {
        Route::post('upload', [AiManController::class, 'upload']); //上传文件
        Route::get('videoList', [AiManController::class, 'videoList']); //ai视频列表
        Route::get('uploadRes', [AiManController::class, 'uploadRes']); //上传文件个人纪录
        Route::delete('delete-file', [AiManController::class, 'deleteFile']); //删除文件
    });

    /*----介绍流程----*/
    Route::group(['prefix' => 'introduceProcess', 'middleware' => 'api.auth'], function () {
        Route::post('done', [IntroduceProcessController::class, 'done']); //提交完成
        Route::get('check', [IntroduceProcessController::class, 'check']); //查看流程
    });

    /*----联号事务所----*/
    Route::group(['prefix' => 'firm', 'middleware' => 'api.auth'], function () {
        Route::post('add', [FirmController::class, 'add']); //资料申请
        Route::get('status', [FirmController::class, 'status']); //申请状态
        Route::get('list', [FirmController::class, 'list']); //列表
        Route::get('info', [FirmController::class, 'info']); //申请信息
        Route::post('setAdmin', [FirmController::class, 'setAdmin']); //设置管理员
        Route::post('quit', [FirmController::class, 'quit']); //踢出成员
    });

    /*--- 项目-绿智地球 ---*/
    Route::group(['prefix' => 'gsp'], function () {
        Route::get('formOption', [GspController::class, 'formOption']); //表格选项列表
        Route::get('previewFile/{token}', [GspController::class, 'previewFile'])->middleware('throttle:1000,1'); //预览文件
        Route::get('downloadFile/{token}', [GspController::class, 'downloadFile']); //下载文件
        Route::post('company/sendCode', [GspController::class, 'sendCode']); // 手机验证码
        Route::post('company/login', [GspController::class, 'companyLogin']); // 企业用户登录

        Route::group(['middleware' => 'api.auth'], function () {
            Route::post('apply', [GspController::class, 'apply']); //预审表格(保存草稿)
            Route::post('createForm', [GspController::class, 'createForm']);// 生成预审表格
            Route::get('contractCheck', [GspController::class, 'contractCheck']);// 提交合同审核
            Route::post('uploadReport', [GspController::class, 'uploadReport']); //上传尽调文件
            Route::post('applyDraft', [GspController::class, 'applyDraft']); //尽调报告(保存草稿)
            Route::post('applyReport', [GspController::class, 'applyReport']); //提交尽调报告
        });

        Route::group(['middleware' => 'gspApi.auth'], function () {
            Route::get('formCheck', [GspController::class, 'formCheck']);// 提交预审表格
            Route::get('applyDetail', [GspController::class, 'applyDetail']);// 查看预审表格数据
            Route::post('uploadForm', [GspController::class, 'uploadForm']); //上传预审表格
            Route::get('printContract', [GspController::class, 'printContract']);// 打印合同
            Route::post('uploadContract', [GspController::class, 'uploadContract']);// 上传合同
            Route::get('reportDetail', [GspController::class, 'reportDetail']); //查看尽调报告
            Route::get('sendEmail', [GspController::class, 'sendEmail']); //再次发邮件通知

            Route::post('payOne', [GspController::class,'createPayOneQRcode']); // 生成付款第一步二维码
            Route::post('payTwo', [GspController::class,'createPayTwoQRcode']); // 生成付款第一步二维码
            Route::post('payThree', [GspController::class,'createPayThreeQRcode']); // 生成付款第三步二维码
            Route::post('payOneReturn', [GspController::class, 'payOneReturn']);// 第一步要求退款
            Route::post('checkPayStatus', [GspController::class, 'checkPayStatus']);// 查询是否支付成功

            Route::post('generateGspFileToken', [GspController::class, 'generateGspFileToken'])->middleware('throttle:1000,1'); //获取文件验证
            Route::post('checkResult', [GspController::class, 'checkResult']); //审核结果
            Route::get('progress', [GspController::class, 'progress']); // 当前进度状态
        });


    });

    Route::group(['prefix' => 'gsp'], function () {
        Route::post('notifyPayOne', [GspController::class,'notifyPayOne']); // 绿智地球付款第一步成功回调
        Route::post('notifyPayTwo', [GspController::class,'notifyPayTwo']); // 绿智地球付款第二步成功回调
        Route::post('notifyPayThree', [GspController::class,'notifyPayThree']); // 绿智地球付款第三步成功回调
    });

    /*--- 名片 ---*/
    Route::group(['prefix' => 'userCard'], function () {
        Route::get('profile', [UserController::class, 'cardProfile']); //获取个人信息
    });

    /*--- 二维码 ---*/
    Route::group(['prefix' => 'qrcode'], function () {
        Route::post('make', [QrcodeController::class, 'make']); //生成二维码
    });

    /*--- 贵宾来访登录---*/
    Route::group(['prefix' => 'visitorLogin'], function () {
        Route::post('create', [VisitorLoginController::class, 'create']); // 提交来访信息
        Route::post('apply', [VisitorLoginController::class, 'apply']); // 到访申请表格
        Route::post('signin', [VisitorLoginController::class, 'signin']); // 贵宾来访登记
        Route::post('report', [VisitorLoginController::class, 'report']); // 会谈结果报告
    });

    /*----活动报名登记--------*/
    Route::group(['prefix' => 'activity'], function () {
        Route::get('info', [ActivityController::class, 'info']); // 活动信息
        Route::post('signup', [ActivitySignupController::class, 'signup']); // 活动报名
        Route::get('detail/{token}', [ActivitySignupController::class, 'detail']); // 查看报名信息
    });



    /*--- 专业资质证明 ---*/
    Route::group(['prefix' => 'qualification', 'middleware' => 'api.auth'], function () {
        Route::get('info', [QualificationController::class, 'info']); //信息
        Route::post('edit', [QualificationController::class, 'edit']); //编辑
        Route::post('batchEdit', [QualificationController::class, 'batchEdit']); //批量编辑
    });

    /*--- 业务案例说明 ---*/
    Route::group(['prefix' => 'businessCase', 'middleware' => 'api.auth'], function () {
        Route::get('info', [BusinessCaseController::class, 'info']); //信息
        Route::post('edit', [BusinessCaseController::class, 'edit']); //编辑
        Route::post('batchEdit', [BusinessCaseController::class, 'batchEdit']); //批量编辑
        Route::delete('del/{id}', [BusinessCaseController::class, 'delete']); //编辑
    });

    /*--- 面试 ---*/
    Route::group(['prefix' => 'interview', 'middleware' => 'api.auth'], function () {
        Route::get('status', [InterviewController::class, 'status']); //状态
        Route::get('info', [InterviewController::class, 'info']); //详情
        Route::post('comment', [InterviewController::class, 'comment']); //评价
    });

    /*--- 合伙人管理 ---*/
    Route::group(['prefix' => 'partner', 'middleware' => 'api.auth'], function () {
        Route::get('list', [PartnerController::class, 'list']); //列表
    });

    /*--- 合伙人晋级题目 ---*/
    Route::group(['prefix' => 'profileBusiness'], function () {
        Route::group(['middleware' => 'api.auth'], function () {
            Route::get('/', [ProcessController::class, 'index']); //列表
            Route::put('withdraw', [ProcessController::class, 'withdraw']); //新增进程
            //获取所有业务清单
            Route::get('getProjectCode', [ProcessController::class, 'getProjectCode']);
        });
        Route::group(['middleware' => 'api.auth.optional'], function () {
            Route::post('create', [ProcessController::class, 'create']); //新增进程
            Route::post('check', [ProcessController::class, 'check']); //查询进程进度
        });

        // 独立的check接口，不强制要求认证
    });

    /*--- 合伙人晋级题目 ---*/
    Route::group(['prefix' => 'upgrade', 'middleware' => 'api.auth'], function () {
        Route::get('menu', [ProfileUpgrade::class, 'getMenu']);//流程选项菜单
        Route::get('examQuestions', [ProfileUpgrade::class, 'examQuestions']); //题获取目
        Route::put('submitExam', [ProfileUpgrade::class, 'submitExam']);//测试提交
    });

    /*--- 综合获取支付业务二维码 + 回调 ---*/
    Route::group(['prefix' => 'pay'], function () {
        Route::group(['middleware' => 'api.auth'], function () {
            Route::get('createToken/{id}', [PayController::class, 'createToken']);
            Route::get('getProjectCode', [PayController::class, 'getProjectCode']);
        });
        Route::group(['middleware' => 'api.auth.optional'], function () {
            Route::post('getQRCode', [PayController::class, 'getQRCode']);
        });
        Route::post('notify', [PayController::class, 'notify'])->name('payNotify');
    });
});