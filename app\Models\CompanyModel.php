<?php

namespace App\Models;

use App\Exceptions\DefaultException;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class CompanyModel extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $table = 'cna_company';
    protected $guarded = [];
    protected $primaryKey = 'companyID';

    public function projectCategory()
    {
        return $this->belongsTo(ProjectCategoryModel::class, 'companyProjectID', 'projectCategoriesID');
    }

    public function getRecord($id)
    {
        return self::select(
            'companyID',
            'companyProjectID',
            'companyName',
            'companyRegisterCode',
            'companyCategoriesID',
            'companyCountryID',
            'companyEmail',
            'companyMobilePrefixID',
            'companyMobile',
            'companyProfileID',
            'companyState',
            'companyProgressState',
            'companyReason'
        )->where('companyID', $id)->first();
    }
    
    /**
     * Method 公司验证
     *
     * @param $companyID $companyID [公司ID]
     *
     * @return void
     */
    public function companyVerify($companyID)
    {
        $companyID = intval($companyID);

        if (!$companyID) {
            throw new DefaultException('非法操作');
        }
        if (!$this->getRecord($companyID)) {
            throw new DefaultException('非法操作');
        }
    }

    public function verificationUserCompany($companyID, $userID)
    {
        $companyID = intval($companyID);

        if (!$companyID) {
            throw new DefaultException();
        }
        
        $res = self::where('companyID', $companyID)->where('companyProfileID', $userID)->first();
        if (!$res) {
            throw new DefaultException();
        }

        return $res;
    }
}
