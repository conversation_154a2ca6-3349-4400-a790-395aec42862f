<?php

namespace App\Services;

use App\Models\ProfileInfoModel;
use Firebase\JWT\JWT;
use Illuminate\Support\Facades\Http;

class AiService
{
    const API_DOMAIN = 'https://api.testai.corporate-advisory.cn'; //api域名
    const GEN_TOKEN_PATH = '/api/v1/platform/generateToken'; //生成token

    public static function generateToken($userProfile)
    {
        $url = self::API_DOMAIN . self::GEN_TOKEN_PATH;
        $avatar = url(storageUrl($userProfile['profileAvatar'] ?: ProfileInfoModel::AVATAR_PATH));
        $payload = [
            'action' => 'platform',
            'platformUserId' => $userProfile['uuid'],
            'platformUserAvatar' => $avatar,
            'platformUserNickname' => $userProfile['profileName'],
            'platformUserEmail' => $userProfile['profileEmail'],
        ];
        $platformToken = JWT::encode($payload, env('AI_ASSISTANT_KEY'), 'HS256');
        $postData = [
            'platform' => '928c853e-a9f0-4344-9418-c523474f0314',
            'platformToken' => $platformToken,
        ];
        $response = Http::post($url, $postData);
        if ($response->successful()) {
            $res = $response->json();
            return $res['data']['token'] ?? false;
        }
        return false;
    }
}