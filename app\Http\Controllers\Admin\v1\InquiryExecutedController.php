<?php
namespace App\Http\Controllers\Admin\v1;

use App\Http\Controllers\Controller;
use App\Models\ActiveLog;
use App\Models\CompanyModel;
use App\Models\InquiryExecutedModel;
use App\Models\InquiryReportModel;
use App\Models\InquiryTrademarkModel;
use App\Services\OssService;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Symfony\Component\HttpFoundation\Request;

class InquiryExecutedController extends Controller
{
    /**
     * 添加执行记录信息
     * @param Request $request
     * @return void
     */
    public function store(Request $request)
    {
        $param = $request->all();

        $result = InquiryExecutedModel::query()->create([
            'name'                  => $param['name'],
            'id_card'               => $param['id_card'],
            'court_name'            => $param['court_name'],
            'area_name'             => $param['area_name'],
            'case_code'             => $param['case_code'],
            'duty'                  => $param['duty'],
            'performance'           => $param['performance'],
            'disrupt_type'          => $param['disrupt_type'],
            'publish_date'          => $param['publish_date'],
        ]);

        if ($result) {
            return responseSuccess();
        } else {
            return responseFail(__('save failed'));
        }

    }
}