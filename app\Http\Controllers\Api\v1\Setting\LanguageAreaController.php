<?php

namespace App\Http\Controllers\Api\v1\Setting;

use App\Http\Controllers\Controller;
use App\Http\Requests\Setting\LanguageArea\UpdateRequest;
use App\Models\ActiveLog;
use App\Models\CurrencyModel;
use App\Models\DateModel;
use App\Models\LanguageModel;
use App\Models\TimeModel;
use App\Models\TimezoneModel;
use App\Models\Setting;
use App\Models\StructureModel;

class LanguageAreaController extends Controller
{
    public function configList()
    {
        $languageModel = new LanguageModel;
        $timezoneModel = new TimezoneModel;
        $currencyModel = new CurrencyModel;
        $dateModel = new DateModel;
        $timeModel = new TimeModel;

        $languageList = $languageModel->getData();
        $timezoneList = $timezoneModel->getData(); 
        $currencyList = $currencyModel->getData(); 
        $dateFormatList = $dateModel->getData();
        $timeFormatList = $timeModel->getData();

        $compact = compact('languageList','timezoneList', 'currencyList', 'dateFormatList', 'timeFormatList');

        return responseSuccess($compact);
    }

    public function update(UpdateRequest $request)
    {
        $user = $request->attributes->get('user');
        $body = $request->all();
        $update = [
            'language' => $body['settingLanguage'],
            'timezone' => $body['settingTimezone'],
            'date_format' => $body['settingDateFormat'],
            'time_format' => $body['settingTimeFormat'],
            'currency' => $body['settingCurrency'],
        ];
        $res = Setting::where('profile_id', $user['profileID'])->update($update);
        if ($res) {
            //记录活动日志
            ActiveLog::log($user['profileID'], ActiveLog::ACTIVE_UPDATE, ActiveLog::API_V1_SETTING_LANGUAGEAREA_UPDATE,
                $user['profileID'], $update);
            return responseSuccess($body);
        } else {
            return responseFail();
        }
    }

    public function structureList()
    {
        $data = StructureModel::select('structureCode', 'structureEN', 'structureMS', 'structureZH', 'structureZT')->get();

        return responseSuccess($data);

    }
}
