<?php

namespace App\Http\Requests\Setting\Notification;

use Illuminate\Foundation\Http\FormRequest;

class UpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'notificationType'                => ['required'],
            'notificationEmergency'           => ['required'],
            'notificationSuspiciousOperation' => ['required'],
            'notificationSafeUpdated'         => ['required'],
            'notificationRecPrivateMsg'       => ['required'],
            'notificationImportanceUpdate'    => ['required'],
            'notificationSystemUpdate'        => ['required'],
            'notificationJoinInvestigate'     => ['required'],
        ];
    }

    public function messages()
    {
        return [
            'notificationType.required'                => '请设置通知方式',
            'notificationEmergency.required'           => '请设置',
            'notificationSuspiciousOperation.required' => '请设置',
            'notificationSafeUpdated.required'         => '请设置',
            'notificationRecPrivateMsg.required'       => '请设置',
            'notificationImportanceUpdate.required'    => '请设置',
            'notificationSystemUpdate.required'        => '请设置',
            'notificationJoinInvestigate.required'     => '请设置',
        ];
    }
}
