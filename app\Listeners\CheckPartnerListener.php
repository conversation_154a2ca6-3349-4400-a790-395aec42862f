<?php

namespace App\Listeners;

use App\Events\CheckPartnerEvent;
use App\Models\NotificationInfoModel;
use App\Models\ProfileInfoModel;
use App\Models\WechatOrderModel;
use App\Services\EmailService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Request;

class CheckPartnerListener
{
    private $emailService;
    private $notificationInfoModel;
    private $profileInfo;
    private $user;
    /**
     * Create the event listener.
     */
    public function __construct(Request $request, EmailService $emailService, NotificationInfoModel $notificationInfoModel)
    {
        //
        $this->request = $request;
        $this->emailService = $emailService;
        $this->notificationInfoModel = $notificationInfoModel;
    }

    /**
     * @param CheckPartnerEvent $event
     * @return bool|null
     */
    public function handle(CheckPartnerEvent $event): ?bool
    {
        // 合伙人信息
        $this->profileInfo = $event->profileInfo;
        $status = $event->status;

        // 操作用户ID
        $user = $this->request->attributes->get('user');
        $this->user = $user;

        if ($status == 1) {
            return $this->pass();
        } else if ($status == 2) {
            return $this->refuse();
        }

        return false;
    }

    /**
     * 审核通过
     * @return void
     */
    public function pass() {

        $profileInfo = $this->profileInfo;
        $profileInfoModel = new ProfileInfoModel();
        try {

            // 开启事务
            DB::beginTransaction();


            // 生成账单记录

            // 查询账单记录
            $wechatOrder = WechatOrderModel::where('user_id', $profileInfo['profileID'])->where('trade_state', 'SUCCESS')->first();
            if ($wechatOrder) {

                // 加盟费用(已付)
                /*$lang = empty($profileInfo['settingLanguage'])? 'ZH':strtoupper($profileInfo['settingLanguage']);
                $webStructure = StructureModel::where('structureCode', 'register.partner.fees')->first();
                BillModel::query()->create([
                    'billProfileID'   => $profileInfo['profileID'],
                    'billCompanyID'   => 0,
                    'billCompanyName' => '',
                    'billType'        => 1,
                    'billDescription' => isset($webStructure['structure'.$lang])?$webStructure['structure'.$lang]:$webStructure['structureZH'],
                    'billPaymentCode' => $wechatOrder['transaction_id'],  // 发票号是交易流水号
                    'billAmount'      => $wechatOrder['payer_total'],
                    'billPaymentType' => 2,// 1贷方付款;2借方付款
                    'billState'       => 1,
                    'createUser'      => $this->user['profileID'],
                    'createRole'      => $this->user['profileRole'],
                    'createTime'      => date('Y-m-d H:i:s'),
                ]);

                // 资格审核费(待付)
                $certStructure = StructureModel::where('structureCode', 'partner.qualification.fee')->first();
                BillModel::query()->create([
                    'billProfileID'   => $profileInfo['profileID'],
                    'billCompanyID'   => 0,
                    'billCompanyName' => '',
                    'billType'        => 2,
                    'billDescription' => isset($certStructure['structure'.$lang])?$certStructure['structure'.$lang]:$certStructure['structureZH'],
                    'billPaymentCode' => '',  // 发票号是交易流水号
                    'billAmount'      => '8000',
                    'billPaymentType' => 1,// 1贷方付款;2借方付款
                    'billState'       => 0,
                    'createUser'      => $this->user['profileID'],
                    'createRole'      => $this->user['profileRole'],
                    'createTime'      => date('Y-m-d H:i:s'),
                ]);

                // AI 办公室开户费为(待付)
                $aiStructure = StructureModel::where('structureCode', 'partner.aioffice.fee')->first();
                BillModel::query()->create([
                    'billProfileID'   => $profileInfo['profileID'],
                    'billCompanyID'   => 0,
                    'billCompanyName' => '',
                    'billType'        => 3,
                    'billDescription' => isset($aiStructure['structure'.$lang])?$aiStructure['structure'.$lang]:$aiStructure['structureZH'],
                    'billPaymentCode' => '',  // 发票号是交易流水号
                    'billAmount'      => '12000',
                    'billPaymentType' => 1,// 1贷方付款;2借方付款
                    'billState'       => 0,
                    'createUser'      => $this->user['profileID'],
                    'createRole'      => $this->user['profileRole'],
                    'createTime'      => date('Y-m-d H:i:s'),
                ]);

                // 绿智地球业务资格证(待付)
                $greenStructure = StructureModel::where('structureCode', 'partner.green_earth.fee')->first();
                BillModel::query()->create([
                    'billProfileID'   => $profileInfo['profileID'],
                    'billCompanyID'   => 0,
                    'billCompanyName' => '',
                    'billType'        => 4,
                    'billDescription' => isset($greenStructure['structure'.$lang])?$greenStructure['structure'.$lang]:$greenStructure['structureZH'],
                    'billPaymentCode' => '',  // 发票号是交易流水号
                    'billAmount'      => '15000',
                    'billPaymentType' => 1,// 1贷方付款;2借方付款
                    'billState'       => 0,
                    'createUser'      => $this->user['profileID'],
                    'createRole'      => $this->user['profileRole'],
                    'createTime'      => date('Y-m-d H:i:s'),
                ]);

                // 查询是否是管理合伙人带进来
                $pre_id = $profileInfo['pre_id'];
                $last_time = strtotime('2024-12-31 23:59:59');

                if ($pre_id > 0 && time() <= $last_time) { // 上级管理合伙人在12月31号前成功拉了一位合伙人,则免其它三张费用
                    BillModel::query()->where('billProfileID', $pre_id)
                        ->where('billCompanyID', 0)
                        ->whereIn('billType', [2,3,4])
                        ->update([
                            'billPaymentType' => 2,
                            'billState'       => 1,
                            'updated_at'      => date('Y-m-d H:i:s'),
                            'editUser'        => $this->user['profileID'],
                            'editRole'        => $this->user['profileRole'],
                        ]);
                }*/

            }

            // 更改状态
            //$password = generateRandomString(8);
            $profileInfo->profileRole = 1;
            //$profileInfo->profilePartnerCode = $profileInfoModel->createPartnerCode($profileInfo['profileAddressCountry'], 1);
            $profileInfo->editUser = $this->user['profileID'];
            $profileInfo->editRole = $this->user['profileRole'];
            $profileInfo->editTime = date('Y-m-d H:i:s');
            //$profileInfo->profilePassword = ProfileInfoModel::INITIAL_PASSWORD_PREFIX . hashPassword($password);
            $profileInfo->status = ProfileInfoModel::STATUS_ACTIVE;
            $profileInfo->save();

            Log::info($profileInfo->profilePartnerCode);
            // 站内通知
           /* $data = [
                'targetUser' =>  $profileInfo['profileID'],
            ];
            \App\Jobs\SendNotice::dispatch($data, 'PartnerPass', $this->user)->onQueue('SendNoticeJob');*/


            // 提交
            DB::commit();

            // 发送邮件
            $this->emailService->sendPasswordEmail($profileInfo['profileEmail']);

            return true;

        } catch (\Exception $e) {
            DB::rollBack();


            return false;
        }

    }

    /**
     * 审核失败
     * @return void
     */
    public function refuse() {

        $profileInfo = $this->profileInfo;

        try {

            // 开启事务
            DB::beginTransaction();

            // 更改状态
            $profileInfo->status = ProfileInfoModel::STATUS_REJECT;
            $profileInfo->save();

            // 站内通知
            $data = [
                'targetUser' =>  $profileInfo['profileID'],
            ];
            \App\Jobs\SendNotice::dispatch($data, 'PartnerRefuse', $this->user)->onQueue('SendNoticeJob');

            // 发送邮件
            $this->emailService->sendCommonEmail($profileInfo['profileEmail'], 'PartnerFailed');


            // 提交
            DB::commit();

            return true;

        } catch (\Exception $e) {
            DB::rollBack();
            // 记录日志
            Log::info($e->getMessage());

            return false;
        }

    }
}
