<?php
namespace App\Console\Commands;

use App\Models\BankCardCn;
use Illuminate\Console\Command;

class BankCardExpireNotify extends Command
{
    const NOTIFY_EVENT_CODE = 'BankCardExpireNotify';
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'bank_card_expire_notify';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '银行卡过期发送通知';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $now = strtotime('today');
        //提前1天提醒
        $data = BankCardCn::whereBetween('expire_time', [$now + 86400, $now + 86400 + 86400 - 1])
            ->pluck('profile_id')->toArray();
        //提前1周提醒
        $data1 = BankCardCn::whereBetween('expire_time', [$now + 86400*7, $now + 86400*7 + 86400 - 1])
            ->pluck('profile_id')->toArray();
        //提前2周提醒
        $data2 = BankCardCn::whereBetween('expire_time', [$now + 86400*14, $now + 86400*14 + 86400 - 1])
            ->pluck('profile_id')->toArray();
        //提前3周提醒
        $data3 = BankCardCn::whereBetween('expire_time', [$now + 86400*21, $now + 86400*21 + 86400 - 1])
            ->pluck('profile_id')->toArray();
        //提前1个月提醒
        $data4 = BankCardCn::whereBetween('expire_time', [$now + 86400*28, $now + 86400*28 + 86400 - 1])
            ->pluck('profile_id')->toArray();
        $ids = array_merge($data, $data1, $data2, $data3, $data4);
        if (empty($ids)) {
            return;
        }
        foreach ($ids as $profileId) {
            // 站内通知
            $data = [
                'targetUser' => $profileId,
            ];
            \App\Jobs\SendNotice::dispatch($data, self::NOTIFY_EVENT_CODE)->onQueue('SendNoticeJob');
        }
    }
}

