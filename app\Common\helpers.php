<?php

/**
 * Method 返回成功示例
 *
 * @param $data $data [数据集]
 * @param $message $message [信息]
 *
 * @return void
 */
function responseSuccess($data = [], $message = 'Operation Success')
{
    $json = ['status' => true, 'code' => 200, 'message' => $message];
    if ($data) {
        $json['data'] = $data;
    }

    return response()->json($json);
}

/**
 * Method 返回失败示例
 *
 * @param $message $message [数据集]
 * @param $code $code [信息]
 *
 * @return void
 */
function responseFail($message = 'Operation failed', $code = -1)
{
    $json = ['status' => false, 'code' => $code, 'message' => $message];

    return response()->json($json);
}

/**
 * Method 生成文件可访问路径
 *
 * @param $file $file [explicite description]
 *
 * @return void
 */
function viewFileUrl($file)
{
    return env('APP_URL').'/storage/'.$file;
}

function storageUrl($url)
{
    return '/storage/'.$url;
}

/**
 * Method 生成加密密码
 *
 * @param $password $password [explicite description]
 *
 * @return void
 */
function hashPassword(string $password)
{
   return hash_hmac('sha256', $password, env('JWT_KEY'));
}

function hashPasswordNoKey(string $password)
{
   return hash('sha256', $password);
}

/**
 * Method 通过国籍ID判断语言
 *
 * @param $nationalityID $nationalityID [explicite description]
 *
 * @return void
 */
function getLanguageByNationalityID($nationalityID)
{
    return $nationalityID != \App\Models\CountryModel::COUNTRY_ID_CHINA && $nationalityID ? 'en' : 'zh';
}

/**
 * Method 生成上传文件名
 * 
 * @param string $extension 扩展名
 *
 * @return string 文件名
 */
if (!function_exists('generateUploadFilename')) {
    function generateUploadFilename($extension = 'jpg')
    {
        return date('YmdHis') . rand(0000, 9999) . '.' . $extension;
    }
}

/**
 * Method 生成随机密码
 * @param $length 密码长度
 * @return string
 */
function generateRandomString($length)
{
    $characters = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $randomString = '';

    for ($i = 0; $i < $length; $i++) {
        $randomString .= $characters[rand(0, strlen($characters) - 1)];
    }

    return $randomString;
}

/**
 * Method 图片缩放
 * @param $sourceFile 源文件
 * @param $destinationFile 目标文件
 * @param $limitLen 图片限制长度
 * @param $outputImgType 图片输出格式
 * @param $quality 图片质量（jpg专用）
 * @return bool
 */
function resizeImage($sourceFile, $destinationFile, $limitLen, $outputImgType = 0, $quality = 90)
{
    list($width, $height) = getimagesize($sourceFile);
    $maxLen = max($width, $height);
    $scale = $limitLen / $maxLen;
    $newWidth = intval($width * $scale);
    $newHeight = intval($height * $scale);

    switch (exif_imagetype($sourceFile)) {
        case IMAGETYPE_JPEG:
            $source = imagecreatefromjpeg($sourceFile);
            break;
        case IMAGETYPE_PNG:
            $source = imagecreatefrompng($sourceFile);
            break;
        case IMAGETYPE_GIF:
            $source = imagecreatefromgif($sourceFile);
            break;
        default:
            return false;
    }

    $target = imagecreatetruecolor($newWidth, $newHeight);
    imagecopyresampled($target, $source, 0, 0, 0, 0, $newWidth, $newHeight, $width, $height);

    $returnImgType = $outputImgType ?: exif_imagetype($sourceFile);
    switch ($returnImgType) {
        case IMAGETYPE_JPEG:
            imagejpeg($target, $destinationFile, $quality);
            break;
        case IMAGETYPE_PNG:
            imagepng($target, $destinationFile);
            break;
        case IMAGETYPE_GIF:
            imagegif($target, $destinationFile);
            break;
        default:
            return false;
    }

    imagedestroy($source);
    imagedestroy($target);

    return true;
}

/**
 * 递归函数，用于计算子类数量
 * @param $array
 * @param $id
 * @return array|mixed
 */

function getAllChild($array,$id)
{
    $arr = array();
    foreach ($array as $v) {
        if ($v['pre_id'] == $id) {
            $arr[] = $v['profileID'];
            $arr = array_merge($arr, getAllChild($array, $v['profileID']));
        };
    };
    return $arr;
}

/**
 * 递归函数，获取某个节点的所有上级节点
 * @param int $nodeId 当前节点的ID
 * @param array $data 关联数组，存储节点及其上级的关系
 * @return array 包含所有上级节点ID的数组
 */
function getAllParents($nodeId, $data) {
    $ancestors = [];

    if (isset($data[$nodeId])) {
        $parentId = $data[$nodeId];
        if ($parentId !== null) {
            $ancestors[] = $parentId;
            $ancestors = array_merge($ancestors, getAllParents($parentId, $data));
        }
    }

    return $ancestors;
}