<?php

namespace App\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class GspRegisterEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $gsp_id;

    /**
     * Create a new event instance.
     */
    public function __construct($gsp_id)
    {
        $this->gsp_id = $gsp_id;
    }
}
