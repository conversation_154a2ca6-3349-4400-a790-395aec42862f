<?php

namespace App\Rules\Register;

use App\Models\PhoneDataModel;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use App\Models\ProfileInfoModel;

class RegConfirmPhoneRule implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (ProfileInfoModel::existsUserByPhone($value)) {
            $fail('该手机号已被使用');
        }
        $isSmsVerify = PhoneDataModel::where('phone', $value)
            ->where('phoneScene', config('sms.verification_code_scene.register_verify'))
            ->where('phoneState', 1)
            ->exists();
        if (!$isSmsVerify) {
            $fail('该手机号未通过验证');
        }
    }
}
