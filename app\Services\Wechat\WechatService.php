<?php

namespace App\Services\Wechat;

use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use App\Exceptions\WechatException;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use WeChatPay\Builder;
use WeChatPay\Crypto\Rsa;
use WeChatPay\Util\PemUtil;

class WechatService
{
    const PATH_CHECK_ORDER = 'v3/pay/transactions/out-trade-no/'; //商户订单号查询订单

    //创建微信订单号
	public function generateWechatOutTradeNo()
    {
        $number = '';
        for ($i = 0; $i < 32; $i++) {
            $number .= mt_rand(0, 9);
        }
        return $number;
    }

    //解密扫码传入数据
  	public function decodeNativePayData($token)
    {
        if (!$token) {
            throw new WechatException('TOKEN ERROR');
        }
        
        try {
        
            $decodeToken = JWT::decode($token, new Key(env('JWT_KEY'), 'HS256'));
            if (!isset($decodeToken->user_id) || !$decodeToken->user_id) {
                throw new WechatException('TOKEN ERROR');        
            }
        
            return $decodeToken;
        
        } catch (\Exception $e) {
            throw new WechatException($e->getMessage());
        }
    }

    //加密支付回调
  	public function encodeNotifyData($data)
    {
        $userId = $data['user_id'];
        $payload = [
            'exp' => time() + 3600,
            'user_id' => $userId,
            'action' => 'wechat_notify'
        ];

        $token = JWT::encode($payload, env('SECRET_KEY'), "HS256");
    
        return $token;
    }

    public function generateRandomString($length)
    {
        return bin2hex(random_bytes($length)); 
    }
    
    /**
     * Method generateJSApiSignature
     * 生成jsapi签名
     *
     * @param $prepayId $prepayId [explicite description]
     *
     * @return void
     */
    public function generateJSApiSignature($prepayId)
    {
        $appid = config('wechat.app_id');
        $time = (string)time();
        $random = Str::random(10);

        // 拼接待签名的数据
        $data = "$appid\n$time\n$random\nprepay_id=$prepayId\n";

        // 加载私钥文件
        $privateKeyFile = '/home/<USER>/htdocs/api.corporate-advisory.cn/apiclient_key.pem'; // 私钥文件路径
        $privateKey = file_get_contents($privateKeyFile);
        if (!$privateKey) {
            die("Failed to load private key file.\n");
        }

        // 转换为 OpenSSL 使用的私钥资源
        $privateKeyResource = openssl_pkey_get_private($privateKey);
        if (!$privateKeyResource) {
            die("Failed to parse private key.\n");
        }

        // 对数据进行签名
        $signature = '';
        if (!openssl_sign($data, $signature, $privateKeyResource, OPENSSL_ALGO_SHA256)) {
            die("Failed to sign data.\n");
        }

        // 释放私钥资源
        openssl_free_key($privateKeyResource);

        // 将签名转换为 Base64 格式
        $signature = base64_encode($signature);

        $compact = compact('appid', 'time', 'random', 'signature', 'prepayId');

        return $compact;
    }

    public static function checkOrder($outTradeNo)
    {
        $merchantPrivateKeyFilePath = file_get_contents(env('WECHAT_PRIVATE_KEY_PATH'));
        $merchantPrivateKeyInstance = Rsa::from($merchantPrivateKeyFilePath, Rsa::KEY_TYPE_PRIVATE);
        $platformCertificateFilePath = file_get_contents(env('WECHAT_CERT_PATH'));
        $platformPublicKeyInstance = Rsa::from($platformCertificateFilePath, Rsa::KEY_TYPE_PUBLIC);
        $platformCertificateSerial = PemUtil::parseCertificateSerialNo($platformCertificateFilePath);
        $instance = Builder::factory([
            'mchid'      => config('wechat.mchid'),
            'serial'     => config('wechat.serial'),
            'privateKey' => $merchantPrivateKeyInstance,
            'certs'      => [
                $platformCertificateSerial => $platformPublicKeyInstance,
            ]
        ]);
        try {
            $resp = $instance->chain(self::PATH_CHECK_ORDER . $outTradeNo);
            $data = json_decode($resp->getBody()->getContents(), true);
            return $data;
        } catch (\Exception $e) {
            Log::error('line:'.$e->getLine().' '.$e->getMessage());
            return false;
        }
    }
}