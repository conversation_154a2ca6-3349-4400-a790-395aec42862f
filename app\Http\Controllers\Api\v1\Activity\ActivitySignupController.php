<?php
namespace App\Http\Controllers\Api\v1\Activity;

use App\Http\Controllers\Controller;
use App\Models\Activity;
use App\Models\ActivitySignup;
use App\Models\AiManFile;
use App\Models\AiManVideo;
use App\Models\VisitorApply;
use App\Models\VisitorAttach;
use App\Models\VisitorLogin;
use App\Models\VisitorReport;
use App\Models\VisitorSignin;
use App\Rules\Mobile\CheckMobileRule;
use App\Services\OssService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class ActivitySignupController extends Controller
{

    /**
     * 活动报名
     * @param Request $request
     * @return void
     */
    public function signup(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'activity_id'       => 'required',
            'name'              => 'required',
            'contact'           => 'required',
            'email'             => ['required', 'email'],
            'company_name'      => 'required',
            'company_job'       => 'required',
            'company_area'      => 'required',
            'company_introduction'  => 'required',
            'people'                => 'required',
            'people_info'           =>  'required',
        ]);

        // 验证失败
        if ($validator->fails()) {
            return responseFail($validator->errors()->all());
        }

        $body = $request->all();

        $info = Activity::find($body['activity_id']);
        if (empty($info)) {
            return responseFail(__('info no exist'));
        }

        $id = ActivitySignup::insertGetId([
            'activity_id'       => $body['activity_id'],
            'name'              => $body['name'],
            'contact'           => $body['contact'],
            'email'             => $body['email'],
            'company_name'      => $body['company_name'],
            'company_job'       => $body['company_job'],
            'company_area'      => $body['company_area'],
            'company_introduction'  => $body['company_introduction'],
            'people'                => $body['people'],
            'people_info'           => $body['people_info'],
            'special'               => isset($body['special'])?$body['special']:'',
            'created_at'            => date('Y-m-d H:i:s')
        ]);

        $domail =   $request->getSchemeAndHttpHost();

        // 使用 Crypt 加密
        $encryptedNumber = Crypt::encrypt($id);

        $url =  $domail.'/api/v1/activity/detail/'.$encryptedNumber;
        return responseSuccess($url);

    }

    // 查看报名信息
    public function detail(Request $request)
    {
        $token = $request->route('token');
        // 使用 Crypt 解密
        $id = Crypt::decrypt($token);
        if (empty($id)) {
            return responseFail('ID不存在');
        }

        // 查看活动信息
        $info = ActivitySignup::where('id', $id)->first();
        if (empty($info)) {
            return responseFail('活动信息不存在');
        }

        return responseSuccess($info);
    }
}