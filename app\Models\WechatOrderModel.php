<?php

namespace App\Models;

use App\Events\UserPaid;
use App\Services\Wechat\WechatService;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class WechatOrderModel extends Model
{
    use HasFactory;

    protected $table = 'cna_wechat_orders';
    protected $guarded = [];

    public function generateOrder($data)
    {
      	$result = self::create($data);
      
      	return $result;
    }
  
  	public function getOrderByOutTradeNo($outTradeNo)
    {
    	  $data = self::select('id', 'out_trade_no', 'payment_state', 'user_id')
            ->where('out_trade_no', $outTradeNo)
          	->where('payment_state', 0)
          	->first();
      
        return $data;
    }

    //检查注册的支付订单
    public static function checkRegisterOrder($profileId)
    {
        $orders = WechatOrderModel::select('id', 'out_trade_no')
          ->where('payment_state', 0)
          ->where('user_id', $profileId)
          ->orderBy('created_at', 'desc')
          ->get();
        if (empty($orders)) {
            return;
        }
        foreach ($orders as $order) {
            $res = WechatService::checkOrder($order['out_trade_no']);
            if (!$res || $res['trade_state'] != 'SUCCESS') {
                continue;
            }
            $update = [
                'mchid' => $res['mchid'],
                'appid' => $res['appid'],
                'transaction_id' => $res['transaction_id'],
                'trade_type' => $res['trade_type'],
                'trade_state' => $res['trade_state'],
                'trade_state_desc' => $res['trade_state_desc'],
                'bank_type' => $res['bank_type'],
                'attach' => $res['attach'],
                'success_time' => $res['success_time'],
                'openid' => $res['payer']['openid'],
                'payer_total' => $res['amount']['payer_total'],
                'currency' => $res['amount']['currency'],
                'payer_currency' => $res['amount']['payer_currency'],
                'payment_state' => 1,
            ];
            WechatOrderModel::where('out_trade_no', $order['out_trade_no'])->update($update);
            event(new UserPaid($profileId, $res['amount']['total']));
            break;
        }
    }
}
