<?php

namespace App\Http\Controllers\Api\v1\Stock;

use App\Http\Controllers\Controller;
use App\Models\Stock;
use Illuminate\Http\Request;

class StockController extends Controller
{
    public function isApply(Request $request)
    {
        $user = $request->attributes->get('user');
        $exist = Stock::where('profile_id', $user['profileID'])->first();
        
        return responseSuccess(['res' => $exist ? 1 : 0]);
    }

    public function apply(Request $request)
    {
        $user = $request->attributes->get('user');
        $exist = Stock::where('profile_id', $user['profileID'])->first();
        if (!$exist) {
            Stock::create(['profile_id' => $user['profileID']]);
        }
        
        return responseSuccess();
    }
}
