<?php

namespace App\Http\Requests\Register;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use App\Exceptions\DefaultException;

class CommitRegSetSecRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            //'currentPassword'      => ['required'],
            'newPassword'          => ['required'],
            'passwordConfirmation' => ['required'],
        ];
    }

    public function messages()
    {
        return [
            //'currentPassword.required'      => __('please fill in the current password'),
            'newPassword.required'          => __('please fill in the new password'),
            'passwordConfirmation.required' => __('please fill in the confirmation password'),
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        $error= $validator->errors()->all();
        throw new DefaultException($error[0]);
    }
}
