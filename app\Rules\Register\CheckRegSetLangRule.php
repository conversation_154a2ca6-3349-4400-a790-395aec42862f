<?php

namespace App\Rules\Register;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use App\Models\LanguageModel;

class CheckRegSetLangRule implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (!LanguageModel::where('languageCode', $value)->exists()) {
            $fail('请选择语言');
        }   
    }
}
