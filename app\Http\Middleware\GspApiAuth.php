<?php

namespace App\Http\Middleware;

use App\Exceptions\DefaultException;
use App\Models\ProfileInfoModel;
use Closure;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Symfony\Component\HttpFoundation\Response;

class GspApiAuth
{
    /**
     * Handle an incoming request.
     *
     * @param \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response) $next
     * @throws \Exception
     */
    public function handle(Request $request, Closure $next, $action = null): Response
    {
        //获取头部鉴权密钥
        $authorization = $request->header('authorization');
        if (!$authorization) {
            return responseFail(__('missing parameter', ['param' => 'authorization']), 401);
        }


        try {

            //解密密钥
            $token = explode(' ', $authorization);
            $token = trim($token[1]);
            $decodeToken = JWT::decode($token, new Key(env('JWT_KEY'), 'HS256'));

            if (isset($decodeToken->action)) {
                // 1.尝试通过 api.auth
                if ($decodeToken->action == ACTION_PROFILE_AUTH || $decodeToken->action == ACTION_PROFILE_EXPIRE) {
                    $profileID = $decodeToken->user_id;
                    $user = ProfileInfoModel::where('profileID', $profileID)->first();
                    if (empty($user)) {
                        return responseFail(__('account error'), 401);
                    }
                    $request->attributes->set('user', $user);
                    return $next($request);
                }

                // 2.尝试通过 企业手机号登录验证
                if ($decodeToken->action == 'company_login') {
                    $request->attributes->set('gsp_id', $decodeToken->gsp_id);
                    return $next($request);
                }



            } else {
                return responseFail(__('param error', ['param'=>'authorization']), 401);
            }
        }  catch (\Exception $e) {
            return responseFail(__('Authorization has expired').$e->getMessage(), 401);
        }

        return responseFail(__('Authorization error'), 401);

    }

}
