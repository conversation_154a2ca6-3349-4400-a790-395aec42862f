<?php

namespace App\Http\Requests\Login;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use App\Exceptions\DefaultException;
use App\Rules\Mobile\CheckMobileRule;
use App\Services\SmsService;

class Login extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'email'        => ['email'],
            'phone'        => [new CheckMobileRule],
            'code'         => ['integer', 'between:'.SmsService::VERIFY_CODE_MIN.','.SmsService::VERIFY_CODE_MAX],
            'captcha_key'  => ['required'],
            'captcha_code' => ['required']
        ];
    }

    public function messages()
    {
        return [
            'email'                 => __('please fill in the correct email'),
            'code'                  => __('verification code error'),
            'captcha_key.required'  => __('missing captcha key'),
            'captcha_code.required' => __('captcha code error'),
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        $error= $validator->errors()->all();
        throw new DefaultException($error[0]);
    }
}
