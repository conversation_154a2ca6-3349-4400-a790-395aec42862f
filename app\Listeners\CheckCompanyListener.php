<?php

namespace App\Listeners;

use App\Events\CheckCompanyEvent;
use App\Models\BillModel;
use App\Models\NotificationInfoModel;
use App\Models\ProfileInfoModel;
use App\Models\ProjectProgressOneModel;
use App\Models\ProjectProgressThreeModel;
use App\Models\ProjectProgressTwoModel;
use App\Models\ProjectServiceModel;
use App\Models\Setting;
use App\Models\WechatOrderModel;
use App\Services\EmailService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Request;

class CheckCompanyListener
{
    private $emailService;
    private $notificationInfoModel;
    private $compnayInfo;
    private $profileInfoModel;
    private $user;
    /**
     * Create the event listener.
     */
    public function __construct(Request $request, EmailService $emailService, NotificationInfoModel $notificationInfoModel, ProfileInfoModel $profileInfoModel)
    {
        //
        $this->request = $request;
        $this->emailService = $emailService;
        $this->notificationInfoModel = $notificationInfoModel;
        $this->profileInfoModel = $profileInfoModel;
    }

    /**
     * Handle the event.
     */
    public function handle(CheckCompanyEvent $event): ?bool
    {
        // 企业信息
        $this->compnayInfo = $event->companyInfo;
        $status = $event->status;

        // 操作用户ID
        $this->user = $this->request->attributes->get('user');

        if ($status == 1) { // 审核通过
            return $this->pass();
        } else if ($status == 2) { // 审核拒绝
            return $this->refuse();
        }
        return false;
    }

    /**
     * 审核通过
     * @return void
     */
    public function pass()
    {
        $info = $this->compnayInfo;
        $account = [];

        try {
            // 开启事务
            DB::beginTransaction();

            if ($info['companyState'] == 1 && $info['companyProgressState'] == 1) { // 进度表一
                $info->companyProgressState = 2;
                $info->companyState = 0;
                $info->editUser = $this->user['profileID'];
                $info->editRole = $this->user['profileRole'];
                $info->editTime = date('Y-m-d H:i:s');
                $info->save();

                // 站内通知
                $data = [
                    'targetUser' =>  $info['companyProfileID'],
                    'companyName' => $info['companyName']
                ];
                \App\Jobs\SendNotice::dispatch($data, 'CompanyProgressOnePass', $this->user)->onQueue('SendNoticeJob');

            } else if ($info['companyState'] == 1 && $info['companyProgressState'] == 2) { // 进度表二
                $info->companyProgressState = 3;
                $info->companyState = 0;
                $info->editUser = $this->user['profileID'];
                $info->editRole = $this->user['profileRole'];
                $info->editTime = date('Y-m-d H:i:s');
                $info->save();

                // step1: 生成绿智地球企业用户登录帐号
                $password = generateRandomString(6);
                $account = [
                    'username' =>  $info['companyEmail'],
                    'password' =>  $password,
                ];

                ProjectProgressThreeModel::create([
                    'username' => $info['companyEmail'],
                    'password' => $password,
                    'loginState'  => 0,
                    'profileID'   => $info['companyProfileID'],
                    'companyID'   => $info['companyID']
                ]);

                // step2: 生成账单记录

                // 查询付款记录
                $paymentOrder = ProjectProgressTwoModel::where('profileID', $info['companyProfileID'])
                    ->where('companyID', $info['companyID'])
                    ->first();
                if ($paymentOrder) {

                    // 付款金额
                    $progressOne = ProjectProgressOneModel::where('profileID', $info['companyProfileID'])
                        ->where('companyID', $info['companyID'])
                        ->first();
                    $totalPrice = $progressOne->companyServicePrice;
                    
                    $lang = Setting::getLang($info['companyProfileID'], 1);

                    // 付款项目
                    $billDescription = '';
                    $companyServices = explode(',', $progressOne->companyServices);
                    $projectService = ProjectServiceModel::whereIn('id', $companyServices)->pluck('title_'.$lang)->toArray();
                    if ($projectService) {
                        $billDescription = implode(',', $projectService);
                    }
                    BillModel::query()->create([
                        'billProfileID'   => $info['companyProfileID'],
                        'billCompanyID'   => $info['companyID'],
                        'billCompanyName' => $info['companyName'],
                        'billDescription' => $billDescription,
                        'billPaymentCode' => $paymentOrder['paymentCode'],  // 发票号是交易流水号
                        'billAmount'      => $totalPrice,
                        'billPaymentType' => 2,// 1贷方付款;2借方付款
                        'billState'       => 1,
                        'createUser'      => $this->user['profileID'],
                        'createRole'      => $this->user['profileRole'],
                        'createTime'      => date('Y-m-d H:i:s'),
                    ]);
                }

                // step3: 站内通知
                $data = [
                    'targetUser' =>  $info['companyProfileID'],
                    'companyName' => $info['companyName']
                ];
                \App\Jobs\SendNotice::dispatch($data, 'CompanyProgressTwoPass', $this->user)->onQueue('SendNoticeJob');


            }


            // 提交
            DB::commit();

            // 发邮件通知
            if ($account) {
                $this->emailService->sendPasswordCompanyEmail($account['username'], $account['password']);
            }

            return true;

        } catch (\Exception $e) {
            DB::rollBack();
            // 记录日志
            Log::info($e->getMessage().$e->getFile().$e->getLine());

            return false;
        }
    }

    /**
     * 审核失败
     * @return void
     */
    public function refuse()
    {
        $info = $this->compnayInfo;

        try {
            // 开启事务
            DB::beginTransaction();

            if ($info['companyState'] == 1 && $info['companyProgressState'] == 1) { // 进度表一
                $info->companyProgressState = 1;
                $info->companyState = 3;
                $info->editUser = $this->user['profileID'];
                $info->editRole = $this->user['profileRole'];
                $info->editTime = date('Y-m-d H:i:s');
                $info->save();

                // 站内通知
                $data = [
                    'targetUser' =>  $info['companyProfileID'],
                    'companyName' => $info['companyName']
                ];
                \App\Jobs\SendNotice::dispatch($data, 'CompanyProgressOneRefuse', $this->user)->onQueue('SendNoticeJob');


            } else if ($info['companyState'] == 1 && $info['companyProgressState'] == 2) {  // 进度表二
                $info->companyProgressState = 2;
                $info->companyState = 3;
                $info->editUser = $this->user['profileID'];
                $info->editRole = $this->user['profileRole'];
                $info->editTime = date('Y-m-d H:i:s');
                $info->save();

                // 站内通知
                $data = [
                    'targetUser' =>  $info['companyProfileID'],
                    'companyName' => $info['companyName']
                ];
                \App\Jobs\SendNotice::dispatch($data, 'CompanyProgressTwoRefuse', $this->user)->onQueue('SendNoticeJob');
            }

            // 提交
            DB::commit();

            return true;

        } catch (\Exception $e) {
            DB::rollBack();
            // 记录日志
            Log::info($e->getMessage());

            return false;
        }
    }
}
