<?php
namespace App\Http\Controllers\Admin\v1;

use App\Http\Controllers\Controller;
use App\Models\ActiveLog;
use App\Models\CompanyModel;
use App\Models\InquiryProcessingModel;
use App\Models\InquiryReportModel;
use Illuminate\Support\Facades\Validator;
use Symfony\Component\HttpFoundation\Request;

class InquiryProcessingController extends Controller
{
    /**
     * 添加行政处理记录
     * @param Request $request
     * @return void
     */
    public function store(Request $request)
    {

        $param = $request->all();

        $result = InquiryProcessingModel::query()->create([
            'administrative_penalty_number'         => $param['administrativePenaltyNumber'],
            'penalty_date'                          => $param['penaltyDate'],
            'penalty_content'                       => $param['penaltyContent'],
            'penalty_amount'                        => $param['penaltyAmount'],
            'uncollected_amount'                    => $param['uncollectedAmount'],
            'temporary_certificate_orbusinessname'  => $param['temporaryCertificateOrBusinessName'],
            'violation_type'                        => $param['violationType'],
            'violation_case'                        => $param['violationCase'],
            'legal_basis'                           => $param['legalBasis'],
            'penalty_case_number'                   => $param['penaltyCaseNumber'],
            'penalty_decision_authority'            => $param['penaltyDecisionAuthority'],
            'penalty_record'                        => $param['penaltyRecord'],
            'legal_representative'                  => $param['legal_representative'],
            'establishment_date'                    => $param['establishment_date'],
            'company_name'                          => $param['company_name'],
            'penalty_category'                      => $param['penalty_category'],
            'dataSource_credit_code'                => $param['dataSource_credit_code']
        ]);

        if ($result) {
            return responseSuccess();
        } else {
            return responseFail(__('save failed'));
        }
    }
}