<?php

namespace App\Http\Controllers\Api\v1\Company;

use App\Events\GspPaidOneEvent;
use App\Events\GspRegisterEvent;
use App\Exceptions\DefaultException;
use App\Http\Controllers\Controller;
use App\Models\CompanyGspReport;
use App\Models\CountryModel;
use App\Models\GspRefundModel;
use App\Models\IcbcOrderModel;
use App\Models\ProfileInfoModel;
use App\Models\ProjectServiceModel;
use App\Services\ICBC\ICBCService;
use App\Services\SmsService;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Illuminate\Http\Request;
use App\Models\CompanyGsp;
use App\Models\GspOption;
use App\Rules\Mobile\CheckMobileRule;
use App\Services\DocService;
use App\Services\GspService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use TCPDF;

class GspController extends Controller
{
    const ACTION = 'gsp_form'; //场景
    const ACTION_COMPANY_LOGIN = 'company_login'; // 企业用户登录场景


    private $smsService;

    public function __construct(SmsService $smsService)
    {
        $this->smsService = $smsService;
    }


    public function formOption(Request $request)
    {
        $type = $request->get('type');
        if (!in_array($type, GspOption::TYPE_ARR)) {
            return responseFail();
        }
        $data = GspOption::where('type', $type)->get();

        return responseSuccess($data);
    }

    // 当前正在进行中流程
    public function getCurrentGsp($user, $gsp_id = '')
    {
        $record = [];
        if ($user) {
            $record = CompanyGsp::where('profile_id', $user['profileID'])->where('status', '!=',CompanyGsp::STATUS_ACTIVE)->first();
        } else if ($gsp_id) {
            $record = CompanyGsp::where('id', $gsp_id)->first();
        }

        return $record;
    }

    public function progress(Request $request)
    {
        $user = $request->attributes->get('user');
        $gsp_id =  $request->attributes->get('gsp_id');
        $record = $this->getCurrentGsp($user, $gsp_id);

        $progress = $this->checkStep($record);
        return responseSuccess($progress);
    }

    // 判断当前进行中的步骤
    public function checkStep($record)
    {
        $progress = [
            1 => [  // 第一步判断
                'createForm' => 0,  // 生成预审表格
                'uploadForm' => 0,  // 提交预审表格
                'payOne'     => 0,  // 已付款(申请费和预审保证金)
                'commitCheck'=> 0,  // 提交审核
            ],
            2 => [ // 第二步判断
                'formCheck' => 0,  // 预审表审核中
            ],
            3 => [ // 第三步判断
                'printContract'  => 0,  // 打印合同
                'uploadContract' => 0,  // 上传签署合同
                'payTwo'         => 0, // 已付款(企业开户费)
                'nextStep'       => 0 // 下一步
            ],
            4 => [ // 第四步判断
                'contractCheck' => 0,  // 合同审核中
            ],
            5 => [ // 第五步判断
                'postReport' => 0,  // 提交尽调报告
            ],
            6 => [ // 第六步判断
                'reportCheck'  => 0,
            ],
            7 => [ // 第七步判断
                'payThree' => 0, // 已付款(AI账号开通费)
                'success'  => 0
            ],
        ];

        if ($record) {
            // 第一步判断
            if ($record['form']) { // 已生成预审表格
                $progress[1]['createForm'] = 1;
            }

            if ($record['form_complete']) { // 已上传预审表格
                $progress[1]['uploadForm'] = 1;
            }

            if ($record['pay_one'] == 1) { // 已付款第一步
                $progress[1]['payOne'] = 1;
            }

            if ($record['status'] == 1) { // 第一步提交审核
                $progress[1]['commitCheck'] = 1;
            }

            // 第二步判断
            if ($record['status'] == 3) { // 预审不通过
                $progress[2]['formFail'] = 0;
                $progress[2]['formCheck'] = 1;
                // 是否退款
                if ($record['pay_one_return'] == 1) {
                    $progress[2]['payOneReturn'] = 0;
                }
            } else if ($record['status'] == 2) { // 审核通过
                //$progress[2]['formSuccess'] = 0;
                $progress[2]['formCheck'] = 1;
                $progress[1]['commitCheck'] = 1;

            }
            if ($record['status'] > 3)  {
                $progress[2]['formCheck'] = 1;
                $progress[1]['commitCheck'] = 1;
            }

            // 第三步判断
            if ($record['contract_url']) {
                $progress[3]['uploadContract'] = 1; // 上传签署合同
            }

            if ($record['is_print_contract'] == 1) { // 打印合同
                $progress[3]['printContract'] = 1;
            }

            if ($record['pay_two'] == 1) { // 已付款(企业开户费)
                $progress[3]['payTwo'] = 1;
            }

            if ($record['status'] == 4) { // 下一步审核
                $progress[3]['nextStep'] = 1;
            }

            // 第四步判断
            if ($record['status'] == 5) { // 审核通过
                $progress[3]['nextStep'] = 1;
                $progress[4]['contractCheck'] = 1;
                //$progress[4]['contractSuccess'] = 0;
            } else if ($record['status'] == 6) { // 审核失败
                $progress[4]['contractFail'] = 0;
                $progress[4]['contractCheck'] = 1;
            }
            if ($record['status'] > 6)  {
                $progress[4]['contractCheck'] = 1;
                $progress[3]['nextStep'] = 1;
            }

            // 第五步判断
            if ($record['status'] == 7) {
                $progress[5]['postReport'] = 1;
            }

            // 第六步判断
            if ($record['status'] == 8) { // 尽调审核通过
                $progress[6]['reportCheck'] = 1;
                $progress[5]['postReport'] = 1;
            } else if ($record['status'] == 9) { // 尽调审核不通过
                $progress[6]['reportCheck'] = 1;
                $progress[6]['reportFail'] = 0;
            }

            // 第七步
            if ($record['pay_three'] == 1) {
                $progress[7]['payThree'] = 1;
            }
        }



        // 过滤掉已设置步骤
        foreach ($progress as $key=>$val) {
            $progress[$key] = array_keys(array_filter($val, function($value){
                return $value==0;
            }));
            if(empty($progress[$key])) unset($progress[$key]);
        }

        return $progress;
    }

    /**
     * 预审表格(保存草稿)
     * @param Request $request
     * @return null
     */
    public function apply(Request $request)
    {

        $validator = Validator::make($request->all(), [
            'register_capital'  => 'numeric',
            'paid_capital'      => 'numeric',
            'annual_revenue'    => 'numeric',
            'annual_cost'       => 'numeric',
            'total_assets'      => 'numeric',
            'total_liability'   => 'numeric',
            'annual_trade'      => 'numeric',
        ]);

        // 验证失败
        if ($validator->fails()) {
            return responseFail($validator->errors()->all());
        }

        $user = $request->attributes->get('user');
        $body = $request->all();
        $body = array_map(function ($value) {
            return empty($value) ? '' : $value;
        }, $body);

        // 判断手机号是否重复
        if (isset($body['phone']) && $body['phone']) {
            $checkPhone = CompanyGsp::where('phone', $body['phone'])->first();
            if ($checkPhone) {
                return responseFail(__('team phone exist'));
            }
        }

        // 判断邮箱唯一性用于创建账号使用
        if (isset($body['email']) && $body['email']) {
            $emailExist = ProfileInfoModel::where('profileEmail', $body['email'])->first();
            if ($emailExist) {
                return responseFail(__('team email exist'));
            }
        }

        // 当前正在进行的入驻申请
        $record = $this->getCurrentGsp($user, '');
        if ($record) {
            $body['status'] = 0;
            $body['form'] = '';
            $body['form_complete'] = '';
            CompanyGsp::where('profile_id', $user['profileID'])->update($body);
        } else {

            $body['profile_id'] = $user['profileID'];
            CompanyGsp::create($body);
        }

        return responseSuccess();
    }

    /**
     * 查看预审表格数据
     * @param Request $request
     * @return void
     */
    public function applyDetail(Request $request)
    {
        $user = $request->attributes->get('user');
        $record = $this->getCurrentGsp($user);

        if (empty($record)) {
            return responseSuccess();
        }
        $data = [
            'name'              => $record['name'],
            'credit_code'       => $record['credit_code'],
            'register_capital'  => $record['register_capital'],
            'paid_capital'      => $record['paid_capital'],
            'desc'              => $record['desc'],
            'main_business'     => $record['main_business'],
            'related_business'  => $record['related_business'],
            'customer'          => $record['customer'],
            'annual_revenue'    => $record['annual_revenue'],
            'annual_cost'       => $record['annual_cost'],
            'total_assets'      => $record['total_assets'],
            'total_liability'   => $record['total_liability'],
            'contact_name'      => $record['contact_name'],
            'contact_position'  => $record['contact_position'],
            'phone'             => $record['phone'],
            'email'             => $record['email'],
            'economic_behavior' => $record['economic_behavior'],
            'industry_group'    => $record['industry_group'],
            'annual_trade'      => $record['annual_trade'],
            'target_country'    => $record['target_country'],
        ];

        if ($record['industry_group']) {
            $data['industry_group'] = explode(',', $record['industry_group']);
        }
        if ($record['target_country']) {
            $data['target_country'] = explode(',', $record['target_country']);
        }

        return responseSuccess($data);

    }

    /**
     * 生成预审表格PDF
     * @return void
     */
    public function createForm(Request $request)
    {
        $body = $request->all();
        $user = $request->attributes->get('user');
        $validator = Validator::make($request->all(), [
            'name'              => 'required',
            'credit_code'       => 'required',
            'register_capital'  => ['required', 'numeric'],
            'paid_capital'      => ['required', 'numeric'],
            'desc'              => 'required',
            'main_business'     => 'required',
            'related_business'  => 'required',
            'customer'          => 'required',
            'annual_revenue'    => ['required', 'numeric'],
            'annual_cost'       => ['required', 'numeric'],
            'total_assets'      => ['required', 'numeric'],
            'total_liability'   => ['required', 'numeric'],
            'contact_name'      => 'required',
            'contact_position'  => 'required',
            'phone'             => ['required',new CheckMobileRule],
            'email'             => ['required', 'email'],
            'economic_behavior' => 'required',
            'industry_group'    => 'required',
            'annual_trade'      =>  ['required', 'numeric'],
            'target_country'    => 'required',
            'link'              => 'required'
        ]);

        // 验证失败
        if ($validator->fails()) {
            return responseFail($validator->errors()->all());
        }

        // 判断手机号是否重复
        if (isset($body['phone']) && $body['phone']) {
            $checkPhone = CompanyGsp::where('phone', $body['phone'])->first();
            if ($checkPhone) {
                return responseFail(__('team phone exist'));
            }
        }

        // 判断邮箱唯一性用于创建账号使用
        if (isset($body['email']) && $body['email']) {
            $emailExist = ProfileInfoModel::where('profileEmail', $body['email'])->first();
            if ($emailExist) {
                return responseFail(__('team email exist'));
            }
        }


        // 二维码链接
        //$body['link'] = $body['link'];

        // step1. 生成预审表格
        $form = (new GspService)->getForm($body);

        // step2. 更新信息
        unset($body['link']);
        $body['form'] = $form;
        $body['status'] = 0;
        $body['form_complete'] = '';

        $record = $this->getCurrentGsp($user);
        if ($record) {
            CompanyGsp::where('profile_id', $user['profileID'])->update($body);
        } else {

            $body['profile_id'] = $user['profileID'];
            CompanyGsp::create($body);
        }

        $token = DocService::generateToken($form, self::ACTION, 3600);
        return responseSuccess($token);

    }


    // 上传预审表格
    public function uploadForm(Request $request)
    {
        $user = $request->attributes->get('user');
        $gsp_id = $request->attributes->get('gsp_id');
        $body = $request->all();
        $rules = [
            'file' => ['file', 'mimes:pdf,jpg,jpeg,png,webp', 'max:'.(env('ALLOW_FILE_SIZE')*1024)],
        ];
        $messages = [
            'file.mimes' => __('incorrect format img'),
            'file.max' => __('exceed size img', ['limit' => env('ALLOW_FILE_SIZE').'M']),
        ];
        $validator = Validator::make($body, $rules, $messages);
        if ($validator->fails()) {
            $errors = $validator->errors()->all();
            return responseFail($errors);
        }

        $file = $request->file('file');
        if (empty($file)) {
            return responseFail();
        }

        $resource = $file->store('gsp', 'public');
        if ($resource) {
            $form_complete = $resource;
        } else {
            return responseFail();
        }

        // 正在进行中申请
        $record = $this->getCurrentGsp($user, $gsp_id);
        if (empty($record)) {
            return responseFail(__('info no exist'));
        }

       $result = CompanyGsp::where('id', $record['id'])->where('status', '!=',CompanyGsp::STATUS_ACTIVE)->update([
           'form_complete' => $form_complete
       ]);
        if ($result !== false) {
            return responseSuccess();
        } else {
            return responseFail();
        }

    }

    /**
     * 第一步提交审核
     * @return void
     */
    public function formCheck(Request $request)
    {
        $user = $request->attributes->get('user');
        $gsp_id = $request->attributes->get('gsp_id');
        // 正在进行中申请
        $record = $this->getCurrentGsp($user, $gsp_id);
        if (empty($record)) {
            return responseFail(__('info no exist'));
        }
        $result = CompanyGsp::where('id', $record['id'])->where('status', '!=',CompanyGsp::STATUS_ACTIVE)->update([
            'status' => CompanyGsp::STATUS_UPLOAD_FORM,
            'form_post_date' => date('Y-m-d H:i:s')
        ]);
        if ($result !== false) {
            return responseSuccess();
        } else {
            return responseFail();
        }
    }

    // 打印合同
    public function printContract(Request $request)
    {
        $user = $request->attributes->get('user');
        $gsp_id = $request->attributes->get('gsp_id');

        $record = $this->getCurrentGsp($user, $gsp_id);
        if (empty($record)) {
            return responseFail(__('info no exist'));
        }
        // 判断前面步骤是否走完
/*        $ret = $this->checkStep($record);
        if (isset($ret[1]) || isset($ret[2])) {
            return responseFail();
        }*/


        $result = CompanyGsp::where('profile_id', $user['profileID'])->where('status', '!=',CompanyGsp::STATUS_ACTIVE)->update([
            'is_print_contract' => 1
        ]);


        if ($result !== false) {
            return responseSuccess();
        } else {
            return responseFail();
        }

    }

    // 上传合同
    public function uploadContract(Request $request)
    {
        $user = $request->attributes->get('user');
        $gsp_id = $request->attributes->get('gsp_id');
        $body = $request->all();
        $rules = [
            'file' => ['required','file', 'mimes:pdf,jpg,jpeg,png,webp', 'max:'.(env('ALLOW_FILE_SIZE')*1024)],
        ];
        $messages = [
            'file.mimes' => __('incorrect format img'),
            'file.max' => __('exceed size img', ['limit' => env('ALLOW_FILE_SIZE').'M']),
        ];
        $validator = Validator::make($body, $rules, $messages);
        if ($validator->fails()) {
            $errors = $validator->errors()->all();
            return responseFail($errors);
        }

        $file = $request->file('file');
        if (empty($file)) {
            return responseFail();
        }

        $resource = $file->store('gsp', 'public');
        if ($resource) {
            $contract_url = $resource;
        } else {
            return responseFail();
        }

        // 正在进行中申请
        $record = $this->getCurrentGsp($user, $gsp_id);
        if (empty($record)) {
            return responseFail(__('info no exist'));
        }
        $result = CompanyGsp::where('id', $record['id'])->where('status', '!=',CompanyGsp::STATUS_ACTIVE)->update([
            'contract_url'  => $contract_url
        ]);
        if ($result !== false) {
            return responseSuccess();
        } else {
            return responseFail();
        }
    }


    /**
     * 合同提交审核
     * @return void
     */
    public function contractCheck(Request $request)
    {
        $user = $request->attributes->get('user');
        // 正在进行中申请
        $result = CompanyGsp::where('profile_id', $user['profileID'])->where('status', '!=',CompanyGsp::STATUS_ACTIVE)->update([
            'status' => CompanyGsp::STATUS_UPLOAD_CONTRACT,
            'contract_post_date' => date('Y-m-d H:i:s')
        ]);
        if ($result !== false) {
            return responseSuccess();
        } else {
            return responseFail();
        }
    }

    /**
     * 审核结果
     * @param Request $request
     * @return void
     */
    public function checkResult(Request $request)
    {
        $user = $request->attributes->get('user');
        $gsp_id = $request->attributes->get('gsp_id');
        $type = $request->input('type');

        // 企业信息
        $info = $this->getCurrentGsp($user, $gsp_id);
        if (empty($info)) {
            return responseFail(__('info no exist'));
        }

        if ($type == 1) {
            if ($info['status'] == 1) { // 审核中
                $data = [
                    'check_date'    => $info['form_post_date']??'',
                    'reject_reason' => '',
                ];
            } else if ($info['status'] == 3) {
                $data = [
                    'check_date'    => $info['form_check_date']??'',
                    'reject_reason' => $info['form_reject_reason'],
                ];
            }

        } else if ($type == 2) {
            if ($info['status'] == 4) {
                $data = [
                    'check_date'    => $info['contract_post_date']??'',
                    'reject_reason' => '',
                ];
            } else if ($info['status'] == 6) {
                $data = [
                    'check_date'    => $info['contract_check_date']??'',
                    'reject_reason' => $info['contract_reject_reason'],
                ];
            }

        } else if ($type == 3) {
            if ($info['status'] == 7) {
                $data = [
                    'check_date'    => $info['report_post_date']??'',
                    'reject_reason' => '',
                ];
            } else if ($info['status'] == 9) {
                $data = [
                    'check_date'    => $info['report_check_date']??'',
                    'reject_reason' => $info['report_reject_reason'],
                ];
            }

        }

        return responseSuccess($data);

    }

    /**
     * 上传尽调报告
     * @param Request $request
     * @return void
     */
    public function uploadReport(Request $request)
    {
        //$gsp_id = $request->attributes->get('gsp_id');
        $user = $request->attributes->get('user');
        $files = $request->file('file');
        $filename = $request->input('filename');

        // 企业信息
        $info = $this->getCurrentGsp($user);
        if (empty($info)) {
            return responseFail(__('info no exist'));
        }

        if ( empty($filename)) {
            return responseFail(__('missing parameter', ['param' => 'param']));
        }

        $file_id = str_replace('file', '', $filename);
        $gsp_id = $info['id'];

        if ($user) {
            $file_owner = 1; // 合伙人
        } else {
            $file_owner = 2; // 企业
        }

        $data = [];
        if ($files) {
            foreach ($files as $file) {
                // $resource = OssService::upload($file);
                $resource = $file->store('gsp/report', 'public');
                $resource && $data[] = [
                    'gsp_id'     => $info['id'],
                    'profile_id' => $info['profile_id'],
                    'file_path'  => $resource,
                    'file_id'    => $file_id,
                    'file_owner' => $file_owner,
                    'created_at' => date('Y-m-d H:i:s')
                ];
            }
        }


        try {
            // 开启事务
            DB::beginTransaction();

            // step1 删除文件
            $oldFile = CompanyGspReport::where('gsp_id', $gsp_id)->where('file_id', $file_id)->where('file_owner', $file_owner)->first();
            if ($oldFile) {
                CompanyGspReport::where('gsp_id', $gsp_id)->where('file_id', $file_id)->where('file_owner', $file_owner)->delete();
            }


            // step2 重新上传
            if ($data) {
                CompanyGspReport::insert($data);

            }

            // 提交
            DB::commit();

            return responseSuccess();

        } catch (\Exception $e) {
            DB::rollBack();

            return responseFail($e->getMessage());
        }

    }

    /**
     * 尽调报告(保存草稿)
     * @param Request $request
     * @return void
     */
    public function applyDraft(Request $request)
    {
        //$gsp_id = $request->attributes->get('gsp_id');
        $user = $request->attributes->get('user');

        /*$body = $request->all();
        Log::info(json_encode($body));*/
        // 企业信息
        $info = $this->getCurrentGsp($user);
        if (empty($info)) {
            return responseFail(__('info no exist'));
        }

        if ($user) {
            $file_owner = 1; // 合伙人
        } else {
            $file_owner = 2; // 企业
        }
        $data = [];
        for ($i = 1; $i <= 23; $i++) {
            $files = $request->file('file'.$i);
            if ($files) {
                foreach ($files as $file) {
                   // $resource = OssService::upload($file);
                    $resource = $file->store('gsp/report', 'public');
                    $resource && $data[$i][] = $resource;
                }

            }
        }


        $gsp_id = $info['id'];
        $updateData = [];
        if ($data) {
            foreach ($data as $key => $val) {
                foreach ($val as $v) {
                    $updateData[] = [
                        'gsp_id'     => $gsp_id,
                        'profile_id' => $info['profile_id'],
                        'file_path'  => $v,
                        'file_id'    => $key,
                        'file_owner' => $file_owner,
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                }

            }
        }


        try {
            // 开启事务
            DB::beginTransaction();

            // step1 删除文件
            CompanyGspReport::where('gsp_id', $gsp_id)->where('file_owner', $file_owner)->delete();

            // step2 重新上传
            if ($updateData) {
                CompanyGspReport::insert($updateData);

            }

            // 提交
            DB::commit();

            return responseSuccess();

        } catch (\Exception $e) {
            DB::rollBack();

            return responseFail($e->getMessage());
        }


    }

    /**
     * 提交尽调报告
     * @param Request $request
     * @return void
     */
    public function applyReport(Request $request)
    {
        //$gsp_id = $request->attributes->get('gsp_id');
        $user = $request->attributes->get('user');

        // 企业信息
        $info = $this->getCurrentGsp($user);
        if (empty($info)) {
            return responseFail(__('info no exist'));
        }

        $gsp_id = $info['id'];

       /* $validator = Validator::make($request->all(), [
            'file1' => 'required',
            'file2' => 'required',
            'file3' => 'required',
            'file4' => 'required',
            'file5' => 'required',
            'file6' => 'nullable',
            'file7' => 'required',
            'file8' => 'required',
            'file9' => 'required',
            'file10' => 'required',
            'file11' => 'required',
            'file12' => 'required',
            'file13' => 'required',
            'file14' => 'required',
            'file15' => 'required',
            'file16' => 'required',
            'file17' => 'required',
            'file18' => 'required',
            'file19' => 'required',
            'file20' => 'required',
            'file21' => 'required',
            'file22' => 'required',
            'file23' => 'nullable'
        ]);

        // 验证失败
        if ($validator->fails()) {
            return responseFail($validator->errors()->all());
        }

        if ($user) {
            $file_owner = 1; // 合伙人
        } else {
            $file_owner = 2; // 企业
        }

        $data = [];
        for ($i = 1; $i <= 23; $i++) {
            $files = $request->file('file'.$i);
            if ($files) {
                foreach ($files as $file) {
                   //$resource = OssService::upload($file);
                    $resource = $file->store('gsp/report', 'public');
                    $resource && $data[$i][] = $resource;
                }

            }
        }
        

        if ($data) {
            foreach ($data as $key => $val) {
                foreach ($val as $v) {
                    $updateData[] = [
                        'gsp_id'     => $gsp_id,
                        'profile_id' => $info['profile_id'],
                        'file_path'  => $v,
                        'file_id'    => $key,
                        'file_owner' => $file_owner
                    ];
                }

            }
        }*/

        // 判断是否必填都填了
        $reportData = CompanyGspReport::where('gsp_id', $gsp_id)->pluck('file_id')->toArray();
        $explode = [6, 23];
        for ($i = 1; $i <= 23; $i++) {
           if (!in_array($i, $reportData) && !in_array($i, $explode)) {
                return responseFail(__('please upload complete file'));
           }
        }

        try {
            // 开启事务
            DB::beginTransaction();

            // step1 删除文件
          //  CompanyGspReport::where('gsp_id', $gsp_id)->where('file_owner', $file_owner)->delete();

            // step2 重新上传
          //  CompanyGspReport::insert($updateData);

            // step3 更新提交状态
            CompanyGsp::where('id', $gsp_id)->update([
                'status' => CompanyGsp::STATUS_POST_REPORT,
                'report_post_date' => date('Y-m-d H:i:s')
            ]);

            // 提交
            DB::commit();

            return responseSuccess();

        } catch (\Exception $e) {
            DB::rollBack();

            return responseFail($e->getMessage());
        }

    }

    /**
     * 查看尽调报告
     * @param Request $request
     * @return void
     */
    public function reportDetail(Request $request)
    {
        $user = $request->attributes->get('user');
        $gsp_id = $request->attributes->get('gsp_id');

        // 企业信息
        $info = $this->getCurrentGsp($user, $gsp_id);
        if (empty($info)) {
            return responseFail(__('info no exist'));
        }

        $gsp_id = $info['id'];

        // 查看尽调报告文件
        $data = CompanyGspReport::where('gsp_id', $gsp_id)->get();
        $result = [];
        if ($data) {
            foreach ($data as $val) {
                $result['file'.$val['file_id']]['file'][] = [
                    'file_path' => $val['file_path'],
                    'file_owner'=> $val['file_owner'],
                    'created_at'=> $val['created_at']
                ];
                $result['file'.$val['file_id']]['status'] = $val['status'];
            }
        }

        return responseSuccess($result);
    }

    /**
     * 生成付款第一步二维码
     * @return void
     */
    public function createPayOneQRcode(Request $request)
    {
        $user = $request->attributes->get('user');
        $gsp_id = $request->attributes->get('gsp_id');

        // 企业信息
        $info = $this->getCurrentGsp($user, $gsp_id);
        if (empty($info)) {
            return responseFail(__('info no exist'));
        }

        if ($info['pay_one'] == 1) {
            return responseFail(__('gsp register paid one'));
        }

        // 生成订单

        // 申请费+预审保证金
        $total = ProjectServiceModel::whereIn('id', [ ProjectServiceModel::PAY_PRE_BOND, ProjectServiceModel::PAY_COMPANY_APPLY])->sum('price');
        if (!$total) return responseFail();

        if (env('APP_ENV') == 'product') {
            $total = $total * 100;
        } else {
            $total = 0.01 * 100;
        }

        $outTradeNo = ICBCService::generateOutTradeNo($info['profile_id']);

        // 调用ICBC接口
        $domail =   $request->getSchemeAndHttpHost();
        $notify =  $domail.'/api/v1/gsp/notifyPayOne';
        $result = ICBCService::generatePayQRcode($outTradeNo, $total, $notify);
        if ($result) {

            //创建订单
            $ret = IcbcOrderModel::query()->create([
                'mer_id'        => config('icbc.mer_id'),
                'app_id'        => config('icbc.app_id'),
                'user_id'       => $gsp_id > 0 ? 0: $user['profileID'], // 企业的话为0; 合伙人则合伙人ID
                'obj_id'        => $info['id'],
                'qrcode_msg_id' => $result['msg_id'],
                'out_trade_no'  => $outTradeNo,
                'qrcode'        => $result['qrcode'],
                'total_amt'     => $total,
                'attach'        => $result['attach'],
                'type'          => IcbcOrderModel::TYPE_GSP_REGISTER,
                'remark'        => '入驻平台申请费用和资格预审保证金',
                'project'       => ProjectServiceModel::PAY_PRE_BOND.','.ProjectServiceModel::PAY_COMPANY_APPLY,  // 预备保证金
            ]);

            if (!$ret) {
                return responseFail('GENERATE_ORDER_ERROR');
            }

            $img = \SimpleSoftwareIO\QrCode\Facades\QrCode::format('png')->size(150)->generate($result['qrcode']);
            $img = 'data:image/png;base64,' . base64_encode($img);
            return responseSuccess(['qrcode'=>$img]);
        }

    }

    /**
     * 付款第一步成功回调
     * @return bool
     */
    public function notifyPayOne(Request $request)
    {
        $queryString = file_get_contents("php://input");

        // 解码查询字符串
        $decodedString = urldecode($queryString);

        // 将查询字符串解析为数组
        parse_str($decodedString, $data);

        $response_biz_content = isset($data['biz_content'])?json_decode($data['biz_content'], true):'';

        if ($response_biz_content && $response_biz_content['return_code'] == 0) {
            // 回调处理
            $order = IcbcOrderModel::query()->where('out_trade_no', $response_biz_content['out_trade_no'])->first();
            if ($order) {

                if ($order->pay_status == 0) {
                    try {
                        // 开启事务
                        DB::beginTransaction();

                        $gspId = $order->obj_id;
                        $gspCompany = CompanyGsp::where('id', $gspId)->first();

                        // step1 更新支付订单信息
                        $order->pay_msg_id = $response_biz_content['msg_id'];
                        $order->order_id = $response_biz_content['order_id'];
                        $order->pay_time = $response_biz_content['pay_time'];
                        $order->pay_status = 1;
                        $order->cust_id = $response_biz_content['cust_id'];
                        $order->card_no = $response_biz_content['card_no'];
                        $order->bank_name = isset($response_biz_content['bank_name']) ?? $response_biz_content['bank_name'];
                        $order->channel = $response_biz_content['channel'];
                        $order->attach = $response_biz_content['attach'];
                        $order->tp_cust_id = isset($response_biz_content['tp_cust_id']) ?? $response_biz_content['tp_cust_id'];
                        $order->trx_ser_no = isset($response_biz_content['trx_ser_no']) ?? $response_biz_content['trx_ser_no'];
                        $order->tp_order_id = isset($response_biz_content['tp_order_id']) ?? $response_biz_content['tp_order_id'];
                        $order->sub_open_id = isset($response_biz_content['sub_open_id']) ?? $response_biz_content['sub_open_id'];
                        $order->bank_type = isset($response_biz_content['bank_type']) ?? $response_biz_content['bank_type'];
                        $order->tp_user_id = isset($response_biz_content['tp_user_id']) ?? $response_biz_content['tp_user_id'];
                        $order->save();


                        // step2 更新付款成功状态
                        CompanyGsp::where('id', $gspId)->update(['pay_one' => 1]);

                        // 提交
                        DB::commit();

                        return true;

                    } catch (\Exception $e) {
                        DB::rollBack();

                        Log::info($e->getMessage());
                        return false;
                    }
                } else if ($order->pay_status == 1) {
                    return true;
                }

            }

        }

        return false;
    }

    /**
     * 第一步付款要求退款
     * @param Request $request
     * @return void
     */
    public function payOneReturn(Request $request)
    {
        $user = $request->attributes->get('user');
        $gsp_id = $request->attributes->get('gsp_id');

        // 企业信息
        $record = $this->getCurrentGsp($user, $gsp_id);
        if (empty($record)) {
            return responseFail(__('info no exist'));
        }

        if ($record['status'] > 3 ) {
            return responseFail(__('param error', ['param'=>'status']));
        }


        try {
            // 开启事务
            DB::beginTransaction();

            // step1 更改为退款状态
            CompanyGsp::where('id', $record['id'])->update(['pay_one_return' => 1]);

            // step2 退款记录

            // 退款金额
            $amount = IcbcOrderModel::query()
                            ->where('project',  ProjectServiceModel::PAY_PRE_BOND.','.ProjectServiceModel::PAY_COMPANY_APPLY)
                            ->where('obj_id', $record['id'])
                            ->where('type', IcbcOrderModel::TYPE_GSP_REGISTER)
                            ->where('pay_status', 1)
                            ->value('total_amt');

            $amount = bcdiv($amount, 100, 2);

            GspRefundModel::insert([
                'gsp_id'     => $record['id'],
                'profile_id' => $record['profile_id'],
                'amount'     => $amount,
                'status'     => 0,
            ]);

            // 提交
            DB::commit();

            return responseSuccess();

        } catch (\Exception $e) {
            DB::rollBack();
            return responseFail();
        }

    }

    /**
     * 生成付款第二步二维码
     * @return void
     */
    public function createPayTwoQRcode(Request $request)
    {
        $user = $request->attributes->get('user');
        $gsp_id = $request->attributes->get('gsp_id');

        // 企业信息
        $info = $this->getCurrentGsp($user, $gsp_id);
        if (empty($info)) {
            return responseFail(__('info no exist'));
        }

        if ($info['pay_two'] == 1) {
            return responseFail(__('gsp register paid two'));
        }

        // 生成订单

        // 企业开户费
        $total = ProjectServiceModel::where('id', ProjectServiceModel::PAY_COMPANY_OPEN)->value('price');
        if (!$total) return responseFail();

        if (env('APP_ENV') == 'product') {
            $total = $total * 100;
        } else {
            $total = 0.01 * 100;
        }

        $outTradeNo = ICBCService::generateOutTradeNo($info['profile_id']);

        // 调用ICBC接口
        $domail =   $request->getSchemeAndHttpHost();
        $notify =  $domail.'/api/v1/gsp/notifyPayTwo';
        $result = ICBCService::generatePayQRcode($outTradeNo, $total, $notify);
        if ($result) {

            //创建订单
            $ret = IcbcOrderModel::query()->create([
                'mer_id'        => config('icbc.mer_id'),
                'app_id'        => config('icbc.app_id'),
                'user_id'       => $gsp_id > 0 ? 0: $user['profileID'], // 企业的话为0; 合伙人则合伙人ID
                'obj_id'        => $info['id'],
                'qrcode_msg_id' => $result['msg_id'],
                'out_trade_no'  => $outTradeNo,
                'qrcode'        => $result['qrcode'],
                'total_amt'     => $total,
                'attach'        => $result['attach'],
                'type'          => IcbcOrderModel::TYPE_GSP_REGISTER,
                'remark'        => '企业入驻平台开户费(一次性)',
                'project'       => ProjectServiceModel::PAY_COMPANY_OPEN,  // 企业开户费
            ]);

            if (!$ret) {
                return responseFail('GENERATE_ORDER_ERROR');
            }

            $img = \SimpleSoftwareIO\QrCode\Facades\QrCode::format('png')->size(150)->generate($result['qrcode']);
            $img = 'data:image/png;base64,' . base64_encode($img);
            return responseSuccess(['qrcode'=>$img]);
        }

    }

    /**
     * 付款第二步成功回调
     * @return bool
     */
    public function notifyPayTwo(Request $request)
    {
        $queryString = file_get_contents("php://input");

        // 解码查询字符串
        $decodedString = urldecode($queryString);

        // 将查询字符串解析为数组
        parse_str($decodedString, $data);

        $response_biz_content = isset($data['biz_content'])?json_decode($data['biz_content'], true):'';

        if ($response_biz_content && $response_biz_content['return_code'] == 0) {
            // 回调处理
            $order = IcbcOrderModel::query()->where('out_trade_no', $response_biz_content['out_trade_no'])->first();
            if ($order) {

                if ($order->pay_status == 0) {
                    try {
                        // 开启事务
                        DB::beginTransaction();

                        $gspId = $order->obj_id;
                        $gspCompany = CompanyGsp::where('id', $gspId)->first();

                        // step1 更新支付订单信息
                        $order->pay_msg_id = $response_biz_content['msg_id'];
                        $order->order_id = $response_biz_content['order_id'];
                        $order->pay_time = $response_biz_content['pay_time'];
                        $order->pay_status = 1;
                        $order->cust_id = $response_biz_content['cust_id'];
                        $order->card_no = $response_biz_content['card_no'];
                        $order->bank_name = isset($response_biz_content['bank_name']) ?? $response_biz_content['bank_name'];
                        $order->channel = $response_biz_content['channel'];
                        $order->attach = $response_biz_content['attach'];
                        $order->tp_cust_id = isset($response_biz_content['tp_cust_id']) ?? $response_biz_content['tp_cust_id'];
                        $order->trx_ser_no = isset($response_biz_content['trx_ser_no']) ?? $response_biz_content['trx_ser_no'];
                        $order->tp_order_id = isset($response_biz_content['tp_order_id']) ?? $response_biz_content['tp_order_id'];
                        $order->sub_open_id = isset($response_biz_content['sub_open_id']) ?? $response_biz_content['sub_open_id'];
                        $order->bank_type = isset($response_biz_content['bank_type']) ?? $response_biz_content['bank_type'];
                        $order->tp_user_id = isset($response_biz_content['tp_user_id']) ?? $response_biz_content['tp_user_id'];
                        $order->save();


                        // step2 更新付款成功状态
                        CompanyGsp::where('id', $gspId)->update(['pay_two' => 1]);

                        // 提交
                        DB::commit();

                        return true;

                    } catch (\Exception $e) {
                        DB::rollBack();

                        Log::info($e->getMessage());
                        return false;
                    }
                } else if ($order->pay_status == 1) {
                    return true;
                }

            }

        }

        return false;
    }


    /**
     * 生成付款第三步二维码
     * @return void
     */
    public function createPayThreeQRcode(Request $request)
    {
        $user = $request->attributes->get('user');
        $gsp_id = $request->attributes->get('gsp_id');

        // 企业信息
        $info = $this->getCurrentGsp($user, $gsp_id);
        if (empty($info)) {
            return responseFail(__('info no exist'));
        }

        if ($info['pay_three'] == 1) {
            return responseFail(__('gsp register paid three'));
        }

        // 生成订单

        // AI开通费
        $total = ProjectServiceModel::where('id', ProjectServiceModel::PAY_COMPANY_AIOPEN)->value('price');
        if (!$total) return responseFail();

        if (env('APP_ENV') == 'product') {
            $total = $total * 100;
        } else {
            $total = 0.01 * 100;
        }

        $outTradeNo = ICBCService::generateOutTradeNo($info['profile_id']);

        // 调用ICBC接口
        $domail =   $request->getSchemeAndHttpHost();
        $notify =  $domail.'/api/v1/gsp/notifyPayThree';
        $result = ICBCService::generatePayQRcode($outTradeNo, $total, $notify);
        if ($result) {

            //创建订单
            $ret = IcbcOrderModel::query()->create([
                'mer_id'        => config('icbc.mer_id'),
                'app_id'        => config('icbc.app_id'),
                'user_id'       => $gsp_id > 0 ? 0: $user['profileID'], // 企业的话为0; 合伙人则合伙人ID
                'obj_id'        => $info['id'],
                'qrcode_msg_id' => $result['msg_id'],
                'out_trade_no'  => $outTradeNo,
                'qrcode'        => $result['qrcode'],
                'total_amt'     => $total,
                'attach'        => $result['attach'],
                'type'          => IcbcOrderModel::TYPE_GSP_REGISTER,
                'remark'        => 'AI账号开通费',
                'project'       => ProjectServiceModel::PAY_COMPANY_AIOPEN,  // 企业开户费
            ]);

            if (!$ret) {
                return responseFail('GENERATE_ORDER_ERROR');
            }

            $img = \SimpleSoftwareIO\QrCode\Facades\QrCode::format('png')->size(150)->generate($result['qrcode']);
            $img = 'data:image/png;base64,' . base64_encode($img);
            return responseSuccess(['qrcode'=>$img]);
        }

    }

    /**
     * 付款第三步成功回调
     * @return bool
     */
    public function notifyPayThree(Request $request)
    {
        $queryString = file_get_contents("php://input");

        // 解码查询字符串
        $decodedString = urldecode($queryString);

        // 将查询字符串解析为数组
        parse_str($decodedString, $data);

        $response_biz_content = isset($data['biz_content'])?json_decode($data['biz_content'], true):'';

        if ($response_biz_content && $response_biz_content['return_code'] == 0) {
            // 回调处理
            $order = IcbcOrderModel::query()->where('out_trade_no', $response_biz_content['out_trade_no'])->first();
            if ($order) {

                if ($order->pay_status == 0) {
                    try {
                        // 开启事务
                        DB::beginTransaction();

                        $gspId = $order->obj_id;
                        $gspCompany = CompanyGsp::where('id', $gspId)->first();

                        // step1 更新支付订单信息
                        $order->pay_msg_id = $response_biz_content['msg_id'];
                        $order->order_id = $response_biz_content['order_id'];
                        $order->pay_time = $response_biz_content['pay_time'];
                        $order->pay_status = 1;
                        $order->cust_id = $response_biz_content['cust_id'];
                        $order->card_no = $response_biz_content['card_no'];
                        $order->bank_name = isset($response_biz_content['bank_name']) ?? $response_biz_content['bank_name'];
                        $order->channel = $response_biz_content['channel'];
                        $order->attach = $response_biz_content['attach'];
                        $order->tp_cust_id = isset($response_biz_content['tp_cust_id']) ?? $response_biz_content['tp_cust_id'];
                        $order->trx_ser_no = isset($response_biz_content['trx_ser_no']) ?? $response_biz_content['trx_ser_no'];
                        $order->tp_order_id = isset($response_biz_content['tp_order_id']) ?? $response_biz_content['tp_order_id'];
                        $order->sub_open_id = isset($response_biz_content['sub_open_id']) ?? $response_biz_content['sub_open_id'];
                        $order->bank_type = isset($response_biz_content['bank_type']) ?? $response_biz_content['bank_type'];
                        $order->tp_user_id = isset($response_biz_content['tp_user_id']) ?? $response_biz_content['tp_user_id'];
                        $order->save();


                        // step2 更新付款成功状态
                        CompanyGsp::where('id', $gspId)->update(['pay_three' => 1]);

                        // 提交
                        DB::commit();

                        // 绿智地球入驻成功处理
                        event(new GspRegisterEvent($gspId));


                        return true;

                    } catch (\Exception $e) {
                        DB::rollBack();

                        Log::info($e->getMessage());
                        return false;
                    }
                } else if ($order->pay_status == 1) {
                    return true;
                }

            }

        }

        return false;
    }

    /**
     * 查询是否支付完成
     * @return void
     */
    public function checkPayStatus(Request $request)
    {
        $user = $request->attributes->get('user');
        $gsp_id = $request->attributes->get('gsp_id');
        $type = $request->input('type');

        if (empty($type)) {
            return responseFail(__('missing parameter', ['param' => 'type']));
        }

        // 企业信息
        $info = $this->getCurrentGsp($user, $gsp_id);
        if (empty($info)) {
            return responseFail(___('info no exist'));
        }

        $data = 0;
        if ($type == 1) {
            $data = $info['pay_one'];
        } else if ($type == 2) {
            $data = $info['pay_two'];
        } else if ($type == 3) {
            $data = $info['pay_three'];
        }

        return responseSuccess(['pay_status'=>$data]);
    }


    /**
     * 再次发邮件通知
     * @param Request $request
     * @return void
     */
    public function sendEmail(Request $request)
    {

    }

    /**
     * 企业用户登录
     */
    public function companyLogin(Request $request)
    {
        $phone = $request->input('phone');
        $code = $request->input('code');

        if (empty($phone)) {
            return responseFail(__('missing parameter', ['param' => 'phone']));
        }

        if (empty($code)) {
            return responseFail(__('missing parameter', ['param' => 'code']));
        }

        try {
            //短信验证码登录
            $this->smsService->verifySms($phone, $code, config('sms.verification_code_scene.company_login'));

            // 查找手机号是否存在
            $record = CompanyGsp::where('phone', $phone)->first();

            $payload = [
                'exp' => time() + 3600,
                'action' => self::ACTION_COMPANY_LOGIN,
                'gsp_id' => $record['id'],
            ];
            $token = JWT::encode($payload, env('JWT_KEY'), 'HS256');
            return responseSuccess(['token' => 'Bearer '.$token]);

        } catch (\Exception $e) {
            return responseFail(__('sms verification code error'));
        }

    }

    //发送验证码
    public function sendCode(Request $request)
    {
        $request->validate([
            'phone' => ['required', new CheckMobileRule],
        ]);
        $body = $request->all();
        $record = CompanyGsp::where('phone', $body['phone'])->first();
        if (!$record) {
            return responseFail(__('account error'));
        }
        //整合区号+手机号
        $resetPhoneData = (new CountryModel())->resetPhone(env('COUNTRY'), $body['phone']);
        $resetPhone = $resetPhoneData['phone'];
        $resetCountryCode = $resetPhoneData['countryCode'];

        return $this->smsService->sendVerificationCodeSms(
            $resetPhone, config('sms.verification_code_scene.company_login'), $resetCountryCode);
    }

    /**
     * 生成文件签名
     * @param Request $request
     * @return null
     */
    public function generateGspFileToken(Request $request)
    {
        $user = $request->attributes->get('user');
        $gsp_id = $request->attributes->get('gsp_id');
        $type = $request->input('type');
        $file_path = $request->input('file_path', '');
        if (empty($type)) {
            return responseFail(__('missing parameter', ['param' => 'type']));
        }

        $record = $this->getCurrentGsp($user, $gsp_id);
        if (empty($record)) {
            return responseFail($gsp_id);
        }

        if ($type == 1) { // 预审表格文件
            $file = $record['form'];
        } else if ($type == 2) { // 用户盖章上传预审表格
            $file = $record['form_complete'];
        } else if ($type == 3) { // 签约合同
            $contractFile = storage_path('pdf/gsp_contract.pdf');
            $newFile = storage_path('app/public/gsp/gsp_contract.pdf');
            copy($contractFile, $newFile);
            $file =  'gsp/gsp_contract.pdf';
        } else if ($type == 4) { // 尽调文件
            if (empty($file_path)) {
                return responseFail(__('missing parameter', ['param' => 'file_path']));
            }
            $file = $file_path;
        }

        if (empty($file)) {
            return responseFail();
        }

        $token = DocService::generateToken($file, self::ACTION, 3600);

        return responseSuccess($token);
    }

    public function previewFile(Request $request)
    {
        $token = $request->route('token');

        return DocService::preview($token, self::ACTION);
    }

    public function downloadFile(Request $request)
    {
        $token = $request->route('token');

        return DocService::download($token, self::ACTION);
    }


}
