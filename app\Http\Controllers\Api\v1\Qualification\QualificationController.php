<?php

namespace App\Http\Controllers\Api\v1\Qualification;

use App\Http\Controllers\Controller;
use App\Models\Qualification;
use App\Services\OssService;
use Illuminate\Http\Request;

class QualificationController extends Controller
{
    public function info(Request $request)
    {
        $user = $request->attributes->get('user');
        $data = Qualification::where('profileID', $user['profileID'])->first();
        if ($data) {
            $data['education'] = json_decode($data['education'],true);
            foreach( $data['education'] as &$education ){
                $education = OssService::link($education);
            }
            $data['certificate'] = json_decode($data['certificate'],true);
            foreach( $data['certificate'] as &$certificate ){
                $certificate = OssService::link($certificate);
            }
        }

        return responseSuccess($data);
    }

    public function edit(Request $request)
    {
        $body = $request->validate([
            'education' => ['file', 'mimes:jpg,jpeg,png,pdf', 'max:' . (env('ALLOW_FILE_SIZE') * 1024)],
            'certificate' => ['file', 'mimes:jpg,jpeg,png,pdf', 'max:' . (env('ALLOW_FILE_SIZE') * 1024)],
        ]);
        if (empty($body)) {
            return responseSuccess();
        }
        $educationPath = OssService::upload($request->file('education'));
        $certificatePath = OssService::upload($request->file('certificate'));
        $user = $request->attributes->get('user');
        Qualification::updateOrCreate(['profileID' => $user['profileID']], [
            'education' => $educationPath ?: '',
            'certificate' => $certificatePath ?: '',
        ]);

        return responseSuccess();
    }

    public function batchEdit(Request $request)
    {
        $body = $request->validate([
            'education.*' => ['file', 'mimes:jpg,jpeg,png,pdf', 'max:' . (env('ALLOW_FILE_SIZE') * 1024)],
            'certificate.*' => ['file', 'mimes:jpg,jpeg,png,pdf', 'max:' . (env('ALLOW_FILE_SIZE') * 1024)],
        ]);
        if (empty($body)) {
            return responseSuccess();
        }

        $educationPaths = [];
        $certificatePaths = [];

        if ($request->hasFile('education')) {
            foreach ($request->file('education') as $file) {
                $educationPaths[] = OssService::upload($file);
            }
        }

        if ($request->hasFile('certificate')) {
            foreach ($request->file('certificate') as $file) {
                $certificatePaths[] = OssService::upload($file);
            }
        }

        $user = $request->attributes->get('user');
        Qualification::updateOrCreate(['profileID' => $user['profileID']], [
            'education' => implode(',', array_filter($educationPaths)),
            'certificate' => implode(',', array_filter($certificatePaths)),
        ]);

        return responseSuccess();
    }
}
