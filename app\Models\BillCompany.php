<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class BillCompany extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'cna_bill_companies';

    protected $fillable = [
        'nameZH',
        'nameZT',
        'nameMS',
        'nameEN',
    ];

    public function bills()
    {
        return $this->hasMany(BillModel::class, 'billCompanyId');
    }
}
