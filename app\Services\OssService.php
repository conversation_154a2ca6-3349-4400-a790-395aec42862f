<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class OssService
{
    const API_KEY = 'c6d7ab55-fdc1-47b8-aebf-42a33aec5acb';

    public static function upload($file)
    {
        $path = '/api/v1/upload';
        $url = env('OSS_DOMAIN') . $path;
        $postData = [
            'apiKey' => self::API_KEY,
        ];
        $fileName = $file->getClientOriginalName();
        $filePath = $file->getRealPath();
        $response = Http::/* withoutVerifying()-> */attach('file', file_get_contents($filePath), $fileName)
            ->post($url, $postData);
        if ($response->successful()) {
            $res = $response->json();
            return $res['data']['url'] ?? false;
        }
        return false;
    }

    public static function link($path)
    {
        if (empty($path) || str_starts_with($path, 'https') || str_starts_with($path, 'http')) {
            return $path;
        }
        return env('OSS_DOMAIN').'/'.$path;
    }

    public static function uploadWebFile($fileUrl)
    {
        $path = '/api/v1/upload';
        $url = env('OSS_DOMAIN') . $path;
        $postData = [
            'apiKey' => self::API_KEY,
        ];
        $fileName = basename($fileUrl);

        $fileData = @file_get_contents($fileUrl);
        if ($fileData === false) {
            return $fileUrl;
        }

        $response = Http::/* withoutVerifying()-> */attach('file',$fileData , $fileName)
            ->post($url, $postData);

        if ($response->successful()) {
            $res = $response->json();
            return self::link($res['data']['url']) ?? false;
        }
        return false;
    }

}