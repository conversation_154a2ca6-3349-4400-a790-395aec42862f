<?php
namespace App\Http\Controllers\Admin\v1;

use App\Http\Controllers\Controller;
use App\Models\ActiveLog;
use App\Models\CompanyModel;
use App\Models\EmailTemplateModel;
use App\Models\InquiryCopyrightModel;
use Illuminate\Support\Facades\Validator;
use Symfony\Component\HttpFoundation\Request;

class EmailTemplateController extends Controller
{
    /**
     * 电邮模板列表
     * @return void
     */
    public function index(Request $request)
    {
        $pageSize = $request->get('page_size', 10);

        $list = EmailTemplateModel::query()->orderBy('ID', 'desc')->paginate($pageSize);
        $items = $list->items();

        $paginate = [
            'page_size' => $pageSize,
            'total' => $list->total(),
            'total_page' => $list->lastPage(),
        ];

        return responseSuccess(compact('items', 'paginate'));
    }

    /**
     * 查看邮件模板
     * @param Request $request
     * @param $id
     * @return void
     */
    public function edit(Request $request, $id)
    {
        if (empty($id)) {
            return responseFail(__('param error', ['param' => 'id']));// 参数缺失:id
        }

        // 获取详情
        $info = EmailTemplateModel::find($id);
        if (empty($info)) {
            return responseFail(__('info no exist'));// 信息不存在
        }

        return responseSuccess($info);
    }

    /**
     * 编辑邮箱
     * @param Request $request
     * @param $id
     * @return void
     */
    public function update(Request $request){

        $validator = Validator::make($request->all(), [
            'eventCode' => 'required',
        ]);

        // 验证失败
        if ($validator->fails()) {
            return responseFail($validator->errors()->all());
        }

        $param = $request->all();
        $user = $request->attributes->get('user');

        // 更新
        $result = EmailTemplateModel::updateOrCreate(
            ['eventCode' => $param['eventCode']],
            [
                'emailTitleEN' => $param['emailTitleEN'],
                'emailTitleMS' => $param['emailTitleMS'],
                'emailTitleZH' => $param['emailTitleZH'],
                'emailTitleZT' => $param['emailTitleZT'],
                'emailDescriptionEN' => $param['emailDescriptionEN'],
                'emailDescriptionMS' => $param['emailDescriptionMS'],
                'emailDescriptionZH' => $param['emailDescriptionZH'],
                'emailDescriptionZT' => $param['emailDescriptionZT'],
            ]

        );

        if ($result !== false) {
            return responseSuccess();
        } else {
            return responseFail(__('edit failed'));
        }

    }
}