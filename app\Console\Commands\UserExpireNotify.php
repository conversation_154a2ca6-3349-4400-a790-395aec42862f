<?php
namespace App\Console\Commands;

use App\Models\ProfileInfoModel;
use App\Services\EmailService;
use Illuminate\Console\Command;

class UserExpireNotify extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'user_expire_notify';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '账号过期发送通知';
    private $emailService;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(EmailService $emailService)
    {
        parent::__construct();
        $this->emailService = $emailService;
    }

    //提前1个月，3周，2周，1周，1天提醒
    public function handle()
    {
        $now = strtotime('today');
        //提前1天提醒
        $data = ProfileInfoModel::whereBetween('expire_time', [$now + 86400, $now + 86400 + 86400 - 1])
            ->pluck('profileID')->toArray();
        //提前1周提醒
        $data1 = ProfileInfoModel::whereBetween('expire_time', [$now + 86400*7, $now + 86400*7 + 86400 - 1])
            ->pluck('profileID')->toArray();
        //提前2周提醒
        $data2 = ProfileInfoModel::whereBetween('expire_time', [$now + 86400*14, $now + 86400*14 + 86400 - 1])
            ->pluck('profileID')->toArray();
        //提前3周提醒
        $data3 = ProfileInfoModel::whereBetween('expire_time', [$now + 86400*21, $now + 86400*21 + 86400 - 1])
            ->pluck('profileID')->toArray();
        //提前1个月提醒
        $data4 = ProfileInfoModel::whereBetween('expire_time', [$now + 86400*28, $now + 86400*28 + 86400 - 1])
            ->pluck('profileID')->toArray();
        $ids = array_merge($data, $data1, $data2, $data3, $data4);
        if (empty($ids)) {
            return;
        }
        foreach ($ids as $profileId) {
            //邮件通知
            $email = ProfileInfoModel::where('profileID', $profileId)->value('profileEmail');
            if (empty($email)) {
                continue;
            }
            $subject = '账号到期续费提醒'; //todo
            $content = '您的账号即将到期，请及时续费';
            $this->emailService->sendEmail($email, $subject, $content);
        }
    }
}

