<?php

namespace App\Services;

use App\Exceptions\AuthException;
use App\Models\IcbcOrderModel;
use App\Models\PaymentModel;
use App\Models\ProfileBusinessProcessLog;
use App\Models\ProfileBusinessProcessModel;
use App\Models\ProfileInfoModel;
use App\Models\ProfileLevelUpgadeLog;
use App\Models\ProjectCategoryModel;
use App\Models\ProjectServiceModel;
use App\Models\TeamRank;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class ProfileBusinessProcessServices {
    private $model;
    private $emailService;
    private $projectServicesDataServices;
    private $profileInfoModel;

    public function __construct(ProfileBusinessProcessModel $profileBusinessProcessModel,ProjectServiceDataServices $projectServicesDataServices,EmailService $emailService,ProfileInfoModel $profileInfoModel) {
        $this->model = $profileBusinessProcessModel;
        $this->projectServicesDataServices = $projectServicesDataServices;
        $this->emailService = $emailService;
        $this->profileInfoModel = $profileInfoModel;
    }

    public function getByProfileID($profileID,$code='',$status=ProfileBusinessProcessModel::STATUS_DOING,$getALL=false){
       $res = $this->model->select('id','profileID','project_id','project_code','project_remark','status','pay_id')
           ->with(['projectInfo'])
           ->where('profileID',$profileID)
           ->where('status',$status);
       if( !empty($code) ){
            $res->where('project_code',$code);
       }
       if($getALL){
           return $res->get();
       }else{
           return $res->first();
       }
    }

    public function create($profileID,$code){
        //检查$code 可用性
        $project = $this->projectServicesDataServices->getInfoByCode($code);
        if( empty ( $project ) ){
            throw new AuthException('不存在的业务');
        }
        $insertData = [
            'profileID' => $profileID,
            'project_id' => $project['id'],
            'project_code' => $project['code'],
            'project_remark' => $project['title_zh'],
            'status' => ProfileBusinessProcessModel::STATUS_DOING
        ];
        $res = $this->model->getConnection()->transaction(function() use ($insertData,$code){
            $info =  $this->model->create($insertData);
            if( !$info ){
                throw new AuthException('create Failed');
            }
            $this->log($info->id,$insertData);
            return true;
        });
        return $res;
    }

    public function check($id){
        $process = $this->model->find($id);
        if( $process->pay_id > 0 ){
            //检查支付状态
            $icbcOrder = IcbcOrderModel::select('id','pay_status')->find($process->pay_id);
            return $icbcOrder['pay_status'] ? true : false;
        }
        return $process->status == 1 ? true : false;
    }

    /**
     * 完成业务流程统一方法
     * @param $id
     * @return true
     */
    public function finished($processInfo){
        if( empty($processInfo['project_code']) ){
            throw new AuthException('业务信息错误');
        }
        $this->model->getConnection()->transaction(function() use ($processInfo){
            switch( $processInfo['project_code'] ){
                case 'profile_upgrade_partner_manager' :
                    $user = ProfileInfoModel::find($processInfo['profileID']);
                    $old_level = $user->role_level;
                    $user->role_level = ProfileInfoModel::ROLE_LEVEL_PROJECT[$processInfo['project_code']];
                    $user->save();
                    ProfileLevelUpgadeLog::create([
                        'profile_id' => $user->profileID,
                        'old_level' => $old_level,
                        'new_level' => $user->role_level,
                        'content' => '完成'.$processInfo['project_remark'].'业务'
                    ]);
                    break;
                case 'profile_upgrade_partner' : //支付信息业务
                    $user = ProfileInfoModel::find($processInfo['profileID']);
                    if( $user->role_level < ProfileInfoModel::ROLE_LEVEL_PROJECT[$processInfo['project_code']] ){
                        $old_level = $user->role_level;
                        $user->role_level = ProfileInfoModel::ROLE_LEVEL_PROJECT[$processInfo['project_code']];
                        $user->save();
                        ProfileLevelUpgadeLog::create([
                            'profile_id' => $user->profileID,
                            'old_level' => $old_level,
                            'new_level' => $user->role_level,
                            'content' => '完成'.$processInfo['project_remark'].'业务'
                        ]);
                    }
                    break;
                case 'profile_register' ://注册 涉及三三制业务
                    $this->profileRegister($processInfo);
                    break;
            }
            $res = $this->update($processInfo['id'],['status'=>ProfileBusinessProcessModel::STATUS_FINISHED]);
            if( !$res ){
                throw new AuthException('process update fail');
            }
        });
        return true;
    }

    public function update($id,$data){
        $info = $this->model->find($id);
        $res = $this->model->getConnection()->transaction(function() use ($id,$data,$info){
            $res = $info->update($data);
            if( !$res ){
                throw new AuthException('update Failed');
            }
            $this->log($info->id,$data);
            return true;
        });
        return $res;
    }

    protected function log($id, $data)
    {
        $content = '';
        if( isset($data['status']) ){
            switch($data['status']){
                case ProfileBusinessProcessModel::STATUS_FINISHED :
                    $content .= '业务流程完成';
                    break;
                case ProfileBusinessProcessModel::STATUS_CANCEL :
                    $content .= '业务撤回';
                    break;
                default :
                    $content .= '新增业务';
                    break;
            }
        }
        if( !empty($data['pay_id']) ){
            $content .= '绑定支付单';
        }
        return ProfileBusinessProcessLog::create([
            'profile_business_process_id' => $id,
            'content' => $content,
        ]);
    }

    public function profileRegister($processInfo){
        $profileId = $processInfo['profileID'];
        $user = ProfileInfoModel::where('profileID',$profileId )->first();
        if( $user->status == ProfileInfoModel::STATUS_PAYING ) {
            $update = ['status' => ProfileInfoModel::STATUS_VERIFYING];//三三制，检查人数是否超限
            if ($user['team_group'] && $user['pre_id']) {
                $recUser = ProfileInfoModel::checkRecommendTeam($user['pre_id']);
                if ($recUser === false) {
                    $update['team_group'] = 0;
                    $update['team_rank'] = 0;
                }
            }
            $update['profileRole'] = 1;
            ProfileInfoModel::where('profileID', $profileId)->update($update);
            if (empty($user['profilePassword'])) {
                //生成初始密码
                $password = generateRandomString(8);
                $hashPsw = hashPasswordNoKey($password);
                $user->profilePassword = ProfileInfoModel::INITIAL_PASSWORD_PREFIX . hashPassword($hashPsw);
                $user->save();
                // 获取邮箱模板
                $templateData = $this->emailService->getTemplateData('', 'InitialPassword', $profileId);
                $template = $templateData['htmlBody'];
                $subject = $templateData['subject'];
                $body = str_replace('{{password}}', $password, $template);
                $this->emailService->sendEmail($user['profileEmail'], $subject, $body);
            }
            // 加盟费用(已付)
            $projectId = ProjectCategoryModel::query()->where('type', 1)->value('projectCategoriesID');
            // 合伙人加盟注册其它待付项目明细
            $noPayProjectDetail = ProjectServiceModel::query()
                ->where('projectId', $projectId)
                ->where('remark', '<>', 'register.partner.fees')->get();
            if ($noPayProjectDetail) {
                foreach ($noPayProjectDetail as $projectDetailVal) {
                    $paymentNumber = PaymentModel::createPaymentNumber('WL', 2);
                    PaymentModel::query()->create([
                        'profileID' => $user['profileID'],
                        'companyID' => 0,
                        'type' => 2,
                        'projectId' => $projectId,  // 合伙人注册加盟
                        'detailId' => $projectDetailVal['id'],  // 合伙人加盟申请费
                        'fee' => $projectDetailVal['price'],  // 金额
                        'paymentNumber' => $paymentNumber, // 单号
                        'createtime' => date('Y-m-d H:i:s'),
                    ]);

                }
            }
            // 查询是否是管理合伙人带进来
            $pre_id = $user['pre_id'];
            $last_time = strtotime('2024-12-31 23:59:59');
            if ($pre_id > 0 && time() <= $last_time) { // 上级管理合伙人在12月31号前成功拉了一位合伙人,则免其它三张费用
                $joinFreeProject = ProjectServiceModel::JOIN_FREE_PROJECT;
                if ($joinFreeProject) {
                    PaymentModel::query()->where('profileID', $pre_id)
                        ->where('companyID', 0)
                        ->where('projectId', $projectId)
                        ->whereIn('detailId', $joinFreeProject)
                        ->whereNull('paytime')
                        ->update([
                            'paytime' => date('Y-m-d H:i:s'),
                        ]);
                    // 豁免的项目写对冲数据
                    foreach ($joinFreeProject as $joinFree) {
                        $joinFreeNumber = PaymentModel::createPaymentNumber('WL',2);
                        $fee =  PaymentModel::query()->where('profileID', $pre_id)
                            ->where('projectId', $projectId)
                            ->where('detailId', $joinFree)
                            ->value('fee');
                        PaymentModel::query()->create([
                            'profileID'       => $pre_id,
                            'companyID'       => 0,
                            'type'            => 2,
                            'projectId'       => $projectId,  // 合伙人注册加盟
                            'detailId'        => $joinFree,  // 豁免项目
                            'fee'             => -$fee,  // 金额
                            'paymentNumber'   => $joinFreeNumber, // 单号
                            'remark'          => '豁免',
                            'createtime'      => date('Y-m-d H:i:s'),
                            'paytime'         => date('Y-m-d H:i:s'),
                        ]);
                    }
                }

            }
            // 通知上级加盟成功
            if ($pre_id > 0) {
                // 发通知
                $this->preNotification($pre_id, $user['profileName']);
                // 发邮件
                $this->preEmail($pre_id, $user['profileName']);

            }

            $user->uuid = Str::uuid()->toString();
            $user->profilePartnerCode = $this->profileInfoModel->createPartnerCode($user['profileAddressCountry'], 1);
            $user->save();
        } else if ($user->status == ProfileInfoModel::STATUS_EXPIRE) {
            ProfileInfoModel::where('profileID', $profileId)
                ->update(['status' => ProfileInfoModel::STATUS_ACTIVE]);
        }
        // 站内通知
        $data = [
            'targetUser' => $user['profileID'],
        ];
        \App\Jobs\SendNotice::dispatch($data, 'PartnerPass', $user)->onQueue('SendNoticeJob');
    }

    /**
     * 管理合伙人和三三制入驻成功通知
     * @return void
     */
    public function preNotification($pre_id, $profileName)
    {
        $preUser = ProfileInfoModel::where('profileID', $pre_id)->first();
        $count = ProfileInfoModel::where('pre_id', $pre_id)->whereIn('status', [ProfileInfoModel::STATUS_VERIFYING, ProfileInfoModel::STATUS_ACTIVE])->count();

        // 查看上级身份
        $isTeam = ProfileInfoModel::getGroupById($pre_id);

        if ($isTeam == 3) { // 三三制

            // 下级等级
            $team_rank = $preUser['team_rank']-1;
            // 通知一：三三制加盟通知
            $data = [
                'targetUser'  =>  $pre_id,
                'profileName' =>  $profileName,
                'teamName'    =>  TeamRank::where('id', $team_rank)->value('job')
            ];
            \App\Jobs\SendNotice::dispatch($data, 'ChildRegister', $preUser)->onQueue('SendNoticeJob');

            if ($count == 3) { // 三三制完成KPI
                $data = [
                    'targetUser'  =>  $pre_id,
                    'teamName'    =>  TeamRank::where('id', $preUser['team_rank'])->value('job')
                ];
                \App\Jobs\SendNotice::dispatch($data, 'ChildSuccessKPI', $preUser)->onQueue('SendNoticeJob');
            }
        } else if ($isTeam == 2) { // 管理合伙人
            // 通知一：合伙人加盟通知
            $data = [
                'targetUser' =>  $pre_id,
                'profileName' =>  $profileName
            ];
            \App\Jobs\SendNotice::dispatch($data, 'ManagePartner2', $preUser)->onQueue('SendNoticeJob');

        }


        // 通知二：合伙人完成KPI
        if ($count == 1) { // 第一个进来
            $data = [
                'targetUser' =>  $pre_id,
            ];
            \App\Jobs\SendNotice::dispatch($data, 'ManageSuccessKPI', $preUser)->onQueue('SendNoticeJob');

        }

    }

    /**
     * 管理合伙人和三三制入驻成功发邮件
     * @return void
     */
    public function preEmail($pre_id, $profileName)
    {
        $preUser = ProfileInfoModel::where('profileID', $pre_id)->first();
        $preName = $preUser['profileName'];
        $count = ProfileInfoModel::where('pre_id', $pre_id)->whereIn('status', [ProfileInfoModel::STATUS_VERIFYING, ProfileInfoModel::STATUS_ACTIVE])->count();

        // 查看上级身份
        $isTeam = ProfileInfoModel::getGroupById($pre_id);
        if ($isTeam == 3) { // 三三制

            // 下级等级
            $team_rank = $preUser['team_rank']-1;

            // 邮件一：三三制加盟通知
            $teamName = TeamRank::where('id', $team_rank)->value('job');
            // 获取邮箱模板
            $templateData  = $this->emailService->getTemplateData('', 'ChildRegister', $pre_id);

            $subject = str_replace('{{profileName}}', $profileName, $templateData['subject']);
            $body = str_replace(['{{preName}}','{{profileName}}','{{teamName}}'],[$preName, $profileName,$teamName], $templateData['htmlBody']);
            $this->emailService->sendEmail($preUser['profileEmail'], $subject, $body);

            if ($count == 3) {
                // 邮件二：三三制完成KPI
                $teamName = TeamRank::where('id', $preUser['team_rank'])->value('job');
                $templateData  = $this->emailService->getTemplateData('', 'ChildSuccessKPI', $pre_id);

                $subject = str_replace('{{preName}}', $preName, $templateData['subject']);
                $body = str_replace(['{{preName}}', '{{teamName}}','{{profileName}}'], [$preName, $teamName,$profileName], $templateData['htmlBody']);
                $this->emailService->sendEmail($preUser['profileEmail'], $subject, $body);

            }
        } else if ($isTeam == 2) { // 管理合伙人

            // 邮件一：合伙人加盟通知
            $templateData  = $this->emailService->getTemplateData('', 'ManagePartner2', $pre_id);

            $subject = $templateData['subject'];
            $body = str_replace(['{{preName}}','{{profileName}}'], [$preName,$profileName], $templateData['htmlBody']);
            $this->emailService->sendEmail($preUser['profileEmail'], $subject, $body);
        }


        // 邮件二：合伙人完成KPI
        if ($count == 1) { // 第一个进来

            $templateData  = $this->emailService->getTemplateData('', 'ManageSuccessKPI', $pre_id);

            $subject = $templateData['subject'];
            $body = str_replace(['{{preName}}'], [$preName], $templateData['htmlBody']);
            $this->emailService->sendEmail($preUser['profileEmail'], $subject, $body);

        }

    }
}