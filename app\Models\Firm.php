<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Firm extends Model
{
    protected $table = 'cna_firm';
    protected $guarded = [];
    const CODE_PREFIX = 'CNF'; //邀请码前缀

    public function profileInfo()
    {
        return $this->belongsTo(ProfileInfoModel::class,'profile_id');
    }

    /**
     * 生成编码
     * @param $profileID
     * @return string
     */
    public static function createCode($profileID = '')
    {
        // 当前最大编码数
        $curMaxCode = self::query()
            ->orderBy('code', 'desc')
            ->limit(1)
            ->value('code');
        if ($curMaxCode) {
            $number = preg_replace("/[^0-9]/", "", $curMaxCode);
            $number += 1;

        } else {
            $number = 10000;
        }

        // 查找用户国际编码
        $code = self::CODE_PREFIX . $number;

        return $code;
    }

}
