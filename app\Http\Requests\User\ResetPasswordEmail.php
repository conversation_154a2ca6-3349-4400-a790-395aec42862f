<?php

namespace App\Http\Requests\User;

use App\Rules\Password\CheckResPswEmailRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use App\Exceptions\DefaultException;

class ResetPasswordEmail extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'email' => ['required', 'email', new CheckResPswEmailRule]
        ];
    }

    public function messages()
    {
        return [
            'email.required' => '请填写邮箱地址',
            'email.email' => '请正确填写邮箱地址'
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        $error= $validator->errors()->all();
        throw new DefaultException($error[0]);
    }


}
