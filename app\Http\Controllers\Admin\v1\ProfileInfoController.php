<?php

namespace App\Http\Controllers\Admin\v1;

use App\Events\CheckPartnerEvent;
use App\Http\Controllers\Controller;
use App\Models\ActiveLog;
use App\Models\BankModel;
use App\Models\CompanyGsp;
use App\Models\DepartmentModel;
use App\Models\Interview;
use App\Models\NotificationInfoModel;
use App\Models\PaymentModel;
use App\Models\ProfileInfoModel;
use App\Models\TeamRank;
use App\Models\WechatOrderModel;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Http\Request;
use App\Models\Qualification;
use App\Models\BusinessCase;
use App\Services\OssService;

class ProfileInfoController extends Controller
{
    private $notificationInfoModel;
    private $profileInfoModel;

    public function __construct(NotificationInfoModel $notificationInfoModel, ProfileInfoModel $profileInfoModel)
    {
        $this->notificationInfoModel = $notificationInfoModel;
        $this->profileInfoModel = $profileInfoModel;
    }

    /**
     * 合伙人列表
     * @param Request $request
     * @return null
     */
    public function partnerList(Request $request)
    {
        $request->validate([
            'status' => 'nullable|in:' . implode(',', array_keys(ProfileInfoModel::$statusMap)),
        ]);

        $pageSize = $request->get('page_size', 10);
        $status = $request->status;
        $keyword = $request->get('keyword'); // 关键字
        $preName = $request->get('pre_name', ''); // 上级推荐人
        $profilePartnerCode = $request->get('profilePartnerCode', ''); // 合伙人编码
        $profileName = $request->get('profileName', ''); // 合伙人姓名
        $profileEmail = $request->get('profileEmail', ''); // 合伙人邮箱
        $profileContact = $request->get('profileContact', ''); // 合伙人联系方式
        $profileNRIC = $request->get('profileNRIC', ''); // 合伙人身份证号码
        $bankAccount = $request->get('bank_account', ''); // 开户行

        $list = ProfileInfoModel::with([
            'nationality',
            'idFile:profile_id,face_file,back_file',
            'bankFile:profile_id,file',
            'contractFile:profileID,contract',
            'parent',
            'bank',
            'setting',
            'newExperiences.experience',
            'newExperiences.country',
            'newExperiences.industry',
            'adminReviewStatus',
        ])
            ->activedNormalAssociate()
            ->when($status, function ($query) use ($status) {
                $query->where('status', $status);
            })
            ->when($keyword != '', function ($query) use ($keyword) {
                $query->where(function ($query) use ($keyword) {
                    $query->orWhere('profileName', 'like', '%' . $keyword . '%')
                        ->orWhere('profileEmail', 'like', '%' . $keyword . '%')
                        ->orWhere('profileContact', 'like', '%' . $keyword . '%')
                        ->orWhere('profileAddressCountry', 'like', '%' . $keyword . '%')
                        ->orWhere('profilePartnerCode', 'like', '%' . $keyword . '%')
                        ->orWhere('profileNRIC', 'like', '%' . $keyword . '%');
                });
            })
            ->when($preName != '', function ($query) use ($preName) {
                $query->whereHas('parent', function ($query) use ($preName) {
                    $query->where('profileName', 'like', '%' . $preName . '%');
                });
            })
            ->when($profilePartnerCode != '', function ($query) use ($profilePartnerCode) {
                $query->where('profilePartnerCode', 'like', '%' . $profilePartnerCode . '%');
            })
            ->when($profileName != '', function ($query) use ($profileName) {
                $query->where('profileName', 'like', '%' . $profileName . '%');
            })
            ->when($profileEmail != '', function ($query) use ($profileEmail) {
                $query->where('profileEmail', 'like', '%' . $profileEmail . '%');
            })
            ->when($profileContact != '', function ($query) use ($profileContact) {
                $query->where('profileContact', 'like', '%' . $profileContact . '%');
            })
            ->when($profileNRIC != '', function ($query) use ($profileNRIC) {
                $query->where('profileNRIC', 'like', '%' . $profileNRIC . '%');
            })
            ->when($bankAccount != '', function ($query) use ($bankAccount) {
                $query->where('bank_account', 'like', '%' . $bankAccount . '%');
            })
            ->orderBy('profilePartnerCode', 'asc')->paginate($pageSize);

        $arrData = [];
        $items = collect($list->items())->map(function ($item) {
            // 当前所属组(1联盟合伙人; 2管理合伙人; 3三三制)
            $item->groupId = ProfileInfoModel::getGroupById($item->profileID);
            $item->manageCount = 0; // 管理合伙人数量
            $item->teamCount = 0;  // 三三制合伙人数量
            $item->rankName = ''; // 三三制等级
            if ($item->groupId == 2) {
                $item->manageCount = ProfileInfoModel::getManagerCount($item->profileID);
            } else if ($item->groupId == 3) {
                $item->teamCount = ProfileInfoModel::getTeamCount($item->profileID);
                $item->rankName = TeamRank::where('id', $item->team_rank)->value('job');
            }

            // 上级推荐人姓名
            $item->preName = $item->parent->profileName ?? '';

            // 三三制最顶级上级为总部
            if ($item->groupId == 3 && $item->team_rank == 11) {
                $item->preName = '总部';
            }

            // 开户行
            $item->bankName = $item->bank->bankName ?? '';
            $item->profileAvatar = $item->profileAvatar ? storageUrl($item->profileAvatar) : null;

            // 个人设置内容
            if ($item->setting) {
                $item->settingLanguage = $item->setting->language;
                $item->settingTimezone = $item->setting->timezone;
                $item->settingDateFormat = $item->setting->date_format;
                $item->settingTimeFormat = $item->setting->time_format;
                $item->settingCurrency = $item->setting->currency;
                $item->settingNotifyType = $item->setting->notify_type;
                $item->settingNotifyEmergency = $item->setting->notify_emergency;
                $item->settingNotifySuspiciousOperation = $item->setting->notify_suspicious_operation;
                $item->settingNotifySafeUpdated = $item->setting->notify_safe_updated;
                $item->settingNotifyRecPrivateMsg = $item->setting->notify_rec_private_msg;
                $item->settingNotifyImportanceUpdate = $item->setting->notify_importance_update;
                $item->settingNotifySystemUpdate = $item->setting->notify_system_update;
                $item->settingNotifyJoinInvestigate = $item->setting->notify_join_investigate;
                $item->settingNotifyBill = $item->setting->notify_bill;
            }

            return $item;
        });

        $paginate = [
            'page_size' => $pageSize,
            'total' => $list->total(),
            'total_page' => $list->lastPage(),
        ];

        return responseSuccess(compact('items', 'paginate'));
    }

    /**
     * 合伙人详情
     * @param Request $request
     * @return void
     */
    public function partnerDetail(Request $request, $id = 0)
    {

        if (empty($id)) {
            return responseFail(__('param error', ['param' => 'id']));  // 参数缺失:id
        }

        // 获取详情
        $info = ProfileInfoModel::with([
            'nationality',
            'newExperiences.experience',
            'newExperiences.country',
            'newExperiences.industry',
            'adminReviewStatus',
            'aiStationInterview',
        ])
            ->find($id);

        if (empty($info)) {
            return responseFail(__('info no exist'));  // 合伙人信息不存在
        }

        // 最高学历与资格证书
        $data = Qualification::where('profileID', $id)->first();
        if ($data) {
            $info['education'] = OssService::link($data['education']);
            $info['certificate'] = OssService::link($data['certificate']);
        }

        // 业务说明
        $info['businessCase'] = BusinessCase::where('profileID', $id)->first();

        return responseSuccess($info);
    }

    /**
     * 审核合伙人
     * @param Request $request
     * @return void
     */
    public function partnerCheck(Request $request)
    {
        $id = $request->post('id');
        $status = $request->post('status', ''); // 审核状态：1审核通过; 2审核不通过
        $user = $request->attributes->get('user');

        if (empty($id)) {
            return responseFail(__('missing parameter', ['param' => 'id']));
        }

        if (empty($status)) {
            return responseFail(__('missing parameter', ['param' => 'status'])); // 审核状态不能为空
        }

        if (!in_array($status, [1, 2])) {
            return responseFail(__('param error', ['param' => 'id']));
        }

        // 获取详情
        $profileInfo = ProfileInfoModel::find($id);
        if (empty($profileInfo)) {
            return responseFail(__('info no exist'));
        }

        if ($profileInfo->status > ProfileInfoModel::STATUS_VERIFYING) {
            return responseFail(__('param error', ['param' => 'status']));
        }

        if ($profileInfo->status == ProfileInfoModel::STATUS_PAYING) {
            return responseFail(__('login not pay')); // 当前合伙人未付款
        }

        //更新面试状态
        $interviewUpdate = Interview::where('user_id',$id)->update(['status'=>$status]);
        if( empty($interviewUpdate) ){
            return responseFail(__('interview update error')); // 当前合伙人未付款
        }

        // 触发审核合伙人事件
        $result = event(new CheckPartnerEvent($profileInfo, $status));

        if ($result) {
            // 记录日志
            ActiveLog::log(
                $user['profileID'],
                ActiveLog::ADMIN_API_V1_CHECK,
                ActiveLog::ADMIN_API_V1_ASSOCIATE,
                $id,
                $profileInfo['profileName'],
                ActiveLog::SYSTEM_CNA_ADMIN
            );

            return responseSuccess();
        } else {
            return responseFail('error');
        }
    }

    /**
     * 更新合伙人信息
     * @param Request $request
     * @return void
     */
    public function partnerEdit(Request $request, $id = 0)
    {
        if (empty($id)) {
            return responseFail(__('param error', ['param' => 'id']));
        }

        $validator = Validator::make($request->all(), [
            'profileName'   => 'required',
            'profileNRIC' => 'required',
            'profileAddressUnit' => 'required',
            'profileAddressStreet' => 'required',
            'profileAddressDistrict' => 'required',
            'profileAddressCity' => 'required',
            'profileAddressState'    => 'required',
            'profileAddressPostcode' => 'required',
            'profileAddressCountry'  => 'required',
        ]);

        // 验证失败
        if ($validator->fails()) {
            return responseFail($validator->errors()->all());
        }

        // 获取详情
        $profileInfo = ProfileInfoModel::find($id);
        if (empty($profileInfo)) {
            return responseFail(__('info no exist')); //合伙人信息不存在
        }

        if ($profileInfo->status <= ProfileInfoModel::STATUS_VERIFYING) {
            return responseFail(__('login review ing')); // 当前合伙人还没有通过审核, 不能修改信息
        }

        $user = $request->attributes->get('user');
        $param = $request->all();

        try {

            // 开启事务
            DB::beginTransaction();

            $profileInfo->update([
                'profileName'           => $request->profileName,
                'profileNRIC'           => $request->profileNRIC,
                'profileAddressUnit'    => $request->profileAddressUnit,
                'profileAddressStreet'  => $request->profileAddressStreet,
                'profileAddressDistrict' => $request->profileAddressDistrict,
                'profileAddressCity'    => $request->profileAddressCity,
                'profileAddressState'   => $request->profileAddressState,
                'profileAddressPostcode' => $request->profileAddressPostcode,
                'profileAddressCountry' => $request->profileAddressCountry,
                'profileBirthDate'      => $request->profileBirthDate,
                'editUser'              => $user['profileID'],
                'editTime'              => date('Y-m-d H:i:s'),
                'editRole'              => $user['profileRole']
            ]);

            // 专业
            $professionalArr = json_decode($request->professionals, true);
            if ($professionalArr) {
                $professionals = [];
                foreach ($professionalArr as $professional) {
                    $professionals[$professional['professionalID']] = [
                        'professionalDescription' => $professional['professionalDescription'],
                    ];
                }
                $profileInfo->professions()->sync($professionals);
            }


            // 技能
            $skillsArr = json_decode($request->skills, true);
            if ($skillsArr) {
                $skills = [];
                foreach ($skillsArr as $skill) {
                    $skills[$skill['skillID']] = [
                        'skillDescription' => $skill['skillDescription'],
                    ];
                }
                $profileInfo->skills()->sync($skills);
            }


            // 经验
            $experiencesArr = json_decode($request->experiences, true);
            if ($experiencesArr) {
                $experiences = [];
                foreach ($experiencesArr as $experience) {
                    $experiences[$experience['experienceID']] = [
                        'experienceDescription' => $experience['experienceDescription'],
                    ];
                }
                $profileInfo->experiences()->sync($experiences);
            }

            // 提交
            DB::commit();

            // 记录日志
            ActiveLog::log(
                $user['profileID'],
                ActiveLog::ADMIN_API_V1_EDIT,
                ActiveLog::ADMIN_API_V1_ASSOCIATE,
                $id,
                $param['profileName'],
                ActiveLog::SYSTEM_CNA_ADMIN
            );

            return responseSuccess();
        } catch (\Exception $e) {
            DB::rollBack();
            return responseFail(__('save failed'));
        }
    }

    /**
     * 合伙人--个人设置
     * @param Request $request
     * @param $id
     * @return void
     */
    public function partnerSetting(Request $request, $id)
    {
        if (empty($id)) {
            return responseFail(__('param error', ['param' => 'id']));
        }

        $validator = Validator::make($request->all(), [
            'settingLanguage'   => 'required',
            'settingTimezone' => 'required',
            'settingDateFormat' => 'required',
            'settingTimeFormat' => 'required',
            'settingCurrency' => 'required',
            'notifyType'    => 'required',
            'notifySafeUpdated' => 'required',
            'notifySuspiciousOperation'  => 'required',
            'notifyRecPrivateMsg'  => 'required',
            'notifyImportanceUpdate'  => 'required',
            'notifyJoinInvestigate'  => 'required',
            'notifySystemUpdate'  => 'required',
            'notifyEmergency'  => 'required',
            'notifyBill'  => 'required',
        ]);

        // 验证失败
        if ($validator->fails()) {
            return responseFail($validator->errors()->all());
        }

        // 获取详情
        $profileInfo = ProfileInfoModel::find($id);
        if (empty($profileInfo)) {
            return responseFail(__('info no exist')); //合伙人信息不存在
        }

        $param = $request->all();
        $user = $request->attributes->get('user');

        $result = $profileInfo->setting()->updateOrCreate(['profile_id' => $id], [
            'language' => $param['settingLanguage'],
            'timezone' => $param['settingTimezone'],
            'date_format' => $param['settingDateFormat'],
            'time_format' => $param['settingTimeFormat'],
            'currency' => $param['settingCurrency'],
            'notify_type' => $param['notifyType'],
            'notify_emergency' => $param['notifyEmergency'],
            'notify_suspicious_operation' => $param['notifySuspiciousOperation'],
            'notify_safe_updated' => $param['notifySafeUpdated'],
            'notify_rec_private_msg' => $param['notifyRecPrivateMsg'],
            'notify_importance_update' => $param['notifyImportanceUpdate'],
            'notify_system_update' => $param['notifySystemUpdate'],
            'notify_join_investigate' => $param['notifyJoinInvestigate'],
            'notify_bill' => $param['notifyBill'],
            // todo need update profileInfo too?
            // 'editUser'                         => $user['profileID'],
            // 'editTime'                         => date('Y-m-d H:i:s'),
            // 'editRole'                         => $user['profileRole']
        ]);

        if ($result) {

            // 记录日志
            ActiveLog::log(
                $user['profileID'],
                ActiveLog::ADMIN_API_V1_SET,
                ActiveLog::ADMIN_API_V1_ASSOCIATE,
                $id,
                $profileInfo['profileName'],
                ActiveLog::SYSTEM_CNA_ADMIN
            );

            return responseSuccess();
        } else {
            return responseFail(__('save failed'));
        }
    }

    /**
     * 账号列表
     * @param Request $request
     * @param $type 账号类型: 2 行政管理人员; 3律师
     * @return null
     */
    public function accountList(Request $request, $type)
    {

        $pageSize = $request->get('page_size', 10);
        $keyword = $request->get('keyword'); // 关键字

        if (!in_array($type, [2, 3])) {
            return responseFail(__('param error', ['param' => 'type']));
        }

        $list = ProfileInfoModel::query()
            ->when($type != '', function ($query) use ($type) {
                return $query->where('profileRole', $type);
            })
            ->when($keyword != '', function ($query) use ($keyword) {
                $query->where(function ($q) use ($keyword) {
                    $q->orWhere('profileName', 'like', '%' . $keyword . '%')
                        ->orWhere('profileEmail', 'like', '%' . $keyword . '%')
                        ->orWhere('profileContact', 'like', '%' . $keyword . '%');
                });
            })->orderBy('createTime', 'desc')->paginate($pageSize);

        $items = collect($list->items())->map(function ($item) {
            $item->profileAvatar = $item->profileAvatar ? storageUrl($item->profileAvatar) : null;
            $item->department_name = '';
            if ($item['department_id'] > 0) {
                $item->department_name = DepartmentModel::where('id', $item['department_id'])->value('name');
            }

            return $item;
        });

        $paginate = [
            'page_size' => $pageSize,
            'total' => $list->total(),
            'total_page' => $list->lastPage(),
        ];

        return responseSuccess(compact('items', 'paginate'));
    }

    /**
     * 创建账号
     * @return void
     */
    public function createAccount(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'type'              => 'required', // 行政-2，律师-3
            'profileEmail'      => 'required',
            'profilePassword'   => 'required',
            'profileName'       => 'required',
            'profileContact'    => 'required',
            'department_id'     => 'required'
        ]);

        // 验证失败
        if ($validator->fails()) {
            return responseFail($validator->errors()->all());
        }

        $type = $request->type;
        $profileEmail = $request->profileEmail;
        $profilePassword = $request->profilePassword;
        $profileName = $request->profileName;
        $profileContact = $request->profileContact;
        $lawyerOffice  = $request->lawyerOffice;  // 律师事务所
        $profileAddressCountry = $request->profileAddressCountry; // 国籍
        $profileNRIC  = $request->profileNRIC; // 身份证
        $department_id = $request->department_id; // 部门ID
        $user = $request->attributes->get('user');

        if (!in_array($type, [2, 3])) {
            return responseFail(__('param error', ['param' => 'type']));
        }

        if ($type == 3 && empty($lawyerOffice)) {
            return responseFail(__('missing parameter', ['param' => 'lawyerOffice']));
        }

        if ($type == 3 && empty($profileAddressCountry)) {
            return responseFail(__('missing parameter', ['param' => 'profileAddressCountry']));
        }

        if ($type == 3 && empty($profileNRIC)) {
            return responseFail(__('missing parameter', ['param' => 'profileNRIC']));
        }

        //检验邮箱、手机号是否被注册过
        $registerRec = $this->profileInfoModel->registerRec($profileEmail, $profileContact);
        if ($registerRec) {
            return responseFail(__('team phone/email exist'));
        }

        // 生成编码
        if ($type == 2) { // 行政新建默认是中国
            $profileAddressCountry = 44;
        }
        $profilePartnerCode = $this->profileInfoModel->createPartnerCode($profileAddressCountry, $type);

        //新增用户信息
        $insertData = [
            'profileName'  => $profileName,
            'profileEmail' => $profileEmail,
            'profilePassword' => hashPassword($profilePassword),
            'profileContact' => $profileContact,
            'status' => ProfileInfoModel::STATUS_ACTIVE,
            'profileRole'    => $type,
            'profilePartnerCode' => $profilePartnerCode,
            'department_id'   => $department_id,
            'createUser'     => $user['profileID'],
            'createRole'     => $user['profileRole'],
            'createTime'     => date('Y-m-d H:i:s'),
        ];
        if ($type == 3) {
            $insertData['lawyerOffice'] = $lawyerOffice;
            $insertData['profileAddressCountry'] = $profileAddressCountry;
            $insertData['profileNRIC'] = $profileNRIC;
        }
        $result = ProfileInfoModel::create($insertData);

        if ($result) {

            // 记录日志
            ActiveLog::log(
                $user['profileID'],
                ActiveLog::ADMIN_API_V1_CREATE,
                ActiveLog::ADMIN_API_V1_OA,
                $result->profileID,
                $profileName,
                ActiveLog::SYSTEM_CNA_ADMIN
            );

            return responseSuccess();
        } else {
            return responseFail(__('save failed'));
        }
    }

    /**
     * 查看账号详情
     * @param Request $request
     * @param $id
     * @return void
     */
    public function editAccount(Request $request, $id)
    {
        if (empty($id)) {
            return responseFail(__('param error', ['param' => 'id'])); // 参数缺失:id
        }

        // 获取详情
        $info = ProfileInfoModel::find($id);
        if (empty($info)) {
            return responseFail(__('info no exist')); // 信息不存在
        }

        return responseSuccess($info);
    }

    /**
     * 更新账户
     * @param Request $request
     * @param $id
     * @return void
     */
    public function updateAccount(Request $request, $id)
    {
        if (empty($id)) {
            return responseFail(__('param error', ['param' => 'id'])); // 参数缺失:id
        }

        $validator = Validator::make($request->all(), [
            'profileEmail'      => 'required',
            'profilePassword'   => 'required',
            'profileName'       => 'required',
            'profileContact'    => 'required',
        ]);

        // 验证失败
        if ($validator->fails()) {
            return responseFail($validator->errors()->all());
        }

        $parms = $request->all();
        $user = $request->attributes->get('user');

        // 获取详情
        $info = ProfileInfoModel::find($id);
        if (empty($info)) {
            return responseFail(__('info no exist')); // 信息不存在
        }

        // 律师必填项
        if ($info['profileRole'] == 3 && empty($parms['lawyerOffice'])) {
            return responseFail(__('missing parameter', ['param' => 'lawyerOffice']));
        }

        if ($info['profileRole'] == 3 && empty($parms['profileAddressCountry'])) {
            return responseFail(__('missing parameter', ['param' => 'profileAddressCountry']));
        }

        if ($info['profileRole'] == 3 && empty($parms['profileNRIC'])) {
            return responseFail(__('missing parameter', ['param' => 'profileNRIC']));
        }

        // 更新信息
        $updateData = [
            'profileEmail' => $parms['profileEmail'],
            'profilePassword' => hashPassword($parms['profilePassword']),
            'profileName' => $parms['profileName'],
            'profileContact' => $parms['profileContact'],
            'editUser'                  => $user['profileID'],
            'editTime'                  => date('Y-m-d H:i:s'),
            'editRole'                  => $user['profileRole']
        ];
        if ($info['profileRole']  == 3) {
            $updateData['lawyerOffice'] = $parms['lawyerOffice'];
            $updateData['profileAddressCountry'] = $parms['profileAddressCountry'];
            $updateData['profileNRIC'] = $parms['profileNRIC'];
        }

        // 更新
        $result = $info->update($updateData);

        if ($result !== false) {
            // 记录日志
            ActiveLog::log(
                $user['profileID'],
                ActiveLog::ADMIN_API_V1_EDIT,
                ActiveLog::ADMIN_API_V1_OA,
                $id,
                $parms['profileName'],
                ActiveLog::SYSTEM_CNA_ADMIN
            );

            return responseSuccess();
        } else {
            return responseFail(__('edit failed'));
        }
    }

    /**
     * 删除账户
     * @return void
     */
    public function destroyAccount(Request $request, $id)
    {
        if (empty($id)) {
            return responseFail(__('param error', ['param' => 'id'])); // 参数缺失:id
        }

        // 获取详情
        $info = ProfileInfoModel::find($id);
        if (empty($info)) {
            return responseFail(__('info no exist')); // 信息不存在
        }

        $user = $request->attributes->get('user');
        $profileName = $info['profileName'];

        // 删除
        $result =  $info->delete();
        if ($result !== false) {

            // 记录日志
            ActiveLog::log(
                $user['profileID'],
                ActiveLog::ADMIN_API_V1_DELETE,
                ActiveLog::ADMIN_API_V1_OA,
                $id,
                $profileName,
                ActiveLog::SYSTEM_CNA_ADMIN
            );

            return responseSuccess();
        } else {
            return responseFail(__('delete failed'));
        }
    }

    /**
     * 设置三三制总
     * @param Request $request
     * @return void
     */
    public function setTeamTop(Request $request)
    {
        $id = $request->post('id');

        // 获取详情
        $info = ProfileInfoModel::find($id);
        if (empty($info)) {
            return responseFail(__('info no exist')); // 信息不存在
        }

        if ($info['team_group'] > 0) {
            return responseFail(__('param error', ['param' => 'team_group'])); // 参数缺失:id
        }

        // 设置三三制总
        $info->team_group = $id;
        $info->team_rank = TeamRank::query()->max('id');
        $info->save();

        return responseSuccess();
    }


    //修改上级
    public function changePre(Request $request)
    {
        $body = $request->validate([
            'profileID' => ['required', 'integer', 'min:1'],
            'pre_id' => ['required', 'integer', 'min:1'],
            'is_team' => ['required', 'in:0,1'],
        ]);
        $preUser = ProfileInfoModel::where('profileID', $body['pre_id'])->first();
        $user = ProfileInfoModel::where('profileID', $body['profileID'])->first();
        if (!$preUser || !$user) {
            return responseFail();
        }
        $update = [];
        if ($body['is_team']) {
            if ($user['team_group']) {
                return responseFail('该用户已经在其他三三制');
            }
            $preUserTeam = ProfileInfoModel::checkRecommendTeam($body['profileID']);
            if (!$preUserTeam) {
                return responseFail('选定的上级用户不能添加下级');
            }
            $update['team_group'] = $preUser['team_group'];
            $update['team_rank'] = $preUser['team_rank'] - 1;
        }
        $update['pre_id'] = $body['pre_id'];
        ProfileInfoModel::where('profileID', $body['profileID'])->update($update);

        return responseSuccess();
    }

    /**
     * 部门列表
     * @param Request $request
     * @return void
     */
    public function department(Request $request)
    {
        $list = DepartmentModel::query()->get();
        return responseSuccess($list);
    }

    /**
     * 修改用户密码
     * @param Request $request
     * @return void
     */
    public function updatePassword(Request $request)
    {
        $id = $request->input('id');
        $password = $request->input('password');

        if (empty($id)) {
            return responseFail(__('param error', ['param' => 'id'])); // 参数缺失:id
        }

        if (empty($password)) {
            return responseFail(__('param error', ['param' => 'password'])); // 参数缺失:password
        }

        // 获取详情
        $info = ProfileInfoModel::where('profileRole', 2)->find($id);
        if (empty($info)) {
            return responseFail(__('info no exist')); // 信息不存在
        }


        $info->profilePassword = hashPassword(hashPasswordNoKey($password));
        $info->save();

        return responseSuccess();
    }

    /**
     * 合伙人的团队架构
     */
    public function team(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|exists:cna_profile_info,profileID',
        ]);

        $team = ProfileInfoModel::with(['descendants.teamRank:id,job', 'teamRank:id,job'])
            ->select('profileID', 'pre_id', 'profileName', 'profilePartnerCode', 'team_rank')
            ->find($request->id);

        return responseSuccess($team);
    }

    /**
     * 合伙人选项（前端）
     */
    public function options()
    {
        return responseSuccess(ProfileInfoModel::options());
    }

    /**
     * 行政审核合伙人的状态
     */
    public function markReviewStatus(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|exists:cna_profile_info,profileID',
            'type' => 'required|string|in:info_status,register_payment_status',
        ]);

        $loginedUser = $request->attributes->get('user');

        $profileInfo = ProfileInfoModel::find($request->id);

        try {
            DB::beginTransaction();

            $profileInfo->adminReviewStatus()->updateOrCreate(
                ['profile_id' => $request->id],
                [$request->type => 1]
            );

            // 记录日志
            ActiveLog::log(
                $loginedUser['profileID'],
                ActiveLog::ADMIN_API_V1_EDIT,
                ActiveLog::ADMIN_API_V1_OA,
                $request->id,
                $profileInfo->profileName,
                ActiveLog::SYSTEM_CNA_ADMIN
            );

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            return responseFail($e->getMessage());
        }

        return responseSuccess();
    }

    // 合伙人付款记录
    public function payout(Request $request)
    {
        $pageSize = $request->get('page_size', 11);
        $page = $request->get("page", 1);
        $id = $request->get('id');

        if (empty($id)) {
            return responseFail(__('param error', ['param' => 'id'])); // 参数缺失:id
        }

        // 获取详情
        $info = ProfileInfoModel::find($id);
        if (empty($info)) {
            return responseFail(__('info no exist')); // 信息不存在
        }

        $list = PaymentModel::select('id', 'profileID', 'project_name', 'detail_name', 'paymentNumber', 'fee', 'paytime', 'pay_type', 'createtime')->where('type', 2)
            ->where('profileID', $id)
            ->orderBy('created_at', 'desc')->paginate($pageSize);

        $items = $list->items();

        $paginate = [
            'page_size' => $pageSize,
            'total' => $list->total(),
            'total_page' => $list->lastPage(),
        ];

        return responseSuccess(compact('items', 'paginate'));
    }

    // 企业客户
    public function customer(Request $request)
    {
        $pageSize = $request->get('page_size', 11);
        $page = $request->get("page", 1);
        $id = $request->get('id');

        if (empty($id)) {
            return responseFail(__('param error', ['param' => 'id'])); // 参数缺失:id
        }

        // 获取详情
        $info = ProfileInfoModel::find($id);
        if (empty($info)) {
            return responseFail(__('info no exist')); // 信息不存在
        }

        $list = CompanyGsp::where('profile_id', $id)
            ->orderBy('created_at', 'desc')->paginate($pageSize);

        $items = $list->items();

        $paginate = [
            'page_size' => $pageSize,
            'total' => $list->total(),
            'total_page' => $list->lastPage(),
        ];

        return responseSuccess(compact('items', 'paginate'));
    }
}
