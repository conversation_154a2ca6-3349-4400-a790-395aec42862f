<?php
namespace App\Http\Controllers\Admin\v1;

use App\Http\Controllers\Controller;
use App\Models\ActiveLog;
use App\Models\CompanyModel;
use App\Models\InquiryLitigationModel;
use App\Models\InquiryReportModel;
use Illuminate\Support\Facades\Validator;
use Symfony\Component\HttpFoundation\Request;

class InquiryLitigationController extends Controller
{
    /**
     * 添加诉讼记录
     * @param Request $request
     * @return void
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'companyName'         => 'required',
            'caseNumber'        => 'required',
            'defendant'         => 'required',
            'acceptInstitution' => 'required',
        ]);

        // 验证失败
        if ($validator->fails()) {
            return responseFail($validator->errors()->all());
        }

        $param = $request->all();

        // 查询companyID
        $companyID = CompanyModel::query()->where('companyName', $param['companyName'])->value('companyID');
        if (empty($companyID)) {
            return responseFail('公司系统不存在!');
        }

        $result = InquiryLitigationModel::query()->create([
            'companyID'          => $companyID,
            'caseNumber'        => $param['caseNumber'],
            'causeAction'       => $param['causeAction'],
            'plaintiff'         => $param['plaintiff'],
            'defendant'         => $param['defendant'],
            'acceptInstitution' => $param['acceptInstitution'],
            'lawsuitAmount'     => $param['lawsuitAmount'],
            'executionStatus'   => $param['executionStatus'],
        ]);

        if ($result) {
            return responseSuccess();
        } else {
            return responseFail(__('save failed'));
        }
    }
}