name: Rsync Deploy

on:
  push:
    branches:
      - ai-desk-api
jobs:
  rsync-deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v2

      - name: Install rsync and sshpass
        run: |
          sudo apt-get update
          sudo apt-get install -y rsync sshpass

      - name: Cu Cloud Deploy
        run: |
           rsync -avz --exclude='.env' --exclude='.git' --exclude='.github' -e "sshpass -p SoPmxZZQI8Pa4biRd8fi ssh -o StrictHostKeyChecking=no" . corporate-advisory-api-ai-desk@**************:/home/<USER>/htdocs/api.ai-desk.corporate-advisory.cn