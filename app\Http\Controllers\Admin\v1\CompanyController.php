<?php
namespace App\Http\Controllers\Admin\v1;

use App\Events\CheckCompanyEvent;
use App\Http\Controllers\Admin\v1\Project\ProgressOneController;
use App\Http\Controllers\Controller;
use App\Models\ActiveLog;
use App\Models\BankModel;
use App\Models\CompanyModel;
use App\Models\NotificationInfoModel;
use App\Models\PaymentCategoryModel;
use App\Models\ProfileInfoModel;
use App\Models\ProjectProgressFourFileModel;
use App\Models\ProjectProgressFourUserFileModel;
use App\Models\ProjectProgressOneModel;
use App\Models\ProjectProgressOnePdfModel;
use App\Models\ProjectProgressThreeModel;
use App\Models\ProjectProgressTwoModel;
use App\Models\ProjectServiceModel;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Symfony\Component\HttpFoundation\Request;

class CompanyController extends Controller
{
    private $notificationInfoModel;

    public function __construct(NotificationInfoModel $notificationInfoModel) {
        $this->notificationInfoModel = $notificationInfoModel;
    }

    /**
     * 客户列表
     * @param Request $request
     * @return null
     */
    public function index(Request $request)
    {
        $pageSize = $request->get('page_size', 10);
        $status = $request->get('status', ''); // 状态:0待审核; 1已完成;
        $keyword = $request->get('keyword'); // 关键字
        $profileID = $request->get('profileID');

        if ($status!= '' && !in_array($status, [0, 1])) {
            return responseFail(__('param error', ['param'=>'status'])); // 状态数值错误
        }

        $list = CompanyModel::query()->when($status != '', function ($query) use ($status) {
            if ($status == 0) { // 待审核
                $query->where('companyState', 1)->whereIn('companyProgressState', [1,2]); // 步骤1，2 状态是1审核中
            } else if ($status == 1) { // 已完成
                $query->whereIn('companyProgressState', [3,4]); // 步骤全部完成(包括完成前面两个步骤的客户)
            }

        })->when($keyword != '', function ($query) use ($keyword) {
            $query->where(function ($query) use ($keyword) {
                $query->orWhere('companyName', 'like', '%' . $keyword . '%')
                    ->orWhere('companyRegisterCode', 'like', '%' . $keyword . '%')
                    ->orWhere('companyEmail', 'like', '%' . $keyword . '%')
                    ->orWhere('companyMobile', 'like', '%' . $keyword . '%');
            });
        })->when($profileID != '', function ($query) use ($profileID) { // 合伙人
            return $query->where('companyProfileID', $profileID);
        })->orderBy('createTime', 'desc')->paginate($pageSize);

        $items = $list->items();

        $paginate = [
            'page_size' => $pageSize,
            'total' => $list->total(),
            'total_page' => $list->lastPage(),
        ];

        return responseSuccess(compact('items', 'paginate'));
    }

    /**
     * 客户详情
     * @param Request $request
     * @param $id
     * @return void
     * @throws BindingResolutionException
     */
    public function edit(Request $request, $id)
    {
        if (empty($id)) {
            return responseFail(__('param error', ['param' => 'id']));// 参数缺失:id
        }

        // 获取详情
        $info = CompanyModel::find($id);
        if (empty($info)) {
            return responseFail(__('info no exist'));// 客户信息不存在
        }

        // 获取进度表一信息
        $info['progressOneInfo'] = $this->getProgressOneInfo($id);
        // 获取进度表二信息
        $info['progressTwoInfo'] = $this->getProgressTwoInfo($id);
        // 获取进度表三信息
        $info['progressThreeInfo'] = $this->getProgressThreeInfo($id);
        // 获取进度表四信息
        $info['progressFourInfo'] = $this->getProgressFourInfo($id);

        return responseSuccess($info);

    }

    /**
     * 编辑客户信息
     * @param Request $request
     * @param $id
     * @return void|null
     */
    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'companyName' => 'required',
            'companyRegisterCode' => 'required',
            'companyCategoriesID' => 'required',
        ]);

        // 验证失败
        if ($validator->fails()) {
            return responseFail($validator->errors()->all());
        }
        if (empty($id)) {
            return responseFail(__('param error', ['param' => 'id']));// 参数缺失:id
        }

        // 获取详情
        $info = CompanyModel::find($id);
        if (empty($info)) {
            return responseFail(__('info no exist'));// 客户信息不存在
        }

        $param = $request->all();
        $user = $request->attributes->get('user');

        // 更新
        $result = $info->update([
            'companyName' => $param['companyName'],
            'companyRegisterCode' => $param['companyRegisterCode'],
            'companyCategoriesID' => $param['companyCategoriesID'],
            'editUser' => $user['profileID'],
            'editRole' => $user['profileRole'],
            'editTime' => date('Y-m-d H:i:s'),
        ]);

        if ($result !== false) {

            // 记录日志
            ActiveLog::log($user['profileID'], ActiveLog::ADMIN_API_V1_EDIT, ActiveLog::ADMIN_API_V1_CLIENT,
                $id, $info['companyName'], ActiveLog::SYSTEM_CNA_ADMIN);

            return responseSuccess();
        } else {
            return responseFail(__('edit failed'));
        }
    }

    /**
     * 客户资料审核
     * @param Request $request
     * @param $id
     * @return void
     */
    public function check(Request $request)
    {
        $id = $request->get('id');
        $status = $request->get('status', ''); // 审核状态：1审核通过; 2审核不通过
        $user = $request->attributes->get('user');

        if (empty($id)) {
            return responseFail(__('param error', ['param' => 'id']));// 参数缺失:id
        }

        if (empty($status)) {
            return responseFail(__('missing parameter', ['param'=>'status'])); // 审核状态不能为空
        }

        if (!in_array($status, [1,2])) {
            return responseFail(__('param error', ['param'=>'status'])); // 审核状态不能为空
        }

        // 获取详情
        $info = CompanyModel::find($id);
        if (empty($info)) {
            return responseFail(__('info no exist'));// 客户信息不存在
        }

        // 查看当前状态
        if (!in_array($info['companyProgressState'], [1,2])) {
            return responseFail(__('param error', ['param'=>'status'])); // 审核状态不能为空
        }

        if ($info['companyState'] !=1) {
            return responseFail(__('Not submitted'));// 当前不是提审状态
        }


        // 触发客户审核事件
        $result = event(new CheckCompanyEvent($info, $status));

        if ($result) {
            // 记录日志
            ActiveLog::log($user['profileID'],
                ActiveLog::ADMIN_API_V1_CHECK,
                ActiveLog::ADMIN_API_V1_CLIENT,
                $id, $info['companyName'], ActiveLog::SYSTEM_CNA_ADMIN);

            return responseSuccess();
        } else {
            return responseFail(__('Check failed'));
        }

    }

    /**
     * 尽调报告
     * @return void
     */
    public function reports(Request $request)
    {
        $companyID = $request->get('companyID');
        if (empty($companyID)) {
            return responseFail(__('param error', ['param' => 'id']));
        }

        $list = ProjectProgressFourUserFileModel::with('files')->where('companyID', $companyID)->get();
        $items = $list->map(function ($item) {
            $item->file = $item->file ? storageUrl($item->file) : null;
            $item->filename = $item->files->fileName;
            $item->isRequired = $item->files->isRequired;
            unset($item->files);
            return $item;
        });

        return responseSuccess($items);
    }

    /**
     * 获取进度表一信息
     * @param $companyID
     * @return mixed
     */
    public function getProgressOneInfo($companyID)
    {
        // 获取进度一详情
        $info = ProjectProgressOneModel::where('companyID', $companyID)->first();
        if ($info) {
            // PDF文件
            $pdf = ProjectProgressOnePdfModel::where('companyID', $companyID)->first();
            $info['upload_file'] = $pdf ? storageUrl($pdf->file) : null;

        }
        $data['info'] = $info;
        // 获取所有服务项目
        $data['customerService'] = ProjectServiceModel::where('projectCategoriesID', 1)->get();
        return $data;
    }

    /**
     * 获取进度表二信息
     * @param $companyID
     * @return mixed
     */
    public function getProgressTwoInfo($companyID)
    {
        // 获取付款信息
        $info = ProjectProgressTwoModel::where('companyID', $companyID)->first();
        if ($info) {
            $info['file'] = $info['file'] ? storageUrl($info['file']) : null;
            // 银行卡
            $info['bank'] = BankModel::find($info['bankID']);
            // 付款方式
            $info['payment'] = PaymentCategoryModel::find($info['paymentID']);
        }

        return $info;
    }

    /**
     * 获取进度表三信息
     * @param $companyID
     * @return mixed
     */
    public function getProgressThreeInfo($companyID) {
        $data = ProjectProgressThreeModel::where('companyID', $companyID)->first();
        return $data;
    }

    /**
     * 获取进度表四信息
     * @param $companyID
     * @return mixed
     */
    public function getProgressFourInfo($companyID) {
        $datas = ProjectProgressFourUserFileModel::with(['files'])->where('companyID', $companyID)->get();
        if ($datas) {
            foreach ($datas as $key => $item) {
                $item['file'] = $item['file'] ? storageUrl($item['file']) : null;
                $datas[$key] =$item;
            }
        }
        return $datas;
    }
}