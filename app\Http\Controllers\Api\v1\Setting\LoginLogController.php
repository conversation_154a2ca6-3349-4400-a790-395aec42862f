<?php

namespace App\Http\Controllers\Api\v1\Setting;

use App\Http\Controllers\Controller;
use App\Models\ActiveLog;
use App\Models\LoginLog;
use Illuminate\Http\Request;

class LoginLogController extends Controller
{
    public function list(Request $request)
    {
        $user = $request->attributes->get('user');
        $token = $request->bearerToken();
        $pageSize = $request->post('pageSize', 10);
        $data = LoginLog::where('profile_id', $user['profileID'])->orderByDesc('id')->paginate($pageSize);
        $list = $data->toArray();
        $list = $list['data'];
        $currentPos = 0;
        foreach ($list as $k => &$item) {
            $item['current'] = 0;
            if ($item['token'] == $token) {
                $item['current'] = 1;
                $currentPos = $k;
            }
            $item['create_time'] = strtotime($item['created_at']);
            unset($item['token']);
        }
        //将当前记录移动到第一位
        if ($data->onFirstPage() && $currentPos) {
            while ($currentPos > 0) {
                $tmp = $list[$currentPos];
                $list[$currentPos] = $list[$currentPos-1];
                $list[$currentPos-1] = $tmp;
                $currentPos--;
            }
        }
        $currentPage = $data->currentPage();
        $perPage = $data->perPage();
        $totalRecord = $data->total();
        $totalPage = $data->lastPage();
        $paginate = [
            'currentPage' => $currentPage,
            'perPage' => $perPage,
            'totalRecord' => $totalRecord,
            'totalPage' => $totalPage
        ];
        $compact = compact('list', 'paginate');

        return responseSuccess($compact);
    }

    public function quit(Request $request)
    {
        $validated = $request->validate([
            'id' => ['required', 'integer', 'min:0'],
        ]);
        $user = $request->attributes->get('user');
        $update = ['token_status' => 0];
        if ($validated['id']) {
            //退出一个
            $res = LoginLog::select('id', 'profile_id')->where('id', $validated['id'])->first();
            if (empty($res) || $res['profile_id'] != $user['profileID']) {
                return responseSuccess();
            }
            $res->token_status = 0;
            $res->save();
        } else {
            //全部退出（除了当前）
            $token = $request->bearerToken();
            $res = LoginLog::select('id', 'token')->where('token_status', 1)
                ->where('profile_id', $user['profileID'])->get();
            $ids = [];
            foreach ($res as $item) {
                if ($item['token'] != $token) {
                    $ids[] = $item['id'];
                }
            }
            if ($ids) {
                LoginLog::whereIn('id', $ids)->update(['token_status' => 0]);
            }
            $update['id'] = $ids;
        }
        //记录活动日志
        ActiveLog::log($user['profileID'], ActiveLog::ACTIVE_UPDATE, ActiveLog::API_V1_COMPANY_CREATECOMPANY,
            $validated['id'], $update);

        return responseSuccess();
    }
}
