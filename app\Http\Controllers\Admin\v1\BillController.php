<?php

namespace App\Http\Controllers\Admin\v1;

use App\Http\Controllers\Controller;
use App\Models\ActiveLog;
use App\Models\BillModel;
use App\Models\ProfileInfoModel;
use Illuminate\Support\Facades\Validator;
use Symfony\Component\HttpFoundation\Request;

class BillController extends Controller
{
    /**
     * 账单列表
     * @return void
     */
    public function index(Request $request)
    {

        $pageSize = $request->get('page_size', 11);
        $keyword = $request->get('keyword'); // 关键字
        $profileID = $request->get('profileID'); // 合伙人ID
        $billCompanyID = $request->get('billCompanyID'); // 企业用户ID

        $list = BillModel::query()->with('profileInfo')
            ->when($profileID != '', function ($query) use ($profileID) { // 合伙人
                return $query->where('billProfileID', $profileID)->where('billCompanyID', 0);
            })
            ->when($billCompanyID != '', function ($query) use ($billCompanyID) { // 企业
                return $query->where('billCompanyID', $billCompanyID);
            })
            ->when($keyword != '', function ($query) use ($keyword) {
                $query->where(function ($query) use ($keyword) {
                    $query->where('billCompanyName', 'like', '%' . $keyword . '%')
                        ->orWhere('billDescription', 'like', '%' . $keyword . '%');
                });
            })->orderBy('created_at', 'desc')->paginate($pageSize);

        $items = collect($list->items())->map(function ($item) {

            $item->profileName  = $item->profileInfo->profileName;
            $item->profileEmail = $item->profileInfo->profileEmail;
            unset($item->profileInfo);
            return $item;
        });

        $paginate = [
            'page_size' => $pageSize,
            'total' => $list->total(),
            'total_page' => $list->lastPage(),
        ];

        return responseSuccess(compact('items', 'paginate'));
    }

    /**
     * 上传账单
     * @param Request $request
     * @return void
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'billProfileID' => 'required',
            'billCompanyName' => 'required',
            'billDescription' => 'required',
            'billAmount' => 'required',
            'createTime' => 'required',
        ]);

        // 验证失败
        if ($validator->fails()) {
            return responseFail($validator->errors()->all());
        }

        $billProfileID = $request->get('billProfileID');
        $billCompanyName = $request->get('billCompanyName');
        $billDescription = $request->get('billDescription');
        $billAmount = $request->get('billAmount');
        $createTime = $request->get('createTime');
        $user = $request->attributes->get('user');
        $user_id = $user['profileID'];

        // 查看合伙人是否存在
        $profileInfo = ProfileInfoModel::where('profileID', $billProfileID)->where('profileRole', 1)->first();
        if (!$profileInfo) {
            return responseFail(__('info no exist'));
        }

        $result = BillModel::query()->create([
            'billProfileID'     => $billProfileID,
            'billCompanyName'   => $billCompanyName,
            'billDescription'   => $billDescription,
            'billAmount'        => $billAmount,
            'createTime'        => $createTime,
            'createUser'        => $user_id,
            'createRole'        => $user['profileRole'],
        ]);

        // 发送通知

        if ($result) {
            // 记录日志
            ActiveLog::log(
                $user['profileID'],
                ActiveLog::ADMIN_API_V1_CREATE,
                ActiveLog::ADMIN_API_V1_RETURN_BILL,
                $result['billID'],
                $profileInfo['profileName'] . '-' . $billCompanyName . '-' . $billDescription . '(' . $billAmount . ')',
                ActiveLog::SYSTEM_CNA_ADMIN
            );
            return responseSuccess();
        } else {
            return responseFail(__('save failed'));
        }
    }

    /**
     * 账单详情
     * @param Request $request
     * @param $id
     * @return void
     */
    public function edit(Request $request, $id)
    {
        if (empty($id)) {
            return responseFail(__('param error', ['param' => 'id'])); // 参数缺失:id
        }

        // 获取详情
        $info = BillModel::with('profileInfo')->find($id);
        if (empty($info)) {
            return responseFail(__('info no exist')); // 客户信息不存在
        }

        $info->profileName  = $info->profileInfo->profileName;
        $info->profileEmail = $info->profileInfo->profileEmail;
        unset($info->profileInfo);

        return responseSuccess($info);
    }

    /**
     * 编辑账单
     * @param Request $request
     * @param $id
     * @return void
     */
    public function update(Request $request, $id)
    {

        $validator = Validator::make($request->all(), [
            'billCompanyName' => 'required',
            'billDescription' => 'required',
            'billAmount' => 'required',
            'createTime' => 'required',
        ]);

        // 验证失败
        if ($validator->fails()) {
            return responseFail($validator->errors()->all());
        }

        if (empty($id)) {
            return responseFail(__('param error', ['param' => 'id'])); // 参数缺失:id
        }

        // 获取详情
        $info = BillModel::find($id);
        if (empty($info)) {
            return responseFail(__('info no exist')); // 账单信息不存在
        }

        $billCompanyName = $request->get('billCompanyName');
        $billDescription = $request->get('billDescription');
        $billAmount = $request->get('billAmount');
        $createTime = $request->get('createTime');
        $user = $request->attributes->get('user');


        // 更新
        $result = $info->update([
            'billCompanyName' => $billCompanyName,
            'billDescription' => $billDescription,
            'billAmount' => $billAmount,
            'createTime' => $createTime,
            'editUser' => $user['profileID'],
            'editRole' => $user['profileRole'],
            'editTime' => date('Y-m-d H:i:s'),
        ]);

        // 发送通知
        if ($result !== false) {
            // 记录日志
            ActiveLog::log(
                $user['profileID'],
                ActiveLog::ADMIN_API_V1_EDIT,
                ActiveLog::ADMIN_API_V1_RETURN_BILL,
                $id,
                $billCompanyName . '-' . $billDescription . '(' . $billAmount . ')',
                ActiveLog::SYSTEM_CNA_ADMIN
            );

            return responseSuccess();
        } else {
            return responseFail(__('edit failed'));
        }
    }

    /**
     * 账单删除
     * @param Request $request
     * @param $id
     * @return void
     */
    public function destroy(Request $request, $id)
    {
        if (empty($id)) {
            return responseFail(__('param error', ['param' => 'id'])); // 参数缺失:id
        }

        // 获取详情
        $info = BillModel::find($id);
        if (empty($info)) {
            return responseFail(__('info no exist')); // 账单信息不存在
        }
        $desc = $info['billCompanyName'] . '-' . $info['billDescription'];
        $user = $request->attributes->get('user');

        // 删除
        $result =  $info->delete();
        if ($result !== false) {
            // 记录日志
            ActiveLog::log(
                $user['profileID'],
                ActiveLog::ADMIN_API_V1_DELETE,
                ActiveLog::ADMIN_API_V1_RETURN_BILL,
                $id,
                $desc,
                ActiveLog::SYSTEM_CNA_ADMIN
            );
            return responseSuccess();
        } else {
            return responseFail(__('delete failed'));
        }
    }
}
