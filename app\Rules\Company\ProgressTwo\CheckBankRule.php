<?php

namespace App\Rules\Company\ProgressTwo;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use App\Models\BankModel;

class CheckBankRule implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (!BankModel::where('ID', $value)->exists()) {
            $fail('请选择银行');
        }
    }
}
