<?php
namespace App\Console\Commands;

use App\Models\NotificationDataModel;
use App\Models\NotificationInfoModel;
use Illuminate\Console\Command;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class NotifiyValidityClean extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'notifiy_validity_clean';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '定期清理过期通知用户数据';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $now = Carbon::now();

        // 过期的群发通知
        $ids = NotificationInfoModel::query()
                ->where('notificationTarget',  0)
                ->where('notificationValidity', '<', $now)
                ->pluck('notificationID')
                ->toArray();

        // 用户通知表
        $count = NotificationDataModel::query()->whereIn('notificationID', $ids)->delete();

        Log::info('执行成功:清除'.$count.'条记录');
    }
}

