<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cna_bill_companies', function (Blueprint $table) {
            $table->id();
            $table->string('nameZH')->comment('中文名');
            $table->string('nameZT')->comment('繁体名');
            $table->string('nameMS')->comment('马来名');
            $table->string('nameEN')->comment('英文名');
            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('cna_bill_descriptions', function (Blueprint $table) {
            $table->id();
            $table->string('descriptionZH')->comment('中文描述');
            $table->string('descriptionZT')->comment('繁体描述');
            $table->string('descriptionMS')->comment('马来描述');
            $table->string('descriptionEN')->comment('英文描述');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cna_bill_companies');
        Schema::dropIfExists('cna_bill_descriptions');
    }
};
