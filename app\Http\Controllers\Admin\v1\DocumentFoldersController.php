<?php
namespace App\Http\Controllers\Admin\v1;

use App\Http\Controllers\Controller;
use App\Models\DocumentFoldersModel;
use App\Models\DocumentInfoModel;
use Illuminate\Support\Facades\Validator;
use Illuminate\Http\Request;

class DocumentFoldersController extends Controller
{
    /**
     * 资料列表
     * @param Request $request
     * @return void
     */
    public function index(Request $request) {
        $pageSize = $request->get('page_size', 10);
        $keyword = $request->get('keyword'); // 关键字

        // 查询列表
        $list = DocumentFoldersModel::query()->when($keyword != '', function ($query) use ($keyword) {
                $query->orWhere('foldersNameEN', 'like', '%' . $keyword . '%')
                      ->orWhere('foldersNameMS', 'like', '%' . $keyword . '%')
                      ->orWhere('foldersNameZH', 'like', '%' . $keyword . '%')
                      ->orWhere('foldersNameZT', 'like', '%' . $keyword . '%');
                })->orderBy('created_at', 'desc')->paginate($pageSize);

        $items = $list->items();

        $paginate = [
            'page_size' => $pageSize,
            'total' => $list->total(),
            'total_page' => $list->lastPage(),
        ];

        return responseSuccess(compact('items', 'paginate'));
    }

    /**
     * 获取文件夹下文件
     * @param Request $request
     * @return void
     */
    public function files(Request $request, $id)
    {
        $pageSize = $request->get('page_size', 10);

        if (empty($id)) {
            return responseFail(__('param error', ['param' => 'id']));// 参数缺失:id
        }

        // 获取详情
        $info = DocumentFoldersModel::find($id);
        if (empty($info)) {
            return responseFail(__('info no exist'));// 信息不存在
        }

        // 文件夹下文件列表
        $list = DocumentInfoModel::query()->where('folderId', $id)->orderBy('createTime', 'desc')->paginate($pageSize);

        $items = $list->items();

        $paginate = [
            'page_size' => $pageSize,
            'total' => $list->total(),
            'total_page' => $list->lastPage(),
        ];

        return responseSuccess(compact('items', 'paginate'));
    }

    /**
     * 创建文件夹
     * @param Request $request
     * @return void
     */
    public function store(Request $request) {
        $validator = Validator::make($request->all(), [
            'foldersNameEN' => 'required',
            'foldersNameMS' => 'required',
            'foldersNameZH' => 'required',
            'foldersNameZT' => 'required',
        ]);
        // 验证失败
        if ($validator->fails()) {
            return responseFail($validator->errors()->all());
        }

        $params      = $request->all();
        $user = $request->attributes->get('user');

        // step2 添加资料表
        $result = DocumentFoldersModel::query()->create([
            'foldersNameEN' => $params['foldersNameEN'],
            'foldersNameMS' => $params['foldersNameMS'],
            'foldersNameZH' => $params['foldersNameZH'],
            'foldersNameZT' => $params['foldersNameZT'],
            'parentId'      => $params['parentId'],
            'order'         => $params['order']
        ]);

        if ($result !== false) {
            return responseSuccess();
        } else {
            return responseFail(__('save failed'));
        }



    }

    /**
     * 查看文件夹
     * @param Request $request
     * @param $id
     * @return void
     */
    public function edit(Request $request, $id)
    {
        if (empty($id)) {
            return responseFail(__('param error', ['param' => 'id']));// 参数缺失:id
        }

        // 获取详情
        $info = DocumentFoldersModel::find($id);
        if (empty($info)) {
            return responseFail(__('info no exist'));// 信息不存在
        }

        return responseSuccess($info);
    }

    /**
     * 编辑文件夹
     * @param Request $request
     * @param $id
     * @return void
     */
    public function update(Request $request, $id){

        $validator = Validator::make($request->all(), [
            'foldersNameEN' => 'required',
            'foldersNameMS' => 'required',
            'foldersNameZH' => 'required',
            'foldersNameZT' => 'required',
        ]);

        // 验证失败
        if ($validator->fails()) {
            return responseFail($validator->errors()->all());
        }

        if (empty($id)) {
            return responseFail(__('param error', ['param' => 'id']));// 参数缺失:id
        }

        // 获取详情
        $info = DocumentFoldersModel::find($id);
        if (empty($info)) {
            return responseFail(__('info no exist'));// 信息不存在
        }

        $params      = $request->all();
        $user = $request->attributes->get('user');

        $result = $info->update([
            'foldersNameEN' => $params['foldersNameEN'],
            'foldersNameMS' => $params['foldersNameMS'],
            'foldersNameZH' => $params['foldersNameZH'],
            'foldersNameZT' => $params['foldersNameZT'],
            'parentId'      => $params['parentId'],
            'order'         => $params['order']
        ]);


        if ($result !== false) {
            return responseSuccess();
        } else {
            return responseFail(__('edit failed'));
        }

    }


    /**
     * 删除文件夹
     * @param Request $request
     * @param $id
     * @return void
     */
    public function destroy(Request $request, $id) {
        if (empty($id)) {
            return responseFail(__('param error', ['param' => 'id']));// 参数缺失:id
        }

        // 获取详情
        $info = DocumentFoldersModel::find($id);
        if (empty($info)) {
            return responseFail(__('info no exist'));// 信息不存在
        }

        $user = $request->attributes->get('user');

        // 是否有文件在下面
        $file = DocumentInfoModel::query()->where('folderId', $id)->count();
        if ($file > 0) {
            return responseFail(__('delete failed'));
        }

        // 删除
        $result =  $info->delete();
        if ($result !== false) {

            return responseSuccess();
        } else {
            return responseFail(__('delete failed'));
        }
    }


}