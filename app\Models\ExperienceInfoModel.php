<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ExperienceInfoModel extends Model
{
    use HasFactory;

    protected $table = 'cna_experience_info';
    protected $guarded = [];

    /**
     * Method 获取数据集
     *
     * @return void
     */
    public function getData()
    {
        return self::select('experienceID', 'experienceTitleZH')->get();
    }
    
    /**
     * Method 获取数据记录
     *
     * @param $professionalID $professionalID [主键]
     *
     * @return void
     */
    public function getRecord($experienceID)
    {
        return self::select('experienceID', 'experienceTitleZH')
                    ->where('experienceID', $experienceID)
                    ->first();
    }
}
