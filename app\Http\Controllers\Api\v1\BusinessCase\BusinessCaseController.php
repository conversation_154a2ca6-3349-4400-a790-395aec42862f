<?php

namespace App\Http\Controllers\Api\v1\BusinessCase;

use App\Http\Controllers\Controller;
use App\Models\BusinessCase;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class BusinessCaseController extends Controller
{
    public function info(Request $request)
    {
        $user = $request->attributes->get('user');
        $data = [
            'business_case' => [],
            'ai_case' => []
        ];
        $list = BusinessCase::where('profileID', $user['profileID'])->get();
        foreach( $list as $item ) {
            if ($item['type'] == BusinessCase::TYPE_CASE_NORMAL) {
                unset($item['ai_experience']);
                $data['business_case'][] = $item;
            } else {
                $data['ai_case'][] = [
                    'type' => $item['type'],
                    'name' => $item['name'],
                    'content' => $item['content']
                ];
            }
        }
        return responseSuccess($data);
    }

    public function edit(Request $request)
    {
        $body = $request->validate([
            'type' => ['required', 'integer', 'in:'.implode(',',BusinessCase::TYPE_CASE_ALL)],
            'name' => $request->input('type') == BusinessCase::TYPE_CASE_NORMAL ? ['required'] : ['nullable'],
            'address' => $request->input('type') == BusinessCase::TYPE_CASE_NORMAL ? ['required'] : ['nullable      '],
            'date' => $request->input('type') == BusinessCase::TYPE_CASE_NORMAL ? ['required'] : ['nullable'],
            'content' => ['required'],
            'result' => $request->input('type') == BusinessCase::TYPE_CASE_NORMAL ? ['required'] : ['nullable'],
        ], [
            'type.required' => '案例类型不能为空',
            'type.integer' => '案例类型必须是整数',
            'type.in' => '案例类型值无效',
            'name.required' => '案例名称不能为空',
            'address.required' => '案例地址不能为空',
            'date.required' => '案例日期不能为空',
            'content.required' => '案例内容不能为空',
            'result.required' => '案例结果不能为空',
        ]);
        $user = $request->attributes->get('user');
        if( $body['type'] == BusinessCase::TYPE_CASE_AI_TOOL ){
            $body['name'] = "AI工具使用经验";
        }
        BusinessCase::updateOrCreate(
            ['profileID' => $user['profileID'],'type'=>$body['type'], 'name' => $body['name']],
        $body);

        return responseSuccess();
    }

    public function batchEdit(Request $request)
    {
        $body = $request->validate([
            'type' => ['required', 'integer', 'in:'.implode(',',BusinessCase::TYPE_CASE_ALL)],
            'business_cases' => ['required', 'array'],
            'business_cases.*.name' => $request->input('type') == BusinessCase::TYPE_CASE_NORMAL ? ['required'] : ['nullable'],
            'business_cases.*.address' => $request->input('type') == BusinessCase::TYPE_CASE_NORMAL ? ['required'] : ['nullable'],
            'business_cases.*.date' => $request->input('type') == BusinessCase::TYPE_CASE_NORMAL ? ['required'] : ['nullable'],
            'business_cases.*.content' => ['required'],
            'business_cases.*.result' => $request->input('type') == BusinessCase::TYPE_CASE_NORMAL ? ['required'] : ['nullable'],
        ], [
            'type.required' => '案例类型不能为空',
            'type.integer' => '案例类型必须是整数',
            'type.in' => '案例类型值无效',
            'business_cases.required' => '案例数据不能为空',
            'business_cases.array' => '案例数据格式错误',
            'business_cases.*.name.required' => '案例名称不能为空',
            'business_cases.*.address.required' => '案例地址不能为空',
            'business_cases.*.date.required' => '案例日期不能为空',
            'business_cases.*.content.required' => '案例内容不能为空',
            'business_cases.*.result.required' => '案例结果不能为空'
        ]);

        $user = $request->attributes->get('user');
        
        // 获取提交的所有案例名称
        $submittedNames = collect($body['business_cases'])->pluck('name')->toArray();
        if (count($submittedNames) !== count(array_unique($submittedNames))) {
            return responseFail('存在重复案例提交');
        }
        // 删除不在提交数据中的原有案例
        BusinessCase::where('profileID', $user['profileID'])
            ->where('type', $body['type'])
            ->whereNotIn('name', $submittedNames)
            ->delete();
        
        // 更新或创建提交的案例
        foreach ($body['business_cases'] as $case) {
            $case['type'] = $body['type'];
            if( $case['type'] == BusinessCase::TYPE_CASE_AI_TOOL && empty($case['name']) ) {
                $case['name'] = "AI工具使用经验";
            }
            BusinessCase::updateOrCreate(
                ['profileID' => $user['profileID'],'type'=>$case['type'], 'name' => $case['name']],
                $case
            );
        }

        return responseSuccess();
    }

    public function delete($id,Request $request)
    {
//        $user = $request->attributes->get('user');
        BusinessCase::findOrFail($id);
        BusinessCase::destroy($id);
        return responseSuccess();
    }
}
