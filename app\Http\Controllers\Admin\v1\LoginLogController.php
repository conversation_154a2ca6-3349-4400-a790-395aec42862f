<?php
namespace App\Http\Controllers\Admin\v1;

use App\Http\Controllers\Controller;
use App\Models\LoginLog;
use Illuminate\Http\Request;

class LoginLogController extends Controller
{

    /**
     * 活动足迹
     * @return void
     */
    public function list(Request $request)
    {
        $user = $request->attributes->get('user');
        $token = $request->bearerToken();
        $pageSize = $request->post('pageSize', 10);
        $list = LoginLog::where('profile_id', $user['profileID'])->orderByDesc('id')->paginate($pageSize);

        $items = collect($list->items())->map(function ($item) use($token) {
            if ($item->token == $token) {
                $item->current = 1;
            } else {
                $item->current = 0;
            }
            unset($item['token']);
            return $item;
        });

        $paginate = [
            'page_size' => $pageSize,
            'total' => $list->total(),
            'total_page' => $list->lastPage(),
        ];

        return responseSuccess(compact('items', 'paginate'));

    }

    /**
     * 退出设备(退出单个或全部退出)
     * @param Request $request
     * @return void
     */
    public function quit(Request $request)
    {
        $id = $request->get('id'); // id 为0 全部退出
        $user = $request->attributes->get('user');
        $token = $request->bearerToken();

        if ($id > 0) { // 退出一个
            $quitLogin = LoginLog::query()->select('id', 'profile_id')->where('id', $id)->first();
            if (empty($quitLogin) || $quitLogin['profile_id'] != $user['profileID']) {
                return responseSuccess();
            }
            $quitLogin->token_status = 0;
            $quitLogin->save();

        } else { // 全部退出（除了当前）
            $onlines = LoginLog::query()->select('id', 'token')->where('token_status', 1)
                ->where('profile_id', $user['profileID'])->get();
            $quitIds = [];
            foreach ($onlines as $item) {
                if ($item['token'] != $token) {
                    $quitIds[] = $item['id'];
                }
            }
            if ($quitIds) {
                LoginLog::whereIn('id', $quitIds)->update(['token_status' => 0]);
            }

        }

        return responseSuccess();

    }
}