<?php
// 税率配置
return [
    'excluding_tax'    => 1.01,  // 不含税收入除于的数
    // 应纳税额减除费用一
    'payable_config1'  => [
        'payable_point'         => 4000, // 应纳税额计算临界点
        'payable_sub_number'    => 800,  // 应纳税额小于临界点减去数额
        'payable_mul_number'    => 0.8   // 应纳税额大于临界点剩于比例
    ],
    // 应纳税额减除费用二
    'payable_config2'  => [
        'payable_free'          => 500,     // 小于等于500，不参与减免费用
        'payable_sum_point'     => 101000,  // 月累计收入临界点
        'payable_less_number'   => 0.07,    // 小于临界点剩于比例
        'payable_greater_number'=> 0.12,    // 大于临界点剩于比例
        'payable_div_number'    => 2,       // 最后除于数
        'payable_tax_rate'      => 0.01     // 固定税率
    ],
    // 个人所得税预扣率
    'tax_brackets'    => [
        1 => [
            'min'           => 0,          // 税率区间下限
            'max'           => 20000,      // 税率区间上限
            'rate'          => 0.2,        // 税率
            'deduction'     => 0       // 速算扣除数
        ],
        2 => [
            'min'           => 20000,          // 税率区间下限
            'max'           => 50000,      // 税率区间上限
            'rate'          => 0.3,        // 税率
            'deduction'     => 2000       // 速算扣除数
        ],
        3 => [
            'min'           => 50000,      // 税率区间下限
            'max'           => 0,          // 大于50000
            'rate'          => 0.4,        // 税率
            'deduction'     => 7000       // 速算扣除数
        ]
    ]
];