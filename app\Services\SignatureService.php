<?php

namespace App\Services;

use App\Exceptions\DefaultException;
use App\Models\BankModel;
use App\Models\DivisionCn;
use App\Models\Nric;
use App\Models\ProfileContractModel;
use App\Models\ProfileInfoModel;
use Exception;
use Illuminate\Support\Str;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Storage;

class SignatureService
{    
    /**
     * Method getContractTemplate
     * 制作用户信息合同模版
     *
     * @param $data $data [用户信息]
     *
     * @return void
     */
    public function getContractTemplate($data, $type = 1, $redo = 0)
    {
        $keys = ['profileID', 'bank_id', 'profileAddressDistrictId', 'profileName',
            'profileNRIC', 'profileAddressStreet', 'profileAddressUnit', 'profileContact',
            'profileEmail', 'bank_account'];
        foreach ($keys as $k) {
            if ($data[$k] === '') {
                throw new DefaultException(__('missing param', ['param' => $k]));
            }
        }
        try {
            $userSignature = ProfileContractModel::where('profileID', $data->profileID)
                ->where('type', $type)->first(); //todo
            $nricName = Nric::where('profile_id', $data->profileID)->value('name');
            $bank = '';
            if ($data->bank_id) {
                $bank = BankModel::where('ID', $data->bank_id)->value('bankNameZH');
                $data->bank_branch && $bank .= $data->bank_branch;
            }
            if (!$userSignature || $redo) {
                if ($redo && empty($userSignature['signature'])) {
                    throw new Exception('缺少用户签名');
                }
                $address = DivisionCn::find($data->profileAddressDistrictId);
                $addressName = $address->adname;
                // 资源文件路径
                $originalContract = storage_path('pdf/contract-template.pdf');
                $originalSignature = storage_path('pdf/contract-signature.png');

                $pdf = new \Fpdi\Fpdi();
                // 获取页数
                $pageCount = $pdf->setSourceFile($originalContract);
                // 设置字体
                $pdf->AddGBFont();
                $pdf->SetFont('GB', '', 12);
                // 遍历所有页面
                for ($pageNo = 1; $pageNo <= $pageCount; $pageNo++) {
                    // 导入页面
                    $templateId = $pdf->importPage($pageNo);
                    $size = $pdf->getTemplateSize($templateId);
                    // 获取导入页面的大小
                    $pdf->AddPage($size['orientation'], [$size['width'], $size['height']]);
                    $pdf->useTemplate($templateId);
                    // 签署页面
                    if ($pageNo === 12) {        
                        // C&A中国签署
                        $pdf->Image($originalSignature, 130, 90, 40, 20);
                        // $pdf->SetTextColor(0, 0, 0); // 黑色文本
                        // $pdf->SetXY(143, 96);
                        // $pdf->Write(10, 'NG MEI CHOO');
                
                        // $pdf->SetTextColor(0, 0, 0); // 黑色文本
                        // $pdf->SetXY(130, 102);
                        // $pdf->Write(10, $this->mbConvertEncoding('经理'));
                
                        $pdf->SetTextColor(0, 0, 0); // 黑色文本
                        $pdf->SetXY(50, 164);
                        $pdf->Write(10, $this->mbConvertEncoding($nricName ?: $data->profileName));
                        
                        $pdf->SetTextColor(0, 0, 0); // 黑色文本
                        $pdf->SetXY(50, 172);
                        $pdf->Write(10, $data->profileNRIC);
                
                        $pdf->SetTextColor(0, 0, 0); // 黑色文本
                        $pdf->SetXY(142, 193);
                        $pdf->Write(10, $this->mbConvertEncoding($nricName ?: $data->profileName));
                    }

                    // 详细信息页
                    if ($pageNo === 1) {
                        // 身份证姓名
                        $pdf->SetTextColor(0, 0, 0); // 黑色文本
                        $pdf->SetXY(70, 166);
                        $pdf->Write(10, $this->mbConvertEncoding($nricName ?: $data->profileName));
                
                        // 身份证号
                        $pdf->SetTextColor(0, 0, 0); // 黑色文本
                        $pdf->SetXY(70, 175);
                        $pdf->Write(10, $this->mbConvertEncoding($data->profileNRIC));

                        // 联系地址
                        $tempAddress = str_replace(',', '', $addressName.$data->profileAddressStreet.$data->profileAddressUnit);
                        $addressArray = $this->splitString($tempAddress, 26);
                        for ($i = 0; $i < count($addressArray); $i++) {
                            $postionY = 184 + $i*5;
                            $pdf->SetTextColor(0, 0, 0); // 黑色文本
                            $pdf->SetXY(70, $postionY);
                            $pdf->Write(10, $this->mbConvertEncoding($addressArray[$i]));
                        }

                        // 联系电话
                        $pdf->SetTextColor(0, 0, 0); // 黑色文本
                        $pdf->SetXY(70, 201);
                        $pdf->Write(10, $data->profileContact);

                        // 联系电邮
                        $pdf->SetTextColor(0, 0, 0); // 黑色文本
                        $pdf->SetXY(70, 209);
                        $pdf->Write(10, $data->profileEmail);

                        // 开户银行
                        if ($bank) {
                            $pdf->SetTextColor(0, 0, 0); // 黑色文本
                            $pdf->SetXY(70, 218);
                            $pdf->Write(10, $this->mbConvertEncoding($bank));
                        }

                        // 账号名称
                        $pdf->SetTextColor(0, 0, 0); // 黑色文本
                        $pdf->SetXY(70, 227);
                        $pdf->Write(10, $this->mbConvertEncoding($nricName ?: $data->profileName));

                        // 银行账号
                        $pdf->SetTextColor(0, 0, 0); // 黑色文本
                        $pdf->SetXY(70, 235);
                        $pdf->Write(10, $data->bank_account);

                        // 签署日期
                        $pdf->SetTextColor(0, 0, 0); // 黑色文本
                        $pdf->SetXY(70, 261);
                        $pdf->Write(10, $this->mbConvertEncoding(Carbon::now()->format('Y年m月d日')));
                    }
                }
                $filename = 'signature/'.Str::random(16).'.pdf';
                $file = storage_path('app/public/'.$filename);
                $pdf->Output('F', $file);
                
                if ($redo) {
                    $oldContract = $userSignature['contract'];
                }
                ProfileContractModel::updateOrCreate([
                    'profileID' => $data->profileID,
                    'type' => $type, //todo
                ], [
                    'contract' => $filename,
                ]);
                if (!empty($oldContract)) {
                    Storage::disk('public')->delete($oldContract);
                }
                return $filename;
            } else {
                return $userSignature->contract;
            } 
        } catch (\Exception $e) {
            throw new DefaultException('获取合同失败:'.$e->getMessage());
        }
    }
    
    /**
     * Method sealSignature
     * 电子签名盖印合同
     *
     * @param $profileID $profileID [用户ID]
     * @param $contract $contract [用户合同资源]
     * @param $signature $signature [签名svg]
     *
     * @return void
     */
    public function sealSignature($profileID, $contract, $signature, $type = 1, $redo = 0)
    {
        try {
            // 附带用户信息的合同资源文件路径
            $originalContract = storage_path('app/public/'.$contract);

            // 保存svg文件
            if ($redo) {
                $signatureSvg = $signature;
                $signatureStoragePath = storage_path('app/public/'.$signatureSvg);
            } else {
                $signatureSvg = 'signature/svg/'.Str::random(16).'.svg';
                $signatureStoragePath = storage_path('app/public/'.$signatureSvg);
                file_put_contents($signatureStoragePath, $signature);
            }

            // 使用rsvg-convert命令，将svg转png
            $signaturePng = 'signature/svg/'.Str::random(16).'.png';
            $signaturePngStoragePath = storage_path('app/public/'.$signaturePng);
            exec("rsvg-convert $signatureStoragePath > $signaturePngStoragePath");

            $pdf = new \Fpdi\Fpdi();
            // 获取页数
            $pageCount = $pdf->setSourceFile($originalContract);
            // 设置字体
            $pdf->AddGBFont();
            $pdf->SetFont('GB', '', 10);
            // 遍历所有页面
            for ($pageNo = 1; $pageNo <= $pageCount; $pageNo++) {
                // 导入页面
                $templateId = $pdf->importPage($pageNo);
                $size = $pdf->getTemplateSize($templateId);
                // 获取导入页面的大小
                $pdf->AddPage($size['orientation'], [$size['width'], $size['height']]);
                $pdf->useTemplate($templateId);
                // 签署页面
                if ($pageNo === 12) {        
                    // 合伙人签署
                    $pdf->Image($signaturePngStoragePath, 130, 168, 40, 20);
                }
            }

            // 保存签名后的合同
            $filename = 'signature/'.Str::random(16).'.pdf';
            $file = storage_path('app/public/'.$filename);
            $pdf->Output('F', $file);
            
            // 更新用户合同信息
            ProfileContractModel::where('profileID', $profileID)->where('type', $type)
                ->update(['contract' => $filename, 'signature' => $signatureSvg]); //todo

            // 删除所生成的签名png格式文件
            Storage::disk('public')->delete($signaturePng);
            Storage::disk('public')->delete($contract);

            return $filename;

        } catch (\Exception $e) {
            throw new DefaultException('签名盖印失败');
        }
    }
    
    /**
     * Method mbConvertEncoding
     * 中文转编码
     *
     * @param $text $text [explicite description]
     *
     * @return void
     */
    private function mbConvertEncoding($text)
    {
        return mb_convert_encoding($text, 'GBK', 'UTF-8');
    }
    
    /**
     * Method splitString
     * 分割字符
     *
     * @param $str $str [explicite description]
     * @param $maxLength $maxLength [explicite description]
     *
     * @return void
     */
    private function splitString($str, $maxLength = 16) {
        // 初始化结果数组
        $result = [];
    
        // 循环分割字符串
        while (mb_strlen($str, 'UTF-8') > $maxLength) {
            // 截取前 $maxLength 长度的子字符串
            $part = mb_substr($str, 0, $maxLength, 'UTF-8');
            $result[] = str_replace(' ', '', $part);
            // 去掉已经分割的部分
            $str = mb_substr($str, $maxLength, null, 'UTF-8');
        }
    
        // 如果还有剩余的字符串，加入结果数组
        if (mb_strlen($str, 'UTF-8') > 0) {
            $result[] = str_ireplace(' ', '', $str);
        }
    
        return $result;
    }

    public function getContractTpl($user, $type)
    {
        if ($type == ProfileContractModel::TYPE_SERVICE) {
            return $this->getContractTemplate($user);
        } else if ($type == ProfileContractModel::TYPE_PROJECT) {
            return $this->getContractTemplate($user, ProfileContractModel::TYPE_PROJECT); //todo
        }
    }

    public function sealSign($profileID, $contract, $signature, $type)
    {
        if ($type == ProfileContractModel::TYPE_SERVICE) {
            return $this->sealSignature($profileID, $contract, $signature);
        } else if ($type == ProfileContractModel::TYPE_PROJECT) {
            return $this->sealSignature($profileID, $contract, $signature, ProfileContractModel::TYPE_PROJECT); //todo
        }
    }

    //重新生成合同
    public function contractRedo($profileID)
    {
        $user = ProfileInfoModel::where('profileID', $profileID)->first();
        $oldContract = ProfileContractModel::where('profileID', $user['profileID'])
            ->where('type', ProfileContractModel::TYPE_SERVICE)->first();
        if (empty($oldContract['signature'])) {
            return;
        }
        $contract = $this->getContractTemplate($user, ProfileContractModel::TYPE_SERVICE, 1);
        $this->sealSignature($user['profileID'], $contract, $oldContract['signature'], ProfileContractModel::TYPE_SERVICE, 1);
    }
}