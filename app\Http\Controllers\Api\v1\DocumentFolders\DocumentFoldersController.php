<?php

namespace App\Http\Controllers\Api\v1\DocumentFolders;

use App\Http\Controllers\Controller;
use App\Models\DocumentFileDataModel;
use App\Models\DocumentFoldersModel;
use App\Models\TeamRank;
use App\Services\DocService;
use Illuminate\Http\Request;
use App\Models\DocumentInfoModel;
use App\Models\ProfileContractModel;

class DocumentFoldersController extends Controller
{

    private $request;
    private $documentFoldersModel;

    public function __construct(Request $request, DocumentFoldersModel $documentFoldersModel)
    {
        $this->request = $request;
        $this->documentFoldersModel = $documentFoldersModel;
    }

    /**
     * 获取所有文件夹
     * @return void
     */
    public function index()
    {
        $user = $this->request->attributes->get('user');
        $page_size = $this->request->input('page_size', 10);
        $page = $this->request->input('page', 1);

        // 非三三制，不显示名为 "团队文件" 的文件夹
        if (!TeamRank::hasFile($user)) {
            $data = $this->documentFoldersModel->where('foldersNameZH', '<>', '团队文件')
                ->orderBy('order', 'desc')->orderBy('id', 'asc')->paginate($page_size);
        } else {
            $data = $this->documentFoldersModel->orderBy('order', 'desc')->orderBy('id', 'asc')->paginate($page_size);
        }
        $item = $data->items();

        $currentPage = $data->currentPage();
        $perPage = $data->perPage();
        $totalRecord = $data->total();
        $totalPage = $data->lastPage();
        $paginate = [
            'currentPage' => $currentPage,
            'perPage' => $perPage,
            'totalRecord' => $totalRecord,
            'totalPage' => $totalPage
        ];

        $compact = compact('item', 'paginate');

        return responseSuccess($compact);
    }

    /**
     * 获取文件夹下文件
     * @param Request $request
     * @return void
     */
    public function files(Request $request, $id)
    {
        $pageSize = $request->get('page_size', 10);
        $page = $this->request->input('page', 1);
        $user = $this->request->attributes->get('user');

        if (empty($id)) {
            return responseFail(__('param error', ['param' => 'id'])); // 参数缺失:id
        }

        // 获取详情
        $info = DocumentFoldersModel::find($id);
        if (empty($info)) {
            return responseFail(__('info no exist')); // 信息不存在
        }

        $paginate = [];

        if ($info['foldersNameZH'] == '团队文件') { // 三三制特别处理
            $items = [];
            if (TeamRank::hasFile($user)) {
                $rankFile = TeamRank::find($user['team_rank']);
                $items[] = [
                    'documentID'     => 0,
                    'documentTitle'  => $rankFile['job'] . __('Manual'),
                    'documentVersion' => $rankFile['file_version']
                ];

                $paginate = [
                    'page_size' => 100,
                    'total' => count($items),
                    'total_page' => 1,
                ];
            }
        } else {
            // 文件夹下文件列表
            $list = DocumentInfoModel::query()
                ->where('folderId', $id)
                ->orderBy('order', 'desc')->orderBy('documentID', 'asc')->paginate($pageSize);

            $items = $list->items();

            $paginate = [
                'page_size' => $pageSize,
                'total' => $list->total(),
                'total_page' => $list->lastPage(),
            ];
        }

        return responseSuccess(compact('items', 'paginate'));
    }
}
