<?php

namespace App\Http\Controllers\Api\v1\Register;

use App\Http\Controllers\Controller;
use App\Http\Requests\Register\CommitRegSetLangRequest;
use App\Http\Requests\Register\CommitRegSetProfileRequest;
use App\Http\Requests\Register\CommitRegSetSecRequest;
use App\Http\Requests\Register\CommitVerifyEmail;
use App\Http\Requests\Register\CommitVerifySms;
use Illuminate\Http\Request;
use App\Http\Requests\Register\Register;
use App\Http\Requests\Register\RegisterCheck;
use App\Http\Requests\Register\RegisterConfirm;
use App\Http\Requests\Register\VerifyEmail;
use App\Http\Requests\Register\VerifyPhone;
use App\Models\CountryModel;
use App\Models\Firm;
use App\Models\FirmRelation;
use App\Models\ProfileInfoModel;
use App\Models\Setting;
use App\Models\TeamInvite;
use App\Models\TeamRank;
use Firebase\JWT\JWT;
use App\Services\EmailService;
use App\Services\SmsService;

class RegisterController extends Controller
{
    private $request;
    private $profileInfoModel;
    private $emailService;
    private $smsService;

    public function __construct(Request $request, ProfileInfoModel $profileInfoModel, EmailService $emailService, SmsService $smsService)
    {
        $this->request = $request;
        $this->profileInfoModel = $profileInfoModel;
        $this->emailService = $emailService;
        $this->smsService = $smsService;
    }

    /**
     * Method 用户注册第一步
     *
     * @param Register $request [explicite description]
     * @param ProfileInfoModel $profileInfoModel [explicite description]
     *
     * @return void
     */
    public function register(Register $request)
    {
        $body = $request->all();
        $email = $body['email'];
        $nationalityID = intval($body['nationalityID']);
        $prefixID = intval($body['prefixID']);
        $phone = $body['phone'];
        //检查用户注册记录
        $registerRec = $this->profileInfoModel->registerRec($email, $phone);
        if (!$registerRec) {
            return responseSuccess(compact('email', 'prefixID', 'phone', 'nationalityID'));
        }
        if ($registerRec['status'] == ProfileInfoModel::STATUS_PAYING) {
            $payload = [
                'exp' => time() + 3600,
                'action' => ACTION_PROFILE_REGISTER,
                'user_id' => $registerRec['profileID'],
            ];
            $token = JWT::encode($payload, env('JWT_KEY'), 'HS256');
            return responseSuccess(['token' => $token]);
        }
        return responseFail(__('register already active'));
    }
    
    /**
     * Method 用户注册第二步
     *
     * @param RegisterConfirm $request [explicite description]
     *
     * @return void
     */
    public function registerConfirm(RegisterConfirm $request)
    {
        $body = $request->all();
        $name = $body['name'];
        $englishName = $body['englishName'];
        $gender = $body['gender'];
        $birthDate = $body['birthDate'];
        $nationalityID = $body['nationalityID'];
        $prefixID = $body['prefixID'];
        $phone = $body['phone'];
        $email = $body['email'];
        $addressUnit = $body['addressUnit'];
        $addressStreet = $body['addressStreet'];
        $addressDistrictId = $body['addressDistrictId'];
        $addressCityId = $body['addressCityId'];
        $addressStateId = $body['addressStateId'];
        $addressCountry = $body['addressCountry'];
        $professionId = $body['profession_id'];
        $recommendProfileID = $body['recommend'] ?? 0;
        $isTeam = $body['is_team'] ?? 0;
        $isFirm = $body['is_firm'] ?? 0;
        $insert = [
            'profileName' => $name,
            'profileEnglishName' => $englishName,
            'profileGender' => $gender,
            'profileNationalityID' => $nationalityID,
            'profileBirthDate' => $birthDate,
            'profileEmail' => $email,
            'mobilePrefixID' => $prefixID,
            'profileContact' => $phone,
            'profileAddressUnit' => $addressUnit,
            'profileAddressStreet' => $addressStreet,
            'profileAddressDistrictId' => $addressDistrictId,
            'profileAddressCityId' => $addressCityId,
            'profileAddressStateId' => $addressStateId,
            'profileAddressCountry' => $addressCountry,
            'profession_id' => $professionId,
            'status' => ProfileInfoModel::STATUS_PAYING,
            'position' => $body['position'],
            'work_year' => $body['work_year'],
        ];
        $teamTop = 0;
        //推荐人
        if ($recommendProfileID) {
            //被邀请成为最高级的营运总裁（三三制）
            if (str_starts_with($recommendProfileID, TeamInvite::CODE_PREFIX)) {
                $code = substr($recommendProfileID, strlen(TeamInvite::CODE_PREFIX));
                $record = TeamInvite::where('invite_code', $code)->where('status', TeamInvite::STATUS_NOT_USED)
                    ->first();
                if ($record) {
                    TeamInvite::where('invite_code', $code)->update(['status' => TeamInvite::STATUS_USED]);
                    $insert['team_rank'] = TeamRank::getTopRank();
                    $teamTop = 1;
                }
            } else if ($isFirm) {
                $firmCode = Firm::where('profile_id', $recommendProfileID)->value('code');
                if (empty($firmCode)) {
                    $isFirm = 0;
                    $insert['pre_id'] = $recommendProfileID;
                }
            } else if ($isTeam) {
                $insert['pre_id'] = $recommendProfileID;
                $user = ProfileInfoModel::checkRecommendTeam($recommendProfileID);
                if ($user) {
                    $insert['team_group'] = $user['team_group'];
                    $insert['team_rank'] = $user['team_rank'] - 1;
                }
            } else {
                $insert['pre_id'] = $recommendProfileID;
            }
        } else {
            $insert['pre_id'] = ProfileInfoModel::ADMIN_PROFILE_ID;
        }
        //新增用户信息
        $result = $this->profileInfoModel::create($insert);

        //生成用于发起微信支付的密钥
        if ($result) {
            if ($teamTop) {
                $result->team_group = $result->profileID;
                $result->save();
            } else if ($recommendProfileID && $isFirm) {
                FirmRelation::create([
                    'profile_id' => $result->profileID,
                    'pre_id' => $recommendProfileID,
                ]);
            }
            $payload = [
                'exp' => time() + 3600,
                'action' => ACTION_PROFILE_REGISTER,
                'user_id' => $result->profileID
            ];
            $token = JWT::encode($payload, env('JWT_KEY'), 'HS256');
            
            return responseSuccess(compact('token'));
        } else {
            return responseFail();
        }
    }
    
    /**
     * Method 发送邮箱
     *
     * @param VerifyEmail $request [explicite description]
     *
     * @return void
     */
    public function sendEmail(VerifyEmail $request)
    {
        $body = $request->all();
        $nationalityID = $request->input('nationalityID', '');
        $language = getLanguageByNationalityID($nationalityID);

        return $this->emailService->sendVerificationCodeEmail($body['email'], config('email.verification_code_scene.register_verify'), $language);
    }
    
    /**
     * Method 验证邮箱
     *
     * @param CommitVerifyEmail $request [explicite description]
     *
     * @return void
     */
    public function verifyEmail(CommitVerifyEmail $request)
    {
        $body = $request->all();
        $this->emailService->verifyEmail($body['email'], $body['code'], config('email.verification_code_scene.register_verify'));
        return responseSuccess();
    }
    
    /**
     * Method 发送短信
     *
     * @param VerifyPhone $request [explicite description]
     *
     * @return void
     */
    public function sendSms(VerifyPhone $request)
    {
        $body = $request->all();
        $phone = $body['phone'];
        $prefixID = $body['prefixID'];
        $mobilePrefixModel = new CountryModel;

        //整合区号+手机号
        $resetPhoneData = $mobilePrefixModel->resetPhone($prefixID, $phone);
        $resetPhone = $resetPhoneData['phone'];
        $resetCountryCode = $resetPhoneData['countryCode'];

        return $this->smsService->sendVerificationCodeSms($resetPhone,
            config('sms.verification_code_scene.register_verify'), $resetCountryCode);
    }
    
    /**
     * Method 验证短信
     *
     * @param CommitVerifySms $request [explicite description]
     *
     * @return void
     */
    public function verifySms(CommitVerifySms $request)
    {
        $body = $request->all();
        $this->smsService->verifySms($body['phone'], $body['code'],
            config('sms.verification_code_scene.register_verify'));
        return responseSuccess();
    }
    
    /**
     * Method 设置语言
     *
     * @param CommitRegSetLangRequest $request [explicite description]
     *
     * @return void
     */
    public function setLanguage(CommitRegSetLangRequest $request)
    {
        $user = $request->attributes->get('user');
        $languageCode = $request->input('languageCode');
        Setting::create(['profile_id' => $user['profileID'], 'language' => $languageCode]);

        return responseSuccess();
    }
    
    /**
     * Method 安全设置
     *
     * @param CommitRegSetSecRequest $request [explicite description]
     *
     * @return void
     */
    public function setSecurity(CommitRegSetSecRequest $request)
    {
        $user = $request->attributes->get('user');
        //不是初始设置修改密码
        if (env('APP_ENV') == 'product') {
            if (!str_starts_with($user['profilePassword'], ProfileInfoModel::INITIAL_PASSWORD_PREFIX)) {
                return responseFail();
            }
        }
        $body = $request->all();
        //$currentPassword = $body['currentPassword'];
        $newPassword = $body['newPassword'];
        $passwordConfirmation = $body['passwordConfirmation'];
        //$currentHashPassword = hashPassword($currentPassword);
        //确认新密码
        if ($newPassword !== $passwordConfirmation) {
            return responseFail(__('confirm new password'));
        }
        //检验旧密码
        /* if (!ProfileInfoModel::checkPassword($currentHashPassword, $user['profilePassword'])) {
            return responseFail(__('old password error'));
        } */
        $newHashPassword = hashPassword($newPassword);
        ProfileInfoModel::where('profileID', $user['profileID'])->update(['profilePassword' => $newHashPassword]);

        return responseSuccess();
    }
    
    /**
     * Method 用户设置
     *
     * @param CommitRegSetProfileRequest $request [explicite description]
     *
     * @return void
     */
    public function setProfile(CommitRegSetProfileRequest $request)
    {
        $file = $request->file('file');
        $user = $request->attributes->get('user');
        //上传照片
        // $resource = $file->store('images/avatar', 'public');
        $resource = $file->storeAs('images/avatar', generateUploadFilename($file->getClientOriginalExtension()), 'public');
        //更新用户信息
        ProfileInfoModel::where('profileID', $user['profileID'])->update(['profileAvatar' => $resource]);

        return responseSuccess();
    }

    //检查手机号邮箱是否存在
    public function registerCheck(RegisterCheck $request)
    {
        $body = $request->all();
        if (!empty($body['email'])) {
            $registerRec = ProfileInfoModel::where('profileEmail', $body['email'])->first();
            if (!$registerRec) {
                return responseSuccess();
            }
        } else if (!empty($body['phone'])) {
            $registerRec = ProfileInfoModel::where('profileContact', $body['phone'])->first();
            if (!$registerRec) {
                return responseSuccess();
            }
        }
        if ($registerRec['status'] == ProfileInfoModel::STATUS_PAYING) {
            $payload = [
                'exp' => time() + 3600,
                'action' => ACTION_PROFILE_REGISTER,
                'user_id' => $registerRec['profileID'],
            ];
            $token = JWT::encode($payload, env('JWT_KEY'), 'HS256');
            return responseSuccess(['token' => $token]);
        }
        return responseFail(__('register already active'));
    }

}
