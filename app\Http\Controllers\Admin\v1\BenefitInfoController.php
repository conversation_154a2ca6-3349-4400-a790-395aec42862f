<?php

namespace App\Http\Controllers\Admin\v1;

use App\Http\Controllers\Controller;
use App\Models\ActiveLog;
use App\Models\BenefitDataModel;
use App\Models\BenefitInfoModel;
use Illuminate\Support\Facades\Validator;
// use Symfony\Component\HttpFoundation\Request;
use Illuminate\Http\Request;

class BenefitInfoController extends Controller
{
    /**
     * 福利列表
     * @return void
     */
    public function index(Request $request)
    {
        $pageSize = $request->get('page_size', 10);
        $keyword = $request->get('keyword'); // 关键字

        $list = BenefitInfoModel::with(['createUserInfo', 'editUserInfo'])
            ->when($keyword != '', function ($query) use ($keyword) {
                $query->orWhere('benefitTitleEN', 'like', '%' . $keyword . '%')
                    ->orWhere('benefitTitleMS', 'like', '%' . $keyword . '%')
                    ->orWhere('benefitTitleZH', 'like', '%' . $keyword . '%')
                    ->orWhere('benefitTitleZT', 'like', '%' . $keyword . '%');
            })->orderBy('createTime', 'desc')->paginate($pageSize);

        $items = $list->items();

        $paginate = [
            'page_size' => $pageSize,
            'total' => $list->total(),
            'total_page' => $list->lastPage(),
        ];

        return responseSuccess(compact('items', 'paginate'));
    }

    /**
     * 创建福利
     * @param Request $request
     * @return void|null
     */
    public function store(Request $request)
    {


        $validator = Validator::make($request->all(), [
            'benefitTitleEN' => 'required',
            'benefitTitleMS' => 'required',
            'benefitTitleZH' => 'required',
            'benefitTitleZT' => 'required',
            'benefitDescriptionEN' => 'required',
            'benefitDescriptionMS' => 'required',
            'benefitDescriptionZH' => 'required',
            'benefitDescriptionZT' => 'required',
        ]);

        // 验证失败
        if ($validator->fails()) {
            return responseFail($validator->errors()->all());
        }

        $param = $request->all();
        $user = $request->attributes->get('user');
        $user_id = $user['profileID'];

        $result = BenefitInfoModel::query()->insertGetId([
            'benefitTitleEN' => $param['benefitTitleEN'],
            'benefitTitleMS' => $param['benefitTitleMS'],
            'benefitTitleZH' => $param['benefitTitleZH'],
            'benefitTitleZT' => $param['benefitTitleZT'],
            'benefitDescriptionEN' => $param['benefitDescriptionEN'],
            'benefitDescriptionMS' => $param['benefitDescriptionMS'],
            'benefitDescriptionZH' => $param['benefitDescriptionZH'],
            'benefitDescriptionZT' => $param['benefitDescriptionZT'],
            'createTime'           => date('Y-m-d H:i:s'),
            'createUser'           => $user_id,
            'createRole'           => $user['profileRole'],
        ]);


        if ($result) {

            // 记录日志
            ActiveLog::log(
                $user['profileID'],
                ActiveLog::ADMIN_API_V1_CREATE,
                ActiveLog::ADMIN_API_V1_WELFARE_SET,
                $result,
                $param['benefitTitleZH'],
                ActiveLog::SYSTEM_CNA_ADMIN
            );

            return responseSuccess();
        } else {
            return responseFail(__('save failed'));
        }
    }

    /**
     * 详情
     * @param Request $request
     * @param $id
     * @return void
     */
    public function edit(Request $request, $id)
    {
        if (empty($id)) {
            return responseFail(__('param error', ['param' => 'id'])); // 参数缺失:id
        }

        // 获取详情
        $info = BenefitInfoModel::find($id);
        if (empty($info)) {
            return responseFail(__('info no exist')); // 信息不存在
        }


        return responseSuccess($info);
    }

    /**
     * 编辑
     * @param Request $request
     * @param $id
     * @return void
     */
    public function update(Request $request, $id)
    {

        $validator = Validator::make($request->all(), [
            'benefitTitleEN' => 'required',
            'benefitTitleMS' => 'required',
            'benefitTitleZH' => 'required',
            'benefitTitleZT' => 'required',
            'benefitDescriptionEN' => 'required',
            'benefitDescriptionMS' => 'required',
            'benefitDescriptionZH' => 'required',
            'benefitDescriptionZT' => 'required',
        ]);

        // 验证失败
        if ($validator->fails()) {
            return responseFail($validator->errors()->all());
        }

        if (empty($id)) {
            return responseFail(__('param error', ['param' => 'id'])); // 参数缺失:id
        }

        // 获取详情
        $info = BenefitInfoModel::find($id);
        if (empty($info)) {
            return responseFail(__('info no exist')); // 信息不存在
        }

        $param = $request->all();
        $user = $request->attributes->get('user');

        // 更新
        $info->benefitTitleEN = $param['benefitTitleEN'];
        $info->benefitTitleMS = $param['benefitTitleMS'];
        $info->benefitTitleZH = $param['benefitTitleZH'];
        $info->benefitTitleZT = $param['benefitTitleZT'];
        $info->benefitDescriptionEN = $param['benefitDescriptionEN'];
        $info->benefitDescriptionMS = $param['benefitDescriptionMS'];
        $info->benefitDescriptionZH = $param['benefitDescriptionZH'];
        $info->benefitDescriptionZT = $param['benefitDescriptionZT'];
        $info->editUser = $user['profileID'];
        $info->editRole = $user['profileRole'];
        $info->editTime = date('Y-m-d H:i:s');
        $result = $info->save();

        if ($result !== false) {

            // 记录日志
            ActiveLog::log(
                $user['profileID'],
                ActiveLog::ADMIN_API_V1_EDIT,
                ActiveLog::ADMIN_API_V1_WELFARE_SET,
                $id,
                $param['benefitTitleZH'],
                ActiveLog::SYSTEM_CNA_ADMIN
            );

            return responseSuccess();
        } else {
            return responseFail(__('edit failed'));
        }
    }

    /**
     * 删除
     * @param Request $request
     * @param $id
     * @return void
     */
    public function destroy(Request $request, $id)
    {
        if (empty($id)) {
            return responseFail(__('param error', ['param' => 'id'])); // 参数缺失:id
        }

        // 获取详情
        $info = BenefitInfoModel::find($id);
        if (empty($info)) {
            return responseFail(__('info no exist')); // 信息不存在
        }

        // 查询是否有人申请
        $isExist = BenefitDataModel::where('benefitID', $id)->first();
        if ($isExist) {
            return responseFail(__('benefit delete error')); // 有人申请暂不能删除
        }

        $user = $request->attributes->get('user');
        $benefitTitleZH = $info['benefitTitleZH'];

        // 删除
        $result =  $info->delete();
        if ($result !== false) {

            // 记录日志
            ActiveLog::log(
                $user['profileID'],
                ActiveLog::ADMIN_API_V1_DELETE,
                ActiveLog::ADMIN_API_V1_WELFARE_SET,
                $id,
                $benefitTitleZH,
                ActiveLog::SYSTEM_CNA_ADMIN
            );

            return responseSuccess();
        } else {
            return responseFail(__('delete failed'));
        }
    }
}
