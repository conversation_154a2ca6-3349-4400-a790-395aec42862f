<?php

namespace App\Http\Controllers\Admin\v1;

use App\Http\Controllers\Controller;
use App\Models\ActiveLog;
use App\Models\ProfileInfoModel;
use Illuminate\Support\Facades\Validator;
use Symfony\Component\HttpFoundation\Request;

class SettingController extends Controller
{
    /**
     * 修改密码
     * @return void
     */
    public function password(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'oldPassword'   => 'required',
            'newPassword' => 'required',
        ]);

        // 验证失败
        if ($validator->fails()) {
            return responseFail($validator->errors()->all());
        }

        $user = $request->attributes->get('user');
        $oldPassword = $request->oldPassword;
        $newPassword = $request->newPassword;

        $data = ProfileInfoModel::where('profileID', $user['profileID'])
            ->where('profilePassword', hashPassword($oldPassword))
            ->first();

        if (!$data) {
            return responseFail(__('param error', ['param'=>'old password']));
        }

        $data->profilePassword = hashPassword($newPassword);
        $data->save();

        // 记录日志
        ActiveLog::log($user['profileID'], ActiveLog::ADMIN_API_V1_EDIT, ActiveLog::ADMIN_API_V1_PROFILE_PASSWORD,
            $user['profileID'], $user['profileName'], ActiveLog::SYSTEM_CNA_ADMIN);

        return responseSuccess();
    }

    /**
     * 获取设置信息
     * @return void
     */
    public function index(Request $request)
    {
        $user = $request->attributes->get('user');
        $data = ProfileInfoModel::query()->select([
            'profileID',
            'settingLanguage',
            'settingTimezone',
            'settingDateFormat',
            'settingTimeFormat',
            'settingCurrency',
            'settingNotifyType',
            'settingNotifyEmergency',
            'settingNotifySuspiciousOperation',
            'settingNotifySafeUpdated',
            'settingNotifyRecPrivateMsg',
            'settingNotifyImportanceUpdate',
            'settingNotifySystemUpdate',
            'settingNotifyJoinInvestigate'
        ])->find($user['profileID']);
        if ($data) {
            return responseSuccess($data);
        } else {
            return responseFail(__('info no exist'));
        }
    }

    /**
     * 设置语言和地区
     * @return void
     */
    public function setLangArea(Request $request)
    {
        $user = $request->attributes->get('user');
        $settingLanguage = $request->settingLanguage;
        $settingTimezone = $request->settingTimezone;
        $settingDateFormat = $request->settingDateFormat;
        $settingTimeFormat = $request->settingTimeFormat;
        $settingCurrency = $request->settingCurrency;
        $settings = compact('settingLanguage', 'settingTimezone', 'settingDateFormat', 'settingTimeFormat', 'settingCurrency');

        $res = ProfileInfoModel::where('profileID', $user['profileID'])->update($settings);
        if ($res) {

            // 记录日志
            ActiveLog::log($user['profileID'], ActiveLog::ADMIN_API_V1_SET, ActiveLog::ADMIN_API_V1_PROFILE_LANG_REGION,
                $user['profileID'], $user['profileName'], ActiveLog::SYSTEM_CNA_ADMIN);

            return responseSuccess($settings);
        } else {
            return responseFail(__('save failed'));
        }
    }

    /**
     * 设置信息与通知
     * @return void
     */
    public function setNotify(Request $request)
    {
        $user = $request->attributes->get('user');

        $settingNotifyType = $request->notifyType;
        $settingNotifyEmergency = $request->notifyEmergency;
        $settingNotifySuspiciousOperation = $request->notifySuspiciousOperation;
        $settingNotifySafeUpdated = $request->notifySafeUpdated;
        $settingNotifyRecPrivateMsg = $request->notifyRecPrivateMsg;
        $settingNotifyImportanceUpdate = $request->notifyImportanceUpdate;
        $settingNotifySystemUpdate = $request->notifySystemUpdate;
        $settingNotifyJoinInvestigate = $request->notifyJoinInvestigate;

        $settings = compact('settingNotifyType', 'settingNotifyEmergency',
            'settingNotifySuspiciousOperation', 'settingNotifySafeUpdated', 'settingNotifyRecPrivateMsg',
            'settingNotifyImportanceUpdate', 'settingNotifySystemUpdate', 'settingNotifyJoinInvestigate');

        $res = ProfileInfoModel::where('profileID', $user['profileID'])->update($settings);
        if ($res) {
            // 记录日志
            ActiveLog::log($user['profileID'], ActiveLog::ADMIN_API_V1_SET, ActiveLog::ADMIN_API_V1_PROFILE_NOTIFY,
                $user['profileID'], $user['profileName'], ActiveLog::SYSTEM_CNA_ADMIN);

            return responseSuccess($settings);
        } else {
            return responseFail(__('save failed'));
        }
    }

    /**
     * 通知方式
     * @return null
     */
    public function modes()
    {
        $notifyType = [
            [
                "ID" => 1,
                "type" => "email",
            ]
        ];

        return responseSuccess($notifyType);
    }
}
