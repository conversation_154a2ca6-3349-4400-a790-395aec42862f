<?php

namespace App\Http\Controllers\Api\v1\Project;

use App\Exceptions\DefaultException;
use App\Http\Controllers\Controller;
use App\Models\ProjectFileModel;
use App\Services\DocService;
use Firebase\JWT\Key;
use Illuminate\Http\Request;
use App\Models\ProjectCategoryModel;
use App\Models\ProjectServiceModel;
use App\Models\CompanyModel;
use App\Models\ProjectProgressOneModel;
use App\Models\ProjectProgressOnePdfModel;
use App\Services\PdfService;
use Firebase\JWT\JWT;
use Illuminate\Support\Facades\Storage;

class ProjectController extends Controller
{
    private $request;
    private $companyModel;

    const ACTION = 'project_doc'; //场景

    public function __construct(Request $request, CompanyModel $companyModel)
    {
        $this->request = $request;
        $this->companyModel = $companyModel;
    }

    public function projectList()
    {
        
        $data = ProjectCategoryModel::where('type', 2)->get();
        return responseSuccess($data);
    }

    public function serviceList(Request $request)
    {
        $projectId = $request->input('projectId');
        $data = ProjectServiceModel::select('id', 'title_zh as titleZH', 'title_zt as titleZT', 'title_en as titleEN',
            'title_ms as titleMS', 'currency', 'price', 'tip_zh as tipZH', 'tip_zt as tipZT', 'tip_en as tipEN',
            'tip_ms as tipMS')->when($projectId != '', function ($query) use ($projectId) {
                $query->where('projectId', $projectId);
        })->get();
        return responseSuccess($data);
    }

    public function getProgressOneInfo(Request $request)
    {
        $user = $request->attributes->get('user');
        $companyID = $request->input('companyID', 0);

        if (!$companyID) {
            return responseFail();
        }

        $company = CompanyModel::where('companyID', $companyID)->where('companyProfileID', $user['profileID'])->first();
        if (!$company) {
            return responseFail();
        }

        $progressOne = ProjectProgressOneModel::where('companyID', $companyID)->where('profileID', $user['profileID'])->first();
        $companyServices = [];
        $reason = '';
        if ($progressOne) {
            $companyServices = array_map('intval', explode(',', $progressOne->companyServices));
            $reason = $progressOne->reason;
        }

        return responseSuccess(compact('companyServices', 'reason'));
    }

    public function generateKey(Request $request)
    {
        $user = $request->attributes->get('user');
        $companyID = $request->input('companyID', 0);

        if (!$companyID) {
            return responseFail();
        }

        $company = CompanyModel::where('companyID', $companyID)->where('companyProfileID', $user['profileID'])->first();
        if (!$company) {
            return responseFail();
        }

        $userPdf = ProjectProgressOnePdfModel::where('companyID', $companyID)->where('profileID', $user['profileID'])->first();
        if (!$userPdf) {
            $pdfService = new PdfService;
            $resource = $pdfService->createForm($company->companyName, $company->companyRegisterCode, $company->companyCategoriesID);
            ProjectProgressOnePdfModel::create([
                'companyID' => $companyID,
                'profileID' => $user['profileID'],
                'file' => $resource
            ]);
        } else {
            $resource = $userPdf->file;
        }

        $payload = [
            'exp' => time() + 60,
            'action' => 'progress_one_pdf',
            'file' => $resource
        ];

        $token = JWT::encode($payload, env('JWT_KEY'), 'HS256');

        return responseSuccess($token);
    }

    public function downloadPdf()
    {
        
    }

    /**
     * 项目相关文件
     * @return void
     */
    public function fileList(Request $request)
    {
         $projectId = $request->input('projectId', 0);
         if (empty($projectId)) {
                return responseFail(__('param error', ['param' => 'projectId'])); // 参数缺失:id
         }

         // 项目对应文件
         $data = ProjectFileModel::query()->where('projectId', $projectId)->get();
         if ($data) {
             foreach ($data as $key => $val) {
                 $val['filePath'] = $val->filePath ? storageUrl($val->filePath) : null;
                 $data[$key] = $val;
             }
         }

         return responseSuccess($data);
    }

    /**
     * 获取文件验签
     * @param Request $request
     * @return null
     */
    public function generateFileToken(Request $request)
    {
        $fileId = $request->get('id');

        if (!$fileId) {
            return responseFail(__('param error', ['param' => 'id']));
        }

        $fileData = ProjectFileModel::find($fileId);
        if (!$fileData) {
            return responseFail(__('info no exist'));
        }

        $token = DocService::generateToken($fileData['filePath'], self::ACTION, 3600);

        return responseSuccess($token);
    }

    public function previewFile(Request $request)
    {
        $token = $request->route('token');
        return DocService::preview($token, self::ACTION);
    }



}
