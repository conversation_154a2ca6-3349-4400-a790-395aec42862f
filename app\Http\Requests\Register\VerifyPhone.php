<?php

namespace App\Http\Requests\Register;

use App\Rules\Register\CheckVerifyPhoneRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use App\Exceptions\DefaultException;
use App\Rules\Mobile\CheckMobilePrefixRule;

class VerifyPhone extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'prefixID' => ['required', new CheckMobilePrefixRule],
            'phone'    => ['required', new CheckVerifyPhoneRule]
        ];
    }

    public function messages()
    {
        return [
            'prefixID.required' => '请选择区号',
            'phone.required'    => '请填写手机号'
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        $error= $validator->errors()->all();
        throw new DefaultException($error[0]);
    }


}
