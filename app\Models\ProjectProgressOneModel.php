<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProjectProgressOneModel extends Model
{
    use HasFactory;

    protected $table = 'cna_project_progress_one';
    protected $guarded = [];

    public function progressOne()
    {
        return $this->belongsTo(ProjectProgressOneModel::class, 'companyID', 'companyID');
    }

    public function getProgressOneInfo($companyID, $userID)
    {
        return self::where('companyID', $companyID)->where('profileID', $userID)->first();
    }

}
