<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CountryModel extends Model
{
    use HasFactory;

    protected $table = 'cna_web_country';
    protected $guarded = [];
    const COUNTRY_ID_CHINA = 44; //中国
    
    /**
     * Method 获取结果集
     *
     * @return array
     */
    public function getData()
    {
        return self::select('countryID', 'countryZH')->get();
    }

    
    /**
     * Method 获取单条记录
     *
     * @param $id $id [主键]
     *
     * @return array
     */
    public function getRecord($id)
    {
        return self::select('countryID', 'countryZH', 'countryCode')
                    ->where('countryID', $id)
                    ->first();
    }

    /**
     * Method 整合区号+手机号
     *
     * @param $prefixId $prefixId [explicite description]
     * @param $phone $phone [explicite description]
     *
     * @return array
     */
    public function resetPhone($prefixId, $phone)
    {
        $data = $this->getRecord($prefixId);
        $countryCode = $data->countryCode;

        return compact('phone', 'countryCode');
    }
}
