<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProjectProgressFourFileModel extends Model
{
    use HasFactory;

    protected $table = 'cna_project_progress_four_files';
    protected $guarded = [];
    
    /**
     * Method 重组数据结构
     *
     * @return void
     */
    public function groupStructure()
    {
        $data =  self::select('id AS fileID', 'fileName', 'isRequired')->get();
        $res = [];
        foreach ($data as $v) {
            if (!isset($res[$v->fileID])) {
                $res[$v->fileID] = [];
            }
            $res[$v->fileID] = [
                'fileID' => $v->fileID,
                'fileName' => $v->fileName,
                'isRequired' => $v->isRequired
            ];
        }
        return $res;
    }
    
    /**
     * Method 获取必选上传的文件列表
     *
     * @return void
     */
    public function getRequiredUploadFiles()
    {
        return self::where('isRequired', 1)->pluck('id')->toArray();
    }
    
    
}
