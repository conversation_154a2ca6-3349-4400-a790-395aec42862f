<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ProfileBusinessProcessModel extends Model
{
    protected $table = 'cna_profile_business_process';
    protected $appends = ['status_remark'];

    protected $fillable = [
        'profileID',
        'project_id',
        'project_code',
        'project_remark',
        'status',
        'pay_id'
    ];
    const STATUS_DOING = 0;
    const STATUS_FINISHED = 1;
    const STATUS_CANCEL = 2;

    const TYPE_REMARK = [
        self::STATUS_DOING => '进行中',
        self::STATUS_FINISHED => '已完成',
        self::STATUS_CANCEL => '已作废',
    ];

    public function getStatusRemarkAttribute()
    {
        return self::TYPE_REMARK[$this->status] ?? '';
    }

    public function projectInfo(){
        return $this->hasOne(ProjectServiceDataModel::class, 'id', 'project_id')
            ->select([
                'id',
                'title_zh as project_name',
                'price as pay_price',
                'currency as pay_currency',
                'currency_id',
            ])->with(['currencyInfo:id,currency,currency_code']);
    }
}
