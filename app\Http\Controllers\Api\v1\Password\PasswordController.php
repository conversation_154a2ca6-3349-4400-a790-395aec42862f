<?php

namespace App\Http\Controllers\Api\v1\Password;

use App\Http\Controllers\Controller;
use App\Http\Requests\User\ResetPassword;
use App\Models\ActiveLog;
use App\Models\ProfileInfoModel;
use App\Services\EmailService;
use App\Http\Requests\User\ResetPasswordEmail;
use App\Http\Requests\User\UpdatePassword;
use App\Models\Setting;

class PasswordController extends Controller
{
    private $profileInfoModel;
    private $emailService;

    public function __construct(
        EmailService $emailService,
        ProfileInfoModel $profileInfoModel
    )
    {
        $this->emailService = $emailService;
        $this->profileInfoModel = $profileInfoModel;
    }

    public function sendForgotPasswordEmail(ResetPasswordEmail $request)
    {
        $email = $request->input('email');

       return $this->emailService->sendVerificationCodeEmail($email, config('email.verification_code_scene.forgot_password'));
    }
    
    /**
     * Method 重置密码
     *
     * @param ResetPassword $request [explicite description]
     *
     * @return void
     */
    public function resetPassword(ResetPassword $request)
    {
        $body = $request->all();
        $code = $body['code'];
        $password = $body['password'];
        $email = $body['email'];

        $this->emailService->verifyEmail($email, $code, config('email.verification_code_scene.forgot_password'));

        $profile = $this->profileInfoModel::where('profileEmail', $email)->first();
        $profile->profilePassword = hashPassword($password);
        $profile->save();
        $update = ['profilePassword' => $profile->profilePassword];
        $this->notify($profile);
        //记录活动日志
        ActiveLog::log($profile->profileID, ActiveLog::ACTIVE_UPDATE, ActiveLog::API_V1_PASSWORD_FORGOTPSW_RESET,
            $profile->profileID, $update);

        return responseSuccess();
    }
    
    /**
     * Method 修改密码
     *
     * @param UpdatePassword $request [explicite description]
     *
     * @return void
     */
    public function updatePassword(UpdatePassword $request)
    {
        $body = $request->all();
        $user = $request->attributes->get('user');
        $oldPassword = $body['oldPassword'];
        $newPassword = $body['newPassword'];

        $data = $this->profileInfoModel::where('profileID', $user['profileID'])->where('profilePassword', hashPassword($oldPassword))->first();
        
        if (!$data) {
            return responseFail(__('old password error'));
        }

        $data->profilePassword = hashPassword($newPassword);
        $data->save();
        $update = ['profilePassword' => $data->profilePassword];
        $this->notify($user);
        //记录活动日志
        ActiveLog::log($user['profileID'], ActiveLog::ACTIVE_UPDATE, ActiveLog::API_V1_PASSWORD_UPDATE,
            $user['profileID'], $update);

        return responseSuccess();
    }

    private function notify($userProfile)
    {
        $setting = Setting::where('profile_id', $userProfile['profileID'])->first();
        if (empty($setting) || !$setting['notify_safe_updated']) {
            return;
        }
        if ($setting['notify_type'] == Setting::NOTIFY_TYPE_EMAIL) {
            $language = getLanguageByNationalityID($userProfile['profileNationalityID']);
            $datetime = date('Y-m-d H:i:s');
            $config = config('email');
            $subject = $config['email_subject']['psw_update_'.$language];
            $body = str_replace('[datetime]', $datetime, $config['email_templates']['psw_update_'.$language]);
            $this->emailService->sendEmail($userProfile['profileEmail'], $subject, $body);
        }
    }
}
