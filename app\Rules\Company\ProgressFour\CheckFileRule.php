<?php

namespace App\Rules\Company\ProgressFour;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use App\Models\ProjectProgressFourFileModel;

class CheckFileRule implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (!ProjectProgressFourFileModel::where('id', $value)->exists()) {
            $fail('请上传文件');
        }
    }
}
