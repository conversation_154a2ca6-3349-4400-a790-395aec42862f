<?php

namespace App\Rules\Company\ProgressOne;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use App\Models\ProjectServiceModel;

class CheckValueAddedServiceRule implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $explore = explode(',', $value);
        if (!in_array(1, $explore)) {
            $fail('请选择增值服务');
        }

        $all = ProjectServiceModel::select('id')->where('projectCategoriesID', 1)->pluck('id')->toArray();

        if (array_diff($explore, $all)) {
            $fail('请选择增值服务');
        }
    }
}
