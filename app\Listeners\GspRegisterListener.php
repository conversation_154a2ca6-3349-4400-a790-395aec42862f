<?php

namespace App\Listeners;

use App\Events\GspRegisterEvent;
use App\Models\CommissionSetModel;
use App\Models\CompanyGsp;
use App\Models\IcbcOrderModel;
use App\Models\NotificationTemplateModel;
use App\Models\PaymentModel;
use App\Models\ProfileInfoModel;
use App\Models\ProjectCategoryModel;
use App\Models\ProjectServiceModel;
use App\Models\Setting;
use App\Services\EmailService;
use App\Services\PartnerIncomeService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class GspRegisterListener
{
    private $emailService;
    private $profileInfoModel;

    public function __construct(EmailService $emailService, ProfileInfoModel $profileInfoModel)
    {
        $this->emailService = $emailService;
        $this->profileInfoModel = $profileInfoModel;
    }

    public function handle(GspRegisterEvent $event): bool
    {
        $gspCompany = CompanyGsp::where('id', $event->gsp_id)->first();
        if ($gspCompany['pay_one'] != 1 || $gspCompany['pay_two'] != 1 || $gspCompany['pay_three'] != 1 ) {
            return false;
        }

/*        if ($gspCompany['status'] != CompanyGsp::STATUS_REPORT_SUCCESS) {
            return false;
        }*/

        try {

            // 开启事务
            DB::beginTransaction();

            // step1 创建绿智地球账号

            // 绿智账号和密码
            $account  = $gspCompany['email'];
            $password = generateRandomString(8);
            $hashPsw = hashPasswordNoKey($password);
            $gspProfileId = $this->createGspAccount([
                'account' => $account,
                'phone'   => $gspCompany['phone'],
                'hashPsw' => $hashPsw
            ]);

            // step2 发送邮箱
            $this->sendEmail($gspProfileId, $account, $password);

            // step3 发通知
            // $this->sendNotice($gspCompany);

            // step4 生成账单数据
            $this->cratePayment($event->gsp_id, $gspProfileId, $gspCompany['profile_id']);

            // 提交
            DB::commit();
            return true;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::info($e->getMessage());
            return false;
        }
    }

    // 创建绿智地球账号
    public function createGspAccount($data)
    {
        // step1 添加用户
        $insert = [
            'profileEmail'   => $data['account'],
            'profileRole'    => 4,
            'profileContact' => $data['phone'],
            'status' => ProfileInfoModel::STATUS_ACTIVE,
        ];

        $insert['profilePassword'] = ProfileInfoModel::INITIAL_PASSWORD_PREFIX . hashPassword($data['hashPsw']);
        $result = ProfileInfoModel::create($insert);

        // step2 用户设置表
        Setting::query()->create([
            'profile_id'  => $result->profileID
        ]);

        return $result->profileID;

    }

    /**
     * 发邮件通知
     * @return void
     */
    public function sendEmail($profileID, $account, $password)
    {
        // 获取邮箱模板
        $templateData  = $this->emailService->getTemplateData('', 'GspRegisterSuccess', $profileID);
        $template = $templateData['htmlBody'];
        $subject = $templateData['subject'];
        $body = str_replace('{{website}}', 'https://www.greensmartplanet.my', $template);
        $body = str_replace('{{account}}', $account, $body);
        $body = str_replace('{{password}}', $password, $body);
        $this->emailService->sendEmail($account, $subject, $body);


    }

    /**
     * 合伙人发通知
     * @param $gspCompany
     * @return void
     */
    public function sendNotice($gspCompany)
    {
        // 模板
        $templateData = NotificationTemplateModel::where('eventCode', 'GspRegisterSuccess')->first();

        $data = [
            'targetUser'  =>  $gspCompany->profile_id,
            'companyName' =>  $gspCompany->name,
        ];
        $templateData['notificationTitleEN'] = str_replace("{{companyName}}", $data['companyName'], $templateData['notificationTitleEN']);
        $templateData['notificationTitleMS'] = str_replace("{{companyName}}", $data['companyName'], $templateData['notificationTitleMS']);
        $templateData['notificationTitleZH'] = str_replace("{{companyName}}", $data['companyName'], $templateData['notificationTitleZH']);
        $templateData['notificationTitleZT'] = str_replace("{{companyName}}", $data['companyName'], $templateData['notificationTitleZT']);

        $templateData['notificationDescriptionEN'] = str_replace("{{companyName}}", $data['companyName'], $templateData['notificationDescriptionEN']);
        $templateData['notificationDescriptionMS'] = str_replace("{{companyName}}", $data['companyName'], $templateData['notificationDescriptionMS']);
        $templateData['notificationDescriptionZH'] = str_replace("{{companyName}}", $data['companyName'], $templateData['notificationDescriptionZH']);
        $templateData['notificationDescriptionZT'] = str_replace("{{companyName}}", $data['companyName'], $templateData['notificationDescriptionZT']);

        \App\Jobs\SendNotice::dispatch($data, 'GspRegisterSuccess', $gspCompany->profile_id)->onQueue('SendNoticeJob');

    }

    /**
     * 生成账单
     * @param $gspId 绿智入驻ID
     * @param $gspProfileId 绿智地球账号ID
     * @param $profileId 合伙人ID
     * @return void
     */
    public function cratePayment($gspId, $gspProfileId, $profileId)
    {
        $project = ProjectCategoryModel::where('projectCategoriesID', 1)->first();
        // 企业账单
        $serviceData1 = ProjectServiceModel::whereIn('id',
            [
                ProjectServiceModel::PAY_PRE_BOND,
                ProjectServiceModel::PAY_COMPANY_APPLY,
            ]
        )->get()->toArray();

        $serviceData2 = ProjectServiceModel::whereIn('id',
            [
                ProjectServiceModel::PAY_COMPANY_OPEN,
            ]
        )->get()->toArray();

        $serviceData3 = ProjectServiceModel::whereIn('id',
            [
                ProjectServiceModel::PAY_COMPANY_AIOPEN
            ]
        )->get()->toArray();

        $serviceData = array_merge($serviceData1 , $serviceData2 , $serviceData3);

        if ($serviceData) {
            foreach ($serviceData as $service) {
                $paymentNumber = PaymentModel::createPaymentNumber('GL',2);
                PaymentModel::query()->create([
                    'profileID'       => $gspProfileId,
                    'companyID'       => $gspId,
                    'type'            => 2,
                    'projectId'       => $project['projectCategoriesID'],  // 绿智地球平台
                    'detailId'        => $service['id'],  // 项目明细
                    'project_name'    => $project['projectCategoriesNameZH'],
                    'detail_name'     => $service['title_zh'],
                    'fee'             => $service['price'],  // 金额
                    'put_amount'      => $service['price'],  // 收款金额
                    'paymentNumber'   => $paymentNumber, // 单号
                    'paytime'         => date('Y-m-d H:i:s'),
                    'pay_type'        => 3, // 网银
                    'createtime'      => date('Y-m-d H:i:s'),
                ]);
            }
        }


        // 合伙人应付明细(收益分成)
        if ($serviceData) {
            foreach ($serviceData as $service) {
                // 分成比例
                $caseData = CommissionSetModel::where('type', 1)->first();
                $devidePercent = $caseData['case'.$service['case']];
                $devidePercent = bcdiv($devidePercent, 100, 2);
                // 分成金额
                $divideAmount = bcmul($service['price'], $devidePercent, 2);
                // 代扣税额
                $proxyFax = PartnerIncomeService::calculateTax($profileId, $divideAmount);
                // 分成净额
                $divideProfit = bcsub($divideAmount, $proxyFax, 2);
                // 累计已付
                $put_total = PaymentModel::where('profileID', $profileId)->where('type',1)->sum('put_amount');
                $put_total = bcadd($put_total, $divideProfit, 2);

                $paymentNumber = PaymentModel::createPaymentNumber('WL',1);
                PaymentModel::query()->create([
                    'profileID'       => $profileId,
                    'companyID'       => 0,
                    'type'            => 1, // 类型:1收入项目; 2支出项目
                    'projectId'       => $project['projectCategoriesID'],  // 绿智地球平台
                    'detailId'        => $service['id'],  // 项目明细
                    'fee'             => $service['price'],  // 金额
                    'paymentNumber'   => $paymentNumber, // 单号
                    'son_id'          => 0,  // 0表示自已做的项目
                    'project_name'    => $project['projectCategoriesNameZH'],
                    'detail_name'     => $service['title_zh'],
                    'main'            => 1, // 主体
                    'divide_type'     => 1, // 收益分成
                    'divide_percent'  => $devidePercent, // 分成比例
                    'divide_amount'   => $divideAmount, // 分成金额
                    'income_type'     => $service['case'], // 收益类型(A,B,C,D,E)
                    'proxy_fax'       => $proxyFax,   // 代扣税额
                    'divide_profit'   => $divideProfit,  // 分成净额
                    'put_amount'      => $divideProfit, // 付款金额
                    'put_total'       => $put_total,   //  累计已付
                    'pay_type'        => 3, // 网银
                    'createtime'      => date('Y-m-d H:i:s'),
                ]);
            }
        }

        // 合伙人的角色
        $group_id = 1;
        $profileData = ProfileInfoModel::where('profileID', $profileId)->first();
        if ($profileData['team_rank'] > 0 ) { // 三三制
            $group_id = 3;
        } else if ($profileData['pre_id'] > 0 ) { // 管理合伙人
            $group_id = 2;
        }

        // 管理津贴
        if ($group_id >1 && $profileData['team_rank']!=11 && $profileData['pre_id']!= $profileId) {
            $preId = $profileData['pre_id'];
            if ($serviceData) {
                foreach ($serviceData as $service) {
                    // 分成比例
                    $caseData = CommissionSetModel::where('type', 2)->first();
                    $devidePercent = $caseData['case'.$service['case']];
                    $devidePercent = bcdiv($devidePercent, 100, 4);
                    // 分成金额
                    $divideAmount = bcmul($service['price'], $devidePercent, 4);

                    // 代扣税额
                    $proxyFax = PartnerIncomeService::calculateTax($preId, $divideAmount);


                    // 分成净额
                    $divideProfit = bcsub($divideAmount, $proxyFax, 2);
                    // 累计已付
                    $put_total = PaymentModel::where('profileID', $preId)->where('type',1)->sum('put_amount');
                    $put_total = bcadd($put_total, $divideProfit, 2);

                    $paymentNumber = PaymentModel::createPaymentNumber('WL',1);
                    PaymentModel::query()->create([
                        'profileID'       => $preId,
                        'companyID'       => 0,
                        'type'            => 1, // 类型:1收入项目; 2支出项目
                        'projectId'       => $project['projectCategoriesID'],  // 绿智地球平台
                        'detailId'        => $service['id'],  // 项目明细
                        'fee'             => $service['price'],  // 金额
                        'paymentNumber'   => $paymentNumber, // 单号
                        'son_id'          => $profileId,  // 0表示自已做的项目
                        'project_name'    => $project['projectCategoriesNameZH'],
                        'detail_name'     => $service['title_zh'],
                        'main'            => 1, // 主体
                        'divide_type'     => 2, // 收益分成 (1收益分成;2管理津贴;3运营津贴)
                        'divide_percent'  => $devidePercent, // 分成比例
                        'divide_amount'   => $divideAmount, // 分成金额
                        'income_type'     => $service['case'], // 收益类型(A,B,C,D,E)
                        'proxy_fax'       => $proxyFax,   // 代扣税额
                        'divide_profit'   => $divideProfit,  // 分成净额
                        'put_amount'      => $divideProfit, // 付款金额
                        'put_total'       => $put_total,   //  累计已付
                        'pay_type'        => 3, // 网银
                        'createtime'      => date('Y-m-d H:i:s'),
                    ]);
                }
            }
        }


        // 运营津贴
        if ($group_id == 3) {

            // 查找所有上级
            $teamGroup = ProfileInfoModel::where('team_group', $profileData['team_group'])->pluck('pre_id', 'profileID');
            $teamParent = getAllParents($profileId, $teamGroup);

            $teamParent = array_filter($teamParent, function($value) {
                return $value !== 0; // 保留不等于 0 的项
            });


            if ($teamParent) {
                foreach ($teamParent as $teamProfileId) {
                    $teamProfile = ProfileInfoModel::where('profileID', $teamProfileId)->first();
                    if ($serviceData) {
                        foreach ($serviceData as $service) {
                            // 分成比例
                            $teamRank = $teamProfile['team_rank']-1;
                            $caseData = CommissionSetModel::where('type', 3)->where('rank', $teamRank)->first();
                            $devidePercent = $caseData['case'.$service['case']];
                            $devidePercent = bcdiv($devidePercent, 100, 6);
                            // 分成金额
                            $divideAmount = bcmul($service['price'], $devidePercent, 6);

                            // 代扣税额
                            $proxyFax = PartnerIncomeService::calculateTax($teamProfileId, $divideAmount);
                            // 分成净额
                            $divideProfit = bcsub($divideAmount, $proxyFax, 2);
                            // 累计已付
                            $put_total = PaymentModel::where('profileID', $teamProfileId)->where('type',1)->sum('put_amount');
                            $put_total = bcadd($put_total, $divideProfit, 2);
                            
                            $paymentNumber = PaymentModel::createPaymentNumber('WL',1);
                            PaymentModel::query()->create([
                                'profileID'       => $teamProfileId,
                                'companyID'       => 0,
                                'type'            => 1, // 类型:1收入项目; 2支出项目
                                'projectId'       => $project['projectCategoriesID'],  // 绿智地球平台
                                'detailId'        => $service['id'],  // 项目明细
                                'fee'             => $service['price'],  // 金额
                                'paymentNumber'   => $paymentNumber, // 单号
                                'son_id'          => $profileId,  // 0表示自已做的项目
                                'project_name'    => $project['projectCategoriesNameZH'],
                                'detail_name'     => $service['title_zh'],
                                'main'            => 1, // 主体
                                'divide_type'     => 3, // 收益分成 (1收益分成;2管理津贴;3运营津贴)
                                'divide_percent'  => $devidePercent, // 分成比例
                                'divide_amount'   => $divideAmount, // 分成金额
                                'income_type'     => $service['case'], // 收益类型(A,B,C,D,E)
                                'proxy_fax'       => $proxyFax,   // 代扣税额
                                'divide_profit'   => $divideProfit,  // 分成净额
                                'put_amount'      => $divideProfit, // 付款金额
                                'put_total'       => $put_total,   //  累计已付
                                'pay_type'        => 3, // 网银
                                'createtime'      => date('Y-m-d H:i:s'),
                            ]);
                        }
                    }
                }
            }
        }


    }

}
