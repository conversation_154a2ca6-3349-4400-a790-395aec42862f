<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PowerInfoModel extends Model
{
    use HasFactory;

    protected $table = 'cna_power_info';
    protected $guarded = [];
    protected $primaryKey = 'powerID';

    protected $appends = [
        'structureEN',
        'structureMS',
        'structureZH',
        'structureZT'
    ];


    /**
     * 获取权限操作名
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function powerActions()
    {
        return $this->hasMany(PowerActionModel::class, 'powerID');
    }

    public function structureInfo()
    {
        return $this->belongsTo(StructureModel::class,'structureCode', 'structureCode');
    }

    public function getStructureENAttribute()
    {
        return $this->structureInfo()->value('structureEN');
    }

    public function getStructureMSAttribute()
    {
        return $this->structureInfo()->value('structureMS');
    }

    public function getStructureZHAttribute()
    {
        return $this->structureInfo()->value('structureZH');
    }

    public function getStructureZTAttribute()
    {
        return $this->structureInfo()->value('structureZT');
    }


}