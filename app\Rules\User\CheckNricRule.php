<?php

namespace App\Rules\User;

use App\Models\CountryModel;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class CheckNricRule implements ValidationRule
{
    const PATTERN = '/^[1-9]\d{5}(19|20)\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[\dXx]$/';

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (env('COUNTRY') == CountryModel::COUNTRY_ID_CHINA) {
            if (!preg_match(self::PATTERN, $value)) {
                $fail(__('ID number error'));
            }
            $birthDate = substr($value, 6, 8);
            if (strtotime($birthDate) > strtotime(date("Y-m-d"))) {
                $fail(__('ID number error'));
            }
        }
    }
}
