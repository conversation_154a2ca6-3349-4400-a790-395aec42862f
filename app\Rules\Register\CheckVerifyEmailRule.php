<?php

namespace App\Rules\Register;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use App\Models\ProfileInfoModel;

class CheckVerifyEmailRule implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (ProfileInfoModel::where('profileEmail', $value)->exists()) {
            $fail(__('email exist'));
        }
    }
}
