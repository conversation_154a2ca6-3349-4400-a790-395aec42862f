<?php

namespace App\Http\Controllers\Api\v1\Ai;

use App\Http\Controllers\Controller;
use App\Services\AiService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

class AiController extends Controller
{
    //const ADMIN_DOMAIN = 'https://office.corporate-advisory.cn'; //AI助理的域名

    public function link(Request $request)
    {
        $user = $request->attributes->get('user');
        $cacheConfig = config('cachekey.key_ai_token');
        $key = sprintf($cacheConfig['key'], $user['profileID']);
        if (Cache::has($key)) {
            $token = Cache::get($key);
        } else {
            $token = AiService::generateToken($user);
            if ($token) {
                Cache::set($key, $token, $cacheConfig['expire']);
            }
        }
        if (empty($token)) {
            return responseFail();
        }

        return responseSuccess(['link' => env('AI_URL') . '?token=' . $token]);
    }
}
