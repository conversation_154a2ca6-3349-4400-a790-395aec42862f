<?php

namespace  App\Http\Controllers\Api\v1\Community;

use App\Http\Controllers\Controller;
use App\Models\Experience;
use App\Models\ExperienceData;
use App\Models\ProfileInfoModel;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Http\Request;

class CommunityController extends Controller
{
    //查询资源类型
    const SEARCH_TYPE_COMPANY = 0; //合伙人客户资源
    const SEARCH_TYPE_PROFESSIONAL = 1; //合伙人专业资源
    const SEARCH_TYPE_SKILL = 2; //合伙人案例资源
    const TEST_PROFILE_ID = 10; //用户表id<=10的是测试账号

    /**
     * 获取合伙人列表
     */
    public function index(Request $request)
    {
        $page = $request->get('page', 1);
        $pageSize = $request->get('page_size', 10);

        $keyword = $request->get('keyword', '');
        $nationalityId = $request->get('nationality_id', '');

        $searchType = $request->get('search_type', 0);

        $associates = ProfileInfoModel::with(['professionals', 'skills', 'company', 'nationality'])
            ->when($nationalityId != '', function ($q) use ($nationalityId) {
                $q->where('profileNationalityID', $nationalityId);
            })
            ->when($keyword != '', function ($q) use ($keyword, $searchType) {
                switch ($searchType) {
                    case self::SEARCH_TYPE_COMPANY:
                    default:
                        // 合伙人客户资源 company
                        $q->whereHas('company', function ($q) use ($keyword) {
                            $q->where('companyName', 'like', '%' . $keyword . '%');
                        });
                        break;
                    case self::SEARCH_TYPE_PROFESSIONAL:
                        // 合伙人专业资源 professional
                        $q->whereHas('professionals', function ($q) use ($keyword) {
                            $q->where('professionalDescription', 'like', '%' . $keyword . '%')
                                ->orWhere('professionalTitleZH', 'like', '%' . $keyword . '%')
                                ->orWhere('professionalTitleZT', 'like', '%' . $keyword . '%')
                                ->orWhere('professionalTitleMS', 'like', '%' . $keyword . '%')
                                ->orWhere('professionalTitleEN', 'like', '%' . $keyword . '%')
                                ->orWhere('professionalRelate', 'like', '%' . $keyword . '%');
                        });
                        break;
                    case self::SEARCH_TYPE_SKILL:
                        // 合伙人案例资源 skill
                        $q->whereHas('skills', function ($q) use ($keyword) {
                            $q->where('skillDescription', 'like', '%' . $keyword . '%')
                                ->orwhere('skillTitleZH', 'like', '%' . $keyword . '%')
                                ->orWhere('skillTitleZT', 'like', '%' . $keyword . '%')
                                ->orWhere('skillTitleMS', 'like', '%' . $keyword . '%')
                                ->orWhere('skillTitleEN', 'like', '%' . $keyword . '%')
                                ->orWhere('skillRelate', 'like', '%' . $keyword . '%');
                        });
                        break;
                }
            })
            ->whereIn('status', [ProfileInfoModel::STATUS_VERIFYING, ProfileInfoModel::STATUS_ACTIVE])
            ->where('profileID', '>', self::TEST_PROFILE_ID)
            ->paginate($pageSize);

        $items = collect($associates->items())->map(function ($item) use ($searchType, $keyword) {
            $item->profileAvatar = storageUrl($item->profileAvatar ?: ProfileInfoModel::AVATAR_PATH);
            $match = []; //匹配搜索词的内容
            $professions = []; //专业资源
            $skills = []; //技能资源
            if ($searchType == self::SEARCH_TYPE_COMPANY) {
                foreach ($item->company as $p) {
                    if (mb_stripos($p->companyName, $keyword) !== false) {
                        $match[] = $p->companyName;
                    }
                }
            }
            foreach ($item->professionals as $p) {
                $professions[] = [
                    'professionalID' => $p->professionalID,
                    'professionalDescription' => $p->pivot->professionalDescription,
                ];
                if ($searchType == self::SEARCH_TYPE_PROFESSIONAL) {
                    $value = [$p->professionalTitleZH, $p->professionalTitleZT, $p->professionalTitleMS,
                        $p->professionalTitleEN, $p->pivot->professionalDescription];
                    if ($p->professionalRelate) {
                        $relateTitle = explode(',', $p->professionalRelate);
                        $value = array_merge($value, $relateTitle);
                    }
                    foreach ($value as $v) {
                        if (mb_stripos($v, $keyword) !== false) {
                            $match[] = $v;
                        }
                    }
                }
            }
            foreach ($item->skills as $p) {
                $skills[] = [
                    'skillID' => $p->skillID,
                    'skillDescription' => $p->pivot->skillDescription,
                ];
                if ($searchType == self::SEARCH_TYPE_SKILL) {
                    $value = [$p->skillTitleZH, $p->skillTitleZT, $p->skillTitleMS, $p->skillTitleEN,
                        $p->pivot->skillDescription];
                    if ($p->skillRelate) {
                        $relateTitle = explode(',', $p->skillRelate);
                        $value = array_merge($value, $relateTitle);
                    }
                    foreach ($value as $v) {
                        if (mb_stripos($v, $keyword) !== false) {
                            $match[] = $v;
                        }
                    }
                }
            }
            $item->match_word = $match ? implode(',', $match) : '';
            $item->userProfessional = $professions;
            $item->userSkill = $skills;
            unset($item->professionals, $item->skills);
            return $item;
        })->all();

        $paginate = [
            'currentPage' => $page,
            'perPage' => $pageSize,
            'totalRecord' => $associates->total(),
            'totalPage' => $associates->lastPage()
        ];

        return responseSuccess(compact('items', 'paginate'));
    }

    /**
     * 获取合伙人详情
     */
    public function show($id)
    {
        $associate = ProfileInfoModel::with(['professionals', 'skills', 'company', 'nationality'])->find($id);
        if (!$associate) {
            return responseFail();
        }
        $associate->profileAvatar = storageUrl($associate->profileAvatar ?: ProfileInfoModel::AVATAR_PATH);

        return responseSuccess($associate);
    }

    public function list(Request $request)
    {
        $pageSize = $request->get('page_size', 10);
        $keyword = $request->get('keyword', '');
        $nationalityId = $request->get('nationality_id');
        $searchType = $request->get('search_type', 1);
        if ($keyword === '' || !in_array($searchType, ExperienceData::TYPE_ARR)) {
            return responseSuccess();
        }
        $selectKey = ['profileID', 'profileName', 'profileAvatar', 'profileEmail'];
        $res = [];
        if (in_array($searchType, [ExperienceData::TYPE_CREDENTIALS, ExperienceData::TYPE_SKILL])) {
            $experienceTitle = Experience::where('type', $searchType)
                ->where(function (Builder $query) use ($keyword) {
                    $query->where('title_zh', 'like', '%'.$keyword.'%')
                        ->orWhere('title_zt', 'like', '%'.$keyword.'%')
                        ->orWhere('title_ms', 'like', '%'.$keyword.'%')
                        ->orWhere('title_en', 'like', '%'.$keyword.'%');
                })
                ->get()->toArray();
            if ($experienceTitle) {
                $experienceTitle = array_column($experienceTitle, null, 'id');
                $experienceData = ExperienceData::whereIn('experience_id', array_keys($experienceTitle))
                    ->orWhere(function (Builder $query) use ($keyword, $searchType) {
                        $query->where('type', $searchType)
                            ->where('desc', 'like', '%'.$keyword.'%');
                    })
                    ->get()->toArray();
                $experienceData = array_column($experienceData, null, 'profile_id');
                $res = ProfileInfoModel::select($selectKey);
                if ($nationalityId) {
                    $res = $res->where('profileNationalityID', $nationalityId);
                }
                $res = $res->whereIn('profileID', array_keys($experienceData))
                    ->whereIn('status', [ProfileInfoModel::STATUS_VERIFYING, ProfileInfoModel::STATUS_ACTIVE])
                    ->where('profileID', '>', self::TEST_PROFILE_ID)
                    ->paginate($pageSize);
                foreach ($res as &$item) {
                    $item['profileAvatar'] = storageUrl($item['profileAvatar'] ?: ProfileInfoModel::AVATAR_PATH);
                    $matchWord = [];
                    $expData = $experienceData[$item['profileID']];
                    if (stripos($expData['desc'], $keyword) !== false) {
                        $matchWord[] = $expData['desc'];
                    }
                    $expTitle = $experienceTitle[$expData['experience_id']];
                    foreach (['title_zh', 'title_zt', 'title_ms', 'title_en'] as $k) {
                        if (stripos($expTitle[$k], $keyword) !== false) {
                            $matchWord[] = $expTitle[$k];
                        }
                    }
                    $item['match_word'] = implode(',', $matchWord);
                }
            } else {
                $experienceData = ExperienceData::where('type', $searchType)->where('desc', 'like', '%'.$keyword.'%')
                    ->get()->toArray();
                $experienceData = array_column($experienceData, null, 'profile_id');
                $res = ProfileInfoModel::select($selectKey);
                if ($nationalityId) {
                    $res = $res->where('profileNationalityID', $nationalityId);
                }
                $res = $res->whereIn('profileID', array_keys($experienceData))
                    ->whereIn('status', [ProfileInfoModel::STATUS_VERIFYING, ProfileInfoModel::STATUS_ACTIVE])
                    ->where('profileID', '>', self::TEST_PROFILE_ID)
                    ->paginate($pageSize);
                foreach ($res as &$item) {
                    $item['profileAvatar'] = storageUrl($item['profileAvatar'] ?: ProfileInfoModel::AVATAR_PATH);
                    $matchWord = [];
                    $expData = $experienceData[$item['profileID']];
                    if (stripos($expData['desc'], $keyword) !== false) {
                        $matchWord[] = $expData['desc'];
                    }
                    $item['match_word'] = implode(',', $matchWord);
                }
            }
        } else if ($searchType == ExperienceData::TYPE_CLIENT) {
            $experienceData = ExperienceData::where('type', $searchType)
                ->where(function (Builder $query) use ($keyword) {
                    $query->where('experience_name', 'like', '%'.$keyword.'%')
                        ->orWhere('desc', 'like', '%'.$keyword.'%');
                })
                ->get()->toArray();
            $experienceData = array_column($experienceData, null, 'profile_id');
            $res = ProfileInfoModel::select($selectKey);
            if ($nationalityId) {
                $res = $res->where('profileNationalityID', $nationalityId);
            }
            $res = $res->whereIn('profileID', array_keys($experienceData))
                ->whereIn('status', [ProfileInfoModel::STATUS_VERIFYING, ProfileInfoModel::STATUS_ACTIVE])
                ->where('profileID', '>', self::TEST_PROFILE_ID)
                ->paginate($pageSize);
            foreach ($res as &$item) {
                $item['profileAvatar'] = storageUrl($item['profileAvatar'] ?: ProfileInfoModel::AVATAR_PATH);
                $matchWord = [];
                $expData = $experienceData[$item['profileID']];
                if (stripos($expData['experience_name'], $keyword) !== false) {
                    $matchWord[] = $expData['experience_name'];
                }
                if (stripos($expData['desc'], $keyword) !== false) {
                    $matchWord[] = $expData['desc'];
                }
                $item['match_word'] = implode(',', $matchWord);
            }
        }

        return responseSuccess($res);
    }
}
