<?php

namespace App\Http\Controllers\Api\v1\Click;

use App\Http\Controllers\Controller;
use App\Models\ClickLog;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class ClickController extends Controller
{
    //点击位置
    const POS = [
        'doc_view' => 1, //资料-预览
        'doc_download' => 2, //资料-下载
        'community_detail' => 3, //社区-详情
    ];

    //记录点击
    public function log(Request $request)
    {
        $validated = $request->validate([
            'pos' => ['required', 'integer', Rule::in(self::POS)],
            'obj_id' => 'required|integer|min:0',
        ]);
        $user = $request->attributes->get('user');
        ClickLog::create([
            'profile_id' => $user['profileID'],
            'pos' => $validated['pos'],
            'obj_id' => $validated['obj_id'],
        ]);
        return responseSuccess();
    }
}
