<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class CurrencyRateLogModel extends Model
{
    use HasFactory;

    protected $table = 'cna_currency_rate_log';

    const UPDATED_AT = null;

    protected $fillable = [
        'currency_id',
        'rate',
        'year',
        'mouth',
        'date',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $now = Carbon::now();
            $model->year = $model->year ?? $now->year;
            $model->mouth = $model->mouth ?? $now->month;
            $model->date = $model->date ?? $now->day;
        });
    }

}
