<?php
namespace App\Http\Controllers\Admin\v1;

use App\Http\Controllers\Controller;
use App\Models\ActiveLog;
use App\Models\ProfileInfoModel;
use App\Models\StructureModel;
use Symfony\Component\HttpFoundation\Request;

class ActiveLogController extends Controller
{
    /**
     * 活动日志信息
     * @param Request $request
     * @return null
     */
    public function index(Request $request)
    {
        // 可查询其他合伙人的活动日志
        $profileId = $request->get('profileID', 0);

        $pageSize = $request->get('page_size', 10);
        $user = $request->attributes->get('user');

        // 查询
        $list = ActiveLog::select('id', 'active_type', 'obj_type', 'desc','created_at')
            ->where('system', ActiveLog::SYSTEM_CNA_ADMIN)
            ->where('profile_id', $profileId ?: $user['profileID'])
            ->orderByDesc('created_at')
            ->paginate($pageSize);


        $arrActiveType = ActiveLog::activeTypeLang();
        $arrObjType = ActiveLog::objTypeLang();

        $items = collect($list->items())->map(function ($item) use ($arrActiveType, $arrObjType){

            $item['descEN'] = $arrActiveType[$item['active_type']]['structureEN'].$arrObjType[$item['obj_type']]['structureEN'].':'.$item['desc'];
            $item['descMS'] = $arrActiveType[$item['active_type']]['structureMS'].$arrObjType[$item['obj_type']]['structureMS'].':'.$item['desc'];
            $item['descZH'] = $arrActiveType[$item['active_type']]['structureZH'].$arrObjType[$item['obj_type']]['structureZH'].':'.$item['desc'];
            $item['descZT'] = $arrActiveType[$item['active_type']]['structureZT'].$arrObjType[$item['obj_type']]['structureZT'].':'.$item['desc'];
            return $item;
        });


        $paginate = [
            'page_size' => $pageSize,
            'total' => $list->total(),
            'total_page' => $list->lastPage(),
        ];

        return responseSuccess(compact('items', 'paginate'));
    }
}