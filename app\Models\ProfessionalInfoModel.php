<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProfessionalInfoModel extends Model
{
    use HasFactory;

    protected $table = 'cna_professional_info';
    protected $guarded = [];
    
    /**
     * Method 获取数据集
     *
     * @return void
     */
    public function getData()
    {
        return self::get();
    }
    
    /**
     * Method 获取数据记录
     *
     * @param $professionalID $professionalID [主键]
     *
     * @return void
     */
    public function getRecord($professionalID)
    {
        return self::where('professionalID', $professionalID)->first();
    }
}
