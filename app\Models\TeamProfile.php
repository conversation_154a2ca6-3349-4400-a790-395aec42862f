<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class TeamProfile extends Model
{
    protected $table = 'cna_team_profile';
    protected $guarded = [];

    protected $appends = [
        'rankName',
    ];

    public function rankInfo()
    {
        return $this->belongsTo(TeamRank::class,'rank');
    }

    public function getRankNameAttribute()
    {
        return $this->rankInfo()->value('name');
    }


}
