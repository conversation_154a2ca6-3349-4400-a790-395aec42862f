<?php

namespace App\Http\Requests\User;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use App\Exceptions\DefaultException;

class UpdateUserInfo extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'profileAddressStreet'   => ['required'],
            'profileAddressUnit'     => ['required'],
            'profileAddressCountry'  => ['required'],
            'profileAddressStateId'    => ['required', 'integer', 'min:1'],
            'profileAddressCityId'     => ['required', 'integer', 'min:1'],
            'profileAddressDistrictId' => ['required', 'integer', 'min:1'],
            'profession_id' => ['integer', 'min:0'],
        ];
    }

    public function messages()
    {
        return [
            'profileAddressStreet.required'   => __('please fill in the street'),
            'profileAddressUnit.required'     => __('please fill in the unit'),
            'profileAddressCountry.required'  => __('please select address - country'),
            'profileAddressStateId.required'    => __('please fill in the state'),
            'profileAddressCityId.required'     => __('please fill in the city'),
            'profileAddressDistrictId.required' => __('please fill in the district'),
            'profession_id' => __('param error', ['param' => 'profession_id']),
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        $error= $validator->errors()->all();
        throw new DefaultException($error[0]);
    }
}
