<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('cna_bills', function (Blueprint $table) {
            $table->enum('billStatus', ['Y', 'N'])->default('Y')->comment('Y=已付,N=未付');
            $table->unsignedInteger('billCompanyId')->default(0)->comment('账单公司ID');
            $table->unsignedInteger('billDescriptionId')->default(0)->comment('账单描述ID');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('cna_bills', function (Blueprint $table) {
            $table->dropColumn('billStatus');
            $table->dropColumn('billCompanyId');
            $table->dropColumn('billDescriptionId');
        });
    }
};
