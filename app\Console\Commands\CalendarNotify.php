<?php
namespace App\Console\Commands;

use App\Models\Calendar;
use App\Models\EmailTemplateModel;
use App\Models\ProfileInfoModel;
use App\Models\Setting;
use App\Services\EmailService;
use Illuminate\Console\Command;

class CalendarNotify extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'calendar_notify';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '日程发送邮件通知';
    private $emailService;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(EmailService $emailService)
    {
        parent::__construct();
        $this->emailService = $emailService;
    }

    /**
     * Execute console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $now = time();
        $data = Calendar::where('notify_status', 0)->where('notify_type', '>', Calendar::NOTIFY_TYPE_NO)->get()->toArray();
        $data = array_filter($data, function($v) use ($now) {
            if ($v['notify_type'] == Calendar::NOTIFY_TYPE_1_DAY) {
                $notifyTime = $v['start_time'] - 86400;
            } else if ($v['notify_type'] == Calendar::NOTIFY_TYPE_2_DAY) {
                $notifyTime = $v['start_time'] - 2 * 86400;
            }
            return (isset($notifyTime) && $notifyTime <= $now);
        });
        if (empty($data)) {
            return;
        }
        $ids = array_unique(array_column($data, 'profile_id'));
        $users = ProfileInfoModel::select('profileEmail', 'profileID')->whereIn('profileID', $ids)->get()
            ->toArray();
        $users = array_column($users, null, 'profileID');
        $settings = Setting::whereIn('profile_id', $ids)->pluck('language', 'profile_id');
        $template = EmailTemplateModel::where('eventCode', EmailTemplateModel::EVENT_CODE_CALENDAR_NOTIFY)->first();
        foreach ($data as $item) {
            if (!isset($users[$item['profile_id']]) || empty($users[$item['profile_id']]['profileEmail'])) {
                continue;
            }
            $user = $users[$item['profile_id']];
            $lang = empty($settings[$item['profile_id']])? 'ZH': strtoupper($settings[$item['profile_id']]);
            $subject = $template['emailTitle'.$lang] . $item['title'];
            $content = str_replace('[title]', $item['title'], $template['emailDescription'.$lang]);
            $time = date('Y-m-d H:i:s', $item['start_time']) . ' ~ ' . date('Y-m-d H:i:s', $item['end_time']);
            $content = str_replace('[time]', $time, $content);
            $this->emailService->sendEmail($user['profileEmail'], $subject, $content);
            Calendar::where('id', $item['id'])->update(['notify_status' => 1]);
        }
    }
}

