<?php

namespace App\Http\Controllers\Admin\v1;

use App\Http\Controllers\Controller;
use App\Models\AiManFile;
use App\Services\OssService;
use App\Services\ZipService;
use Illuminate\Http\Request;

class DigitalHumanController extends Controller
{
    /**
     * 用户上传的数字人文件列表
     */
    public function index(Request $request)
    {
        $keyword = $request->get('keyword', '');
        $status = $request->get('status', '');
        $pageSize = $request->get('page_size', 10);

        $aiMen = AiManFile::with(['profile:profileID,profileName,profilePartnerCode'])
            ->when($keyword != '', function ($q) use ($keyword) {
                $q->whereHas('profile', function ($qu) use ($keyword) {
                    $qu->where('profileName', 'like', '%' . $keyword . '%')
                        ->orWhere('profilePartnerCode', 'like', '%' . $keyword . '%');
                });
            })
            ->when($status != '', function ($q) use ($status) {
                $q->where('status', $status);
            })
            ->paginate($pageSize);

        $items = $aiMen->items();

        $paginate = [
            'page_size' => $pageSize,
            'total' => $aiMen->total(),
            'total_page' => $aiMen->lastPage(),
        ];

        return responseSuccess(compact('items', 'paginate'));
    }

    /**
     * 下载用户上传的数字人文件
     */
    public function download(Request $request)
    {
        $request->validate([
            'type' => 'required|string|in:local,oss',
            'file_path' => 'required|string',
        ]);

        if ($request->type == 'local') {
            $fileUrl = env('APP_URL') . '/storage/' . $request->file_path; // local 上传文件的basename
        } else {
            $fileUrl = OssService::link($request->file_path);
        }

        // 获取文件名
        $fileName = basename($fileUrl);

        // 读取 OSS 文件
        $client = new \GuzzleHttp\Client();
        $response = $client->get($fileUrl, ['stream' => true]);

        // 流式返回文件内容
        return response()->streamDownload(function () use ($response) {
            $body = $response->getBody();
            while (!$body->eof()) {
                echo $body->read(1024);
            }
        }, $fileName);
    }

    /**
     * 审核资料
     * @return void
     */
    public function check(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|exists:cna_ai_man_file,id',
            'status' => 'required|integer|in:2,3',
            'finished_files' => 'required_if:status,2|file', // 不确定成品文件有几个，先上传单个文件
        ]);

        $user = $request->attributes->get('user');

        // 获取详情
        $info = AiManFile::find($request->id);
        if (empty($info)) {
            return responseFail(__('info no exist')); // 信息不存在
        }

        if ($info['status'] == 2 || $info['status'] == 3) {
            return responseFail(__('param error', ['param' => 'status']));
        }

        $status = $request->status;

        // 更新
        $info->status = $status;
        if ($status == 2) {
            // 审核通过
            $info->finished_files = OssService::upload($request->finished_files);
        }
        $info->save();

        // todo 通知用户
        return responseSuccess();
    }

    /**
     * 打包资料
     * @return void
     */
    public function zipFile(Request $request)
    {
        $user = $request->attributes->get('user');
        $id = $request->input('id');

        if (empty($id)) {
            return responseFail(__('missing parameter', ['param' => 'id'])); // 参数缺失:id
        }

        // 获取详情
        $info = AiManFile::find($id);
        if (empty($info)) {
            return responseFail(__('info no exist')); // 信息不存在
        }

        // 打包文件
        $files = [];
        if ($info['pic']) {
            $files[] = OssService::link($info['pic']);
        }
        if ($info['pic1']) {
            $files[] = OssService::link($info['pic1']);
        }
        if ($info['pic2']) {
            $files[] = OssService::link($info['pic2']);
        }
        if ($info['audio']) {
            $files[] = OssService::link($info['audio']);
        }
        if ($info['video']) {
            $files[] = OssService::link($info['video']);
        }
        if ($files) {
            $result = ZipService::createZip($files, $info['profile_id'] . '_aiman.zip');
            if ($result) return responseSuccess($result);
        }

        return responseFail();
    }
}
