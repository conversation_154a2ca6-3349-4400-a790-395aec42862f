<?php
namespace App\Services\ICBC;

use App\Models\IcbcOrderModel;
use App\Services\ICBC\Lib\DefaultIcbcClient;
use App\Services\ICBC\Lib\IcbcConstants;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use App\Exceptions\WechatException;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class ICBCService
{
    // 生成支付二维码
    const API_QRGENERATE_V3 = 'https://gw.open.icbc.com.cn/api/cardbusiness/qrcode/qrgenerate/V3';
    // 用户二维码被扫支付
    const API_QRCODE_SCANNED_PAY_V5 = 'https://gw.open.icbc.com.cn/api/mybank/pay/qrcode/scanned/pay/V5';
    // 二维码支付状态查询接口
    const API_QRCODE_PAYSTATUS_V4 = 'https://gw.open.icbc.com.cn/api/mybank/pay/qrcode/scanned/paystatus/V4';
    // 二维码支付退货接口
    const API_QRCODE_SCANNED_RETURN_V2 = 'https://gw.open.icbc.com.cn/api/mybank/pay/qrcode/scanned/return/V2';


    public static function getPrivateKey()
    {
        $privateKey = base_path('app/Services/ICBC/icbc_key/yourname.pri');

        // 读取文件内容到数组
        if (file_exists($privateKey)) {
            $privateKey = file_get_contents($privateKey);
            return $privateKey;
        } else {
            return false;
        }
    }

    public static function getPublicKey()
    {
        $publicKey = base_path('app/Services/ICBC/icbc_key/public_cna_pro.pem');

        // 读取文件内容到数组
        if (file_exists($publicKey)) {
            $publicKey = file_get_contents($publicKey);
            return $publicKey;
        } else {
            return false;
        }
    }

    /**
     * 生成支付二维码
     * @param $outTradeNo
     * @param $total
     * @return null
     * @throws \Random\RandomException
     */
    public static function generatePayQRcode($outTradeNo, $total, $notify_url)
    {

        $ip = request()->ip(); // 获取当前请求的 IP 地址

        $request = array(
            "serviceUrl" => ICBCService::API_QRGENERATE_V3,
            "method" => 'POST',
            "isNeedEncrypt" => false,
            "biz_content" => array(
                "mer_id"        => config('icbc.mer_id'),
                "store_code"    => "",
                "out_trade_no"  => $outTradeNo,
                "order_amt"     => (string)$total,
                "trade_date"    => date('Ymd'),
                "trade_time"    => date('His'),
                "attach"        => config('icbc.description'),
                "pay_expire"    => "1200",
                "notify_url"    => $notify_url,
                "tporder_create_ip"=> $ip,
                "sp_flag"       =>"0",
                "notify_flag"   =>"1"
            )

        );

        $client = new DefaultIcbcClient(config('icbc.app_id'),
            self::getPrivateKey(),
            IcbcConstants::$SIGN_TYPE_RSA2,
            '',
            '',
            self::getPublicKey(),
            '',
            '',
            '',
            '');

        $resp = $client->execute($request,'Oikeclo001','');
        $respObj = json_decode($resp,true);
        if($respObj["return_code"] == 0){ //sucess
            return $respObj;
        }else{//fail
            Log::info('icbc:error:'.$respObj["return_msg"]);
            return false;
        }


    }


    /**
     * 二维码支付状态查询接口 （工行订单号和商户订单号必输其中一个，商户系统内唯一）
     * @param $outTradeNo
     * @param $order_id
     * @return void
     * @throws \Random\RandomException
     */
    public static function payStatusQRcode($outTradeNo='', $order_id='')
    {
        $orderData = IcbcOrderModel::where('out_trade_no', $outTradeNo)->first();
        if (empty($orderData)) {
            return false;
        }

        $tradeData = date('Ymd', strtotime($orderData['pay_time']));
        $request = array(
            "serviceUrl" => ICBCService::API_QRCODE_PAYSTATUS_V4,
            "method" => 'POST',
            "isNeedEncrypt" => false,
            "biz_content" => array(
                "mer_id"        => config('icbc.mer_id'),
                "out_trade_no"  => $outTradeNo,
                "trade_date"    => $tradeData,
                "order_id"      => $order_id,
                "deal_flag"     => "0",
            )

        );

        $client = new DefaultIcbcClient(config('icbc.app_id'),
            self::getPrivateKey(),
            IcbcConstants::$SIGN_TYPE_RSA2,
            '',
            '',
            self::getPublicKey(),
            '',
            '',
            '',
            '');

        $resp = $client->execute($request,'Oikeclo001','');
        $respObj = json_decode($resp,true);
        if($respObj["return_code"] == 0){ //sucess
            return $respObj;
        }else{//fail
            Log::info('icbc:error:'.$respObj["return_msg"]);
            return false;
        }
    }

    /**
     * 二维码支付退货接口
     * @param $outTradeNo
     * @param $order_id
     * @return void
     */
    public function returnQRcodeScanned($outTradeNo='', $order_id='', $reject_no, $reject_amt)
    {
        if (empty($outTradeNo) && empty($order_id)) {
            return false;
        }

        $msg_id = ICBCService::generateRandomString(32);
        $postData = [
            'app_id'        => config('icbc.app_id'),
            'msg_id'        => $msg_id,
            'format'        => 'json',
            'charset'       => 'utf-8',
            'sign_type'     => 'RSA2',
            'sign'          => '',
            'timestamp'     => date('Y-m-d H:i:s'),
            'biz_content'   => [
                'mer_id'        => config('icbc.mer_id'),       // 商户、部门编号
                'out_trade_no'  => !empty($outTradeNo)?$outTradeNo:'',  // 商户订单号(商户交易系统中唯一)
                'order_id'      => !empty($order_id)?$order_id:'',      // 工行系统订单号工行订单号和商户订单号必输其中一个）
                'reject_no'     => $reject_no,                      // 退款编号,商户系统生成的退款编号，商户交易系统中需唯一
                'reject_amt'    => $reject_amt,                     // 退款金额
                'oper_id'       => '',                              // 操作人员ID
            ]

        ];

        // 调用ICBC接口
        $response = self::secureCurlPostRequest(ICBCService::API_QRCODE_SCANNED_RETURN_V2, $postData);

        if ($response['success']) {
            // 返回数据
            $response_biz_content = $response['response']['response_biz_content'];
            if ($response_biz_content['return_code'] == 0) {
                return $response_biz_content;
            } else {
                // 异常处理
                Log::info('API_QRCODE_SCANNED_RETURN_V2:'.$response_biz_content);
                return false;
            }

        } else {
            Log::info($response['message']);
            return false;
        }

    }

    /**
     * 生成唯一编号
     * @param $length
     * @return string
     * @throws \Random\RandomException
     */
    public static function generateRandomString($length): string
    {
        return bin2hex(random_bytes($length));
    }

    /**
     * 生成订单号
     * @param $userId  用户ID
     * @param $orderType 前缀
     * @return string
     * @throws \Random\RandomException
     */
    public static function generateOutTradeNo($userId, $orderType = '')
    {
        // 获取当前日期时间（精确到毫秒）
        $datetime = date('YmdHis') . substr((string)round(microtime(true) * 1000), -3);

        // 生成随机数部分（6位随机数）
        $randomPart = random_int(100000, 999999);

        // 拼接生成订单号
        $orderNumber = $orderType . $datetime . $userId . $randomPart;

        return $orderNumber;
    }

    /**
     * 发起一个安全的 POST 请求
     *
     * @param string $url 请求的 URL
     * @param array $data 要发送的 POST 数据
     * @param array $options 可选的 cURL 选项
     * @return array 响应数据
     */
    public static function secureCurlPostRequest($url, $data, $options = [])
    {
        // 初始化 cURL 会话
        $ch = curl_init();

        // 设置默认的 cURL 选项
        $defaultOptions = [
            CURLOPT_URL => $url,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => http_build_query($data),
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_SSL_VERIFYPEER => true, // 验证 SSL 证书
            CURLOPT_SSL_VERIFYHOST => 2,   // 验证 SSL 证书中的主机名
            CURLOPT_TIMEOUT => 30,         // 设置超时时间
            CURLOPT_USERAGENT => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3',
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/x-www-form-urlencoded',
            ],
        ];

        // 合并用户提供的选项
        $options = $defaultOptions + $options;

        // 设置 cURL 选项
        curl_setopt_array($ch, $options);

        // 执行请求
        $response = curl_exec($ch);

        // 检查是否有错误发生
        if (curl_errno($ch)) {
            $error = curl_error($ch);
            curl_close($ch);
            return [
                'success' => false,
                'message' => "cURL error: $error",
            ];
        }

        // 获取 HTTP 状态码
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        // 关闭 cURL 会话
        curl_close($ch);

        // 返回响应数据
        return [
            'success' => true,
            'response' => $response,
        ];
    }

}