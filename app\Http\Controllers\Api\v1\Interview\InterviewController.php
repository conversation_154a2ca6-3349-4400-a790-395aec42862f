<?php

namespace App\Http\Controllers\Api\v1\Interview;

use App\Http\Controllers\Controller;
use App\Models\Interview;
use App\Models\InterviewQuestionBase;
use App\Models\InterviewQuestionBaseNext;
use App\Models\ProfileInfoModel;
use Illuminate\Http\Request;

class InterviewController extends Controller
{
    public function status(Request $request)
    {
        $user = $request->attributes->get('user');
        $data = Interview::where('user_id', $user['profileID'])->first();
        if (!$data) {
            $data = Interview::add($user['profileID']);
        }

        return responseSuccess($data);
    }

    public function info(Request $request)
    {
        $body = $request->validate([
            'user_id' => ['required', 'integer', 'min:1'],
        ]);
        $data = Interview::where('user_id', $body['user_id'])->first();
        if (!$data) {
            $data = Interview::add($body['user_id']);
        }
        if ($data['question_id']) {
            $questionIds = explode(',', $data['question_id']);
            $questions = InterviewQuestionBase::whereIn('id', $questionIds)->get()->toArray();
            if ($questions) {
                $questions = array_column($questions, null, 'id');
                $questionNext = InterviewQuestionBaseNext::whereIn('base_id', $questionIds)
                    ->pluck('next', 'base_id');
                foreach ($questionNext as $k => $v) {
                    if (!isset($questions[$k]['next_question'])) {
                        $questions[$k]['next_question'] = []; 
                    }
                    $questions[$k]['next_question'][] = $v;
                }
                $data['question'] = array_values($questions);
            }
        }

        return responseSuccess($data);
    }

    public function comment(Request $request)
    {
        $body = $request->validate([
            'user_id' => ['required', 'integer', 'min:1'],
            'comment_draft' => ['string'],
            'comment' => ['string'],
        ]);
        $user = $request->attributes->get('user');
        $record = Interview::where('user_id', $body['user_id'])->first();
        if (!$record || $record['status'] != Interview::STATUS_ING) {
            return responseFail();
        }
        $bUser = ProfileInfoModel::where('profileID', $body['user_id'])->first();
        if (!$bUser || $bUser['pre_id'] != $user['profileID']) {
            return responseFail();
        }
        if (isset($body['comment'])) {
            $record->comment = $body['comment'];
            $record->status = Interview::STATUS_REVIEW;
            $record->save();
        } else if (isset($body['comment_draft'])) {
            $record->comment_draft = $body['comment_draft'];
            $record->save();
        }

        return responseSuccess();
    }
}