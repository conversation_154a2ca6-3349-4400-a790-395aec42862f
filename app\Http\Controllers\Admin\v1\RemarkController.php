<?php

namespace App\Http\Controllers\Admin\v1;

use App\Http\Controllers\Controller;
use App\Models\ActiveLog;
use App\Models\CompanyModel;
use App\Models\ProfileInfoModel;
use App\Models\RemarkModel;
use Illuminate\Support\Facades\Validator;
use Symfony\Component\HttpFoundation\Request;

class RemarkController extends Controller
{

    /**
     * 备注列表信息
     * @param Request $request
     * @return null
     */
    public function index(Request $request)
    {
        $pageSize = $request->get('page_size', 10);
        $objID = $request->get('objID');
        $type  = $request->get('type'); //类别: 1合伙人;2行政;;3律师;4客户
        $user = $request->attributes->get('user');

        if (empty($objID) || empty($type)) {
            return responseFail(__('param error', ['param' => 'id']));
        }

        // 查询
        $query = RemarkModel::query()->with('editUser:profileID,profileName');
        // 类别
        if (in_array($type, [1, 2, 3])) { // 合伙人，行政，律师
            $query->with('profileInfo');
        } else if ($type == 4) { // 企业
            $query->with('companyInfo');
        }

        $list = $query->where('editUser', $user['profileID'])->when($objID != '' && $type != '', function ($query) use ($objID, $type) {
            return $query->where('objID', $objID)->where('remarkType', $type);
        })->orderBy('created_at', 'asc')->paginate($pageSize);

        $items = collect($list->items())->map(function ($item) use ($type) {
            if (in_array($type, [1, 2, 3])) {
                $item->objName  = $item->profileInfo->profileName;
                unset($item->profileInfo);
            } else if ($type == 4) {
                $item->objName  = $item->companyInfo->companyName;
                unset($item->companyInfo);
            }
            return $item;
        });

        $paginate = [
            'page_size' => $pageSize,
            'total' => $list->total(),
            'total_page' => $list->lastPage(),
        ];

        return responseSuccess(compact('items', 'paginate'));
    }

    /**
     * 添加备注
     * @param Request $request
     * @return void
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'remarkType' => 'required',
            'remarkContent' => 'required',
            'objID' => 'required',
        ]);

        // 验证失败
        if ($validator->fails()) {
            return responseFail($validator->errors()->all());
        }

        $user = $request->attributes->get('user');
        $param = $request->all();

        // 新增
        $result = RemarkModel::query()->create([
            'remarkType'      => $param['remarkType'],
            'remarkContent'   => $param['remarkContent'],
            'objID'           => $param['objID'],
            'editUser'        => $user['profileID'],
            'editRole'        => $user['profileRole'],
            'created_at'       => date('Y-m-d H:i:s')
        ]);

        if ($result) {

            // 记录日志
            if ($param['remarkType'] == 1) {
                $type = ActiveLog::ADMIN_API_V1_REMARK_ASSOCIATE;
                $descName = ProfileInfoModel::where('profileID', $param['objID'])->value('profileName');
            } else if ($param['remarkType'] == 2) {
                $type = ActiveLog::ADMIN_API_V1_REMARK_OA;
                $descName = ProfileInfoModel::where('profileID', $param['objID'])->value('profileName');
            } else if ($param['remarkType'] == 4) {
                $type = ActiveLog::ADMIN_API_V1_REMARK_CLIENT;
                $descName = CompanyModel::where('companyID', $param['objID'])->value('companyName');
            }

            ActiveLog::log(
                $user['profileID'],
                ActiveLog::ADMIN_API_V1_CREATE,
                $type,
                $param['objID'],
                $descName . '-' . $param['remarkContent'],
                ActiveLog::SYSTEM_CNA_ADMIN
            );

            return responseSuccess();
        } else {
            return responseFail(__('save failed'));
        }
    }

    /**
     * 备注详情
     * @param Request $request
     * @param $id
     * @return void
     */
    public function edit(Request $request, $id)
    {
        $user = $request->attributes->get('user');
        if (empty($id)) {
            return responseFail(__('param error', ['param' => 'id'])); // 参数缺失:id
        }

        // 获取详情
        $info = RemarkModel::where('remarkID', $id)->where('editUser', $user['profileID'])->first();
        if (empty($info)) {
            return responseFail(__('info no exist')); // 信息不存在
        }

        return responseSuccess($info);
    }

    /**
     * 修改备注
     * @param Request $request
     * @param $id
     * @return void
     */
    public function update(Request $request, $id)
    {
        $remarkContent = $request->get('remarkContent');
        $user = $request->attributes->get('user');

        if (empty($id)) {
            return responseFail(__('param error', ['param' => 'id'])); // 参数缺失:id
        }

        if (empty($remarkContent)) {
            return responseFail(__('param error', ['param' => 'id'])); // 参数缺失:id
        }

        // 获取详情
        $info = RemarkModel::where('remarkID', $id)->where('editUser', $user['profileID'])->first();
        if (empty($info)) {
            return responseFail(__('info no exist')); // 信息不存在
        }

        // 更新
        $result = $info->update([
            'remarkContent'   => $remarkContent,
            'editUser'        => $user['profileID'],
            'editRole'        => $user['profileRole'],
            'updated_at'      => date('Y-m-d H:i:s')
        ]);

        if ($result) {

            // 记录日志
            if ($info['remarkType'] == 1) {
                $type = ActiveLog::ADMIN_API_V1_REMARK_ASSOCIATE;
                $descName = ProfileInfoModel::where('profileID', $info['objID'])->value('profileName');
            } else if ($info['remarkType'] == 2) {
                $type = ActiveLog::ADMIN_API_V1_REMARK_OA;
                $descName = ProfileInfoModel::where('profileID', $info['objID'])->value('profileName');
            } else if ($info['remarkType'] == 4) {
                $type = ActiveLog::ADMIN_API_V1_REMARK_CLIENT;
                $descName = CompanyModel::where('companyID', $info['objID'])->value('companyName');
            }

            ActiveLog::log(
                $user['profileID'],
                ActiveLog::ADMIN_API_V1_EDIT,
                $type,
                $id,
                $descName . '-' . $remarkContent,
                ActiveLog::SYSTEM_CNA_ADMIN
            );

            return responseSuccess();
        } else {
            return responseFail(__('save failed'));
        }
    }

    /**
     * 删除备注
     * @param Request $request
     * @param $id
     * @return void
     */
    public function destroy(Request $request, $id)
    {
        $user = $request->attributes->get('user');

        if (empty($id)) {
            return responseFail(__('param error', ['param' => 'id'])); // 参数缺失:id
        }

        // 获取详情
        $info = RemarkModel::where('remarkID', $id)->where('editUser', $user['profileID'])->first();
        if (empty($info)) {
            return responseFail(__('info no exist')); // 信息不存在
        }

        $remarkContent = $info['remarkContent'];

        // 删除
        $result =  $info->delete();
        if ($result !== false) {

            // 记录日志
            if ($info['remarkType'] == 1) {
                $type = ActiveLog::ADMIN_API_V1_REMARK_ASSOCIATE;
                $descName = ProfileInfoModel::where('profileID', $info['objID'])->value('profileName');
            } else if ($info['remarkType'] == 2) {
                $type = ActiveLog::ADMIN_API_V1_REMARK_OA;
                $descName = ProfileInfoModel::where('profileID', $info['objID'])->value('profileName');
            } else if ($info['remarkType'] == 4) {
                $type = ActiveLog::ADMIN_API_V1_REMARK_CLIENT;
                $descName = CompanyModel::where('companyID', $info['objID'])->value('companyName');
            }

            ActiveLog::log(
                $user['profileID'],
                ActiveLog::ADMIN_API_V1_DELETE,
                $type,
                $id,
                $descName . '-' . $remarkContent,
                ActiveLog::SYSTEM_CNA_ADMIN
            );

            return responseSuccess();
        } else {
            return responseFail(__('delete failed'));
        }
    }
}
