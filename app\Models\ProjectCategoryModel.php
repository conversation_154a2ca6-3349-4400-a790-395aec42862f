<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProjectCategoryModel extends Model
{
    use HasFactory;

    protected $table = 'cna_project_categories';

    public function getRecord($id)
    {
        return self::where('projectCategoriesID', $id)->first();
    }

    public static function groupProjectCategory()
    {
        $result = self::select('projectCategoriesID', 'projectCategoriesNameZH', 'projectCategoriesNameZT',
            'projectCategoriesNameEN', 'projectCategoriesNameMS')->where('projectCategoriesID', 1)->get()->toArray();
        $data = [];
        foreach ($result as $v) {
            $data[$v['projectCategoriesID']] = $v;
            $data[$v['projectCategoriesID']]['projectItem'] = [];
        }

        return $data;
    }
}
