<?php

namespace App\Http\Controllers\Admin\V1;

use App\Http\Controllers\Controller;
use App\Services\CurrencyServices;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class CurrencyRatesController extends Controller
{
    protected $currencyService;

    public function __construct(CurrencyServices $currencyService)
    {
        $this->currencyService = $currencyService;
    }

    /**
     * 获取货币列表
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request)
    {
        $params = $request->validate([
            'page_size' => 'nullable|integer',
            'page' => 'nullable|integer',
            'is_default' => 'nullable|integer|in:1,2',
            'rate' => 'nullable|numeric'
        ]);
        $list = $this->currencyService->getList($params);
        $items = $list->items();
        $paginate = [
            'page_size' => $params->page_size ?? 10,
            'total' => $list->total(),
            'total_page' => $list->lastPage(),
        ];
        return responseSuccess(compact('items', 'paginate'));
    }

    /**
     * 新增货币
     * @param Request $request
     * @return JsonResponse
     */
    public function create(Request $request)
    {
        $data = $request->validate([
            'currency' => 'required|string',
            'currency_code' => 'required|string|max:10',
            'rate' => 'required|numeric'
        ]);

        $result = $this->currencyService->save($data);
        
        return responseSuccess();
    }

    /**
     * 更新货币信息
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function update(Request $request, int $id)
    {
        $param = $request->validate([
            'currency' => 'sometimes|string',
            'currency_code' => 'sometimes|string|max:10',
            'rate' => 'sometimes|numeric',
            'is_default' => 'sometimes|integer|in:0,1'
        ]);
        $this->currencyService->save($param, $id);
        
        return responseSuccess();
    }

    /**
     * 获取单个货币信息
     * @param int $id
     * @return JsonResponse
     */
    public function show(int $id)
    {
        $result = $this->currencyService->getInfo($id);
        return responseSuccess($result);
    }
} 