<?php

namespace App\Http\Controllers\Api\v1\User;

use App\Http\Controllers\Controller;
use App\Models\Experience;
use App\Models\ExperienceData;
use App\Rules\Register\NationalityRule;
use App\Services\DocService;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class ExperienceController extends Controller
{
    const ACTION = 'experience_doc'; //场景

    public function title(Request $request)
    {
        $type = $request->get('type');
        if (!in_array($type, [ExperienceData::TYPE_CREDENTIALS, ExperienceData::TYPE_SKILL])) {
            return responseFail();
        }
        $data = Experience::where('type', $type)->get();
        return responseSuccess($data);
    }

    public function list(Request $request)
    {
        $body = $request->validate([
            'type' => ['required', Rule::in(ExperienceData::TYPE_ARR)],
            'profileID' => ['required', 'integer', 'min:1'],
        ]);
        $data = ExperienceData::where('type', $body['type'])->where('profile_id', $body['profileID'])
            ->orderBy('order', 'asc')->get();
        foreach ($data as &$item) {
            $item['file'] = $item['file'] ? storageUrl($item['file']) : '';
        }
        return responseSuccess($data);
    }

    public function edit(Request $request)
    {
        $body = $request->validate([
            'id' => ['integer', 'min:1'],
            'type' => ['required', Rule::in(ExperienceData::TYPE_ARR)],
            'experience_id' => ['integer', 'min:1'],
            'experience_name' => ['string'],
            'desc' => ['required'],
            'country_id' => ['required', new NationalityRule],
            'time_range' => ['required'],
            'industry_id' => ['required', 'min:1'],
            'file' => ['file', 'max:'.(env('ALLOW_FILE_SIZE')*1024)],
            'order' => ['integer', 'min:0'],
        ]);
        $pattern = '/^(19|20)\d{2}$/';
        $currentYear = date('Y');
        $timeRange = explode('-', $body['time_range']);
        if (count($timeRange) != 2 || !preg_match($pattern, $timeRange[0]) || !preg_match($pattern, $timeRange[1])
            || $timeRange[0] > $currentYear || $timeRange[1] > $currentYear || $timeRange[0] > $timeRange[1]) {
                return responseFail(__('param error', ['param' => 'time_range']));
        }
        if ($body['type'] == ExperienceData::TYPE_CREDENTIALS || $body['type'] == ExperienceData::TYPE_SKILL) {
            if (empty($body['experience_id'])) {
                return responseFail(__('param error', ['param' => 'experience_id']));
            }
            $experienceTitle = Experience::find($body['experience_id']);
            if (empty($experienceTitle) || $experienceTitle['type'] != $body['type']) {
                return responseFail(__('param error', ['param' => 'experience_id']));
            }
            unset($body['experience_name']);
        } else if ($body['type'] == ExperienceData::TYPE_CLIENT) {
            if (!isset($body['experience_name'])) {
                return responseFail(__('param error', ['param' => 'experience_name']));
            }
            unset($body['experience_id']);
        }
        $file = $request->file('file');
        if ($file) {
            $filePath = $file->store(ExperienceData::FILE_PATH, 'public');
            $body['file'] = $filePath;
        } else {
            unset($body['file']);
        }
        if (isset($body['id'])) {
            ExperienceData::where('id', $body['id'])->where('type', $body['type'])->update($body);
        } else {
            $user = $request->attributes->get('user');
            $body['profile_id'] = $user['profileID'];
            ExperienceData::create($body);
        }
        return responseSuccess();
    }

    public function del(Request $request)
    {
        $body = $request->validate([
            'id' => ['required', 'integer', 'min:1'],
        ]);
        $user = $request->attributes->get('user');
        ExperienceData::where('id', $body['id'])->where('profile_id', $user['profileID'])->delete();

        return responseSuccess();
    }

    public function generateDocToken(Request $request)
    {
        $id = $request->input('id', 0);
        $path = ExperienceData::where('id', $id)->value('file');
        if ($path === '') {
            return responseFail();
        }
        $token = DocService::generateToken($path, self::ACTION, 3600);

        return responseSuccess($token);
    }

    public function previewDoc(Request $request)
    {
        $token = $request->route('token');

        return DocService::preview($token, self::ACTION);
    }
}
