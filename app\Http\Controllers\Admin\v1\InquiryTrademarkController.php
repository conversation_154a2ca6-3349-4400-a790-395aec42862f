<?php
namespace App\Http\Controllers\Admin\v1;

use App\Http\Controllers\Controller;
use App\Models\ActiveLog;
use App\Models\CompanyModel;
use App\Models\InquiryReportModel;
use App\Models\InquiryTrademarkModel;
use App\Services\OssService;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Symfony\Component\HttpFoundation\Request;

class InquiryTrademarkController extends Controller
{
    /**
     * 添加商标信息
     * @param Request $request
     * @return void
     */
    public function store(Request $request)
    {
        $param = $request->all();

        // 上传商标
        $image_url = '';
        if ($param['imageUrl']) {
            $resource = OssService::uploadWebFile($param['imageUrl']);
            $image_url = $resource ?? '';
        }

        if (empty($param['applicantChinese'])) {
            return responseSuccess();
        }

        $result = InquiryTrademarkModel::query()->create([
            'registration_number'      => $param['registrationNumber'],
            'application_date'         => $param['applicationDate'],
            'applicant_chinese'        => $param['applicantChinese'],
            'similar_groups'           => $param['similarGroups'],
            'validity_period'          => $param['validityPeriod'],
            'image_url'                => $image_url,
            'service_content'          => $param['serviceContent'],
        ]);

        if ($result) {
            return responseSuccess();
        } else {
            return responseFail(__('save failed'));
        }

    }
}