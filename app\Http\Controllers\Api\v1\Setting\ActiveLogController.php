<?php

namespace App\Http\Controllers\Api\v1\Setting;

use App\Http\Controllers\Controller;
use App\Models\ActiveLog;
use Illuminate\Http\Request;

class ActiveLogController extends Controller
{
    public function list(Request $request)
    {
        $user = $request->attributes->get('user');
        $pageSize = $request->post('pageSize', 10);
        $data = ActiveLog::select('id', 'active_type', 'obj_type', 'created_at')
            ->where('system', ActiveLog::SYSTEM_CNA)->where('profile_id', $user['profileID'])
            ->orderByDesc('id')->paginate($pageSize);
        $objTypeMap = config('operatetype');
        $list = collect($data->items())->map(function ($item) use ($objTypeMap) {
            $descArr = $objTypeMap[$item['obj_type']];
            $item['descZH'] = $descArr['descZH'];
            $item['descZT'] = $descArr['descZT'];
            $item['descEN'] = $descArr['descEN'];
            $item['descMS'] = $descArr['descMS'];
            $item['create_time'] = strtotime($item['created_at']);
            return $item;
        });
        $currentPage = $data->currentPage();
        $perPage = $data->perPage();
        $totalRecord = $data->total();
        $totalPage = $data->lastPage();
        $paginate = [
            'currentPage' => $currentPage,
            'perPage' => $perPage,
            'totalRecord' => $totalRecord,
            'totalPage' => $totalPage
        ];
        $compact = compact('list', 'paginate');

        return responseSuccess($compact);
    }
}
