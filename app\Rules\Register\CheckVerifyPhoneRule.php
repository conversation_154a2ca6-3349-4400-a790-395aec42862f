<?php

namespace App\Rules\Register;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use App\Models\ProfileInfoModel;

class CheckVerifyPhoneRule implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $res = ProfileInfoModel::where('profileContact', $value)->exists();
        if ($res) {
            $fail('请填写手机号');
        }
    }
}
