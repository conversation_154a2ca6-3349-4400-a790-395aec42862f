<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cna_admin_review_profile_status', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('profile_id')->default(0)->comment('合伙人ProfileInfo ID');
            $table->boolean('info_status')->default(false)->comment('检查合伙人信息状态 0:未处理 1:已处理');
            $table->boolean('register_payment_status')->default(false)->comment('检查合伙人支付状态 0:未处理 1:已处理');
            $table->timestamps();
        });

        DB::statement("ALTER TABLE `cna_admin_review_profile_status` comment '行政审核合伙人信息审核状态表'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cna_admin_review_profile_status');
    }
};
