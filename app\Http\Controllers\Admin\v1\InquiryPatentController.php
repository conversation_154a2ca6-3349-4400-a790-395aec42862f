<?php
namespace App\Http\Controllers\Admin\v1;

use App\Http\Controllers\Controller;
use App\Models\ActiveLog;
use App\Models\CompanyModel;
use App\Models\InquiryCopyrightModel;
use App\Models\InquiryPatentModel;
use App\Models\InquiryReportModel;
use Illuminate\Support\Facades\Validator;
use Symfony\Component\HttpFoundation\Request;

class InquiryPatentController extends Controller
{
    /**
     * 添加专利
     * @param Request $request
     * @return void
     */
    public function store(Request $request)
    {
        $param = $request->all();

        $patents = $param['patents'];
        if ($patents) {
            //$patents = json_decode($patents, true);
            $arrData = [];
            foreach ($patents as $val) {
                $arrData[] = [
                    'publication_number'    => $val['publicationNumber'],
                    'application_number'    => $val['applicationNumber'],
                    'application_date'      => $val['applicationDate'],
                    'title'                 => $val['title'],
                    'invention_name'        => $val['inventionName'],
                    'applicant'             => $val['applicant'],
                    'legal_status'          => json_encode($val['legalStatus']),
                ];
            }
            InquiryPatentModel::insert($arrData);
            return responseSuccess();
        }
      

        return responseFail(__('save failed'));
    }
}