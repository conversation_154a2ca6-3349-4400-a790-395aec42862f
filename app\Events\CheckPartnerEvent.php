<?php

namespace App\Events;

use App\Models\ProfileInfoModel;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class CheckPartnerEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $profileInfo;
    public $status;
    /**
     * Create a new event instance.
     */
    public function __construct(ProfileInfoModel $profileInfo, $status)
    {
        //
        $this->profileInfo = $profileInfo;
        $this->status = $status;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('channel-name'),
        ];
    }
}
