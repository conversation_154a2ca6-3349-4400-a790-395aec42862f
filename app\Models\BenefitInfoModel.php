<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class BenefitInfoModel extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $table = 'cna_benefit_info';
    protected $guarded = [];
    protected $primaryKey = 'benefitID';

    const CREATED_AT = 'createTime';
    const UPDATED_AT = 'editTime';

    public function createUserInfo()
    {
        return $this->belongsTo(ProfileInfoModel::class, 'createUser', 'profileID')->select('profileID', 'profileName');
    }

    public function editUserInfo()
    {
        return $this->belongsTo(ProfileInfoModel::class, 'editUser', 'profileID')->select('profileID', 'profileName');
    }
}
