<?php

namespace App\Http\Controllers\Api\v1\ProfileUpgrade;

use App\Http\Controllers\Controller;
use App\Models\ProfileBusinessProcessModel;
use App\Models\ProjectServiceDataModel;
use App\Services\ProfileBusinessProcessServices;
use App\Services\ProfileUpgradeQuestionsServices;
use App\Services\ProjectServiceDataServices;
use Illuminate\Http\Request;

class ProfileUpgrade extends Controller
{
    public function __construct(ProfileUpgradeQuestionsServices $profileUpgradeQuestionsServices)
    {
        $this->services = $profileUpgradeQuestionsServices;
    }

    public function examQuestions(Request $request)
    {
        $param = $request->validate([
            'page' => 'bail|nullable|integer',
            'page_size' => 'required|integer',
            'get_type' => 'bail|nullable|string',
        ]);
        return responseSuccess($this->services->getList($param));
    }

    public function submitExam(Request $request,ProfileBusinessProcessServices $profileBusinessProcessServices)
    {
        $user = $request->attributes->get('user');
        //更新业务状态
        $process = $profileBusinessProcessServices->getByProfileID($user->profileID,'profile_upgrade_partner_manager');
        if(empty($process)){
            return responseFail('未参与当前业务');
        }
        //更新业务状态
        $res = $profileBusinessProcessServices->finished($process);
        //更新用户身份
        return responseSuccess();
    }

    public function getMenu(ProjectServiceDataServices $projectServiceDataServices){
        $upgradeProjectCode = ProjectServiceDataModel::UPGRADE_PROJECT_CODE;
        $orderBy = "FIELD(code, '".implode("','", $upgradeProjectCode)."')";
        $menu = $projectServiceDataServices->getCodeList(['codeIn'=>$upgradeProjectCode],$orderBy);
        foreach( $menu as &$item ){
            $item['name'] = ProjectServiceDataModel::UPGRADE_PROJECT_NAME[$item['code']];
        }
        return responseSuccess($menu);
    }
}