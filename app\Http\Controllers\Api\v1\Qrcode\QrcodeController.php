<?php

namespace App\Http\Controllers\Api\v1\Qrcode;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class QrcodeController extends Controller
{
    public function make(Request $request)
    {
        $url = $request->post('url');
        if (empty($url)) {
            return responseFail();
        }
        $img = \SimpleSoftwareIO\QrCode\Facades\QrCode::format('png')->size(150)->generate($url);
        $img = 'data:image/png;base64,' . base64_encode($img);

        return responseSuccess(['qrcode' => $img]);
    }
}
