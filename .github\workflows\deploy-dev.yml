name: Rsync Deploy

on:
  push:
    branches:
      - rewrite-in-laravel
jobs:
  rsync-deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v2

      - name: Install rsync and sshpass
        run: |
          sudo apt-get update
          sudo apt-get install -y rsync sshpass

      - name: Cu Cloud Deploy
        run: |
           rsync -avz --exclude='.env' --exclude='.git' --exclude='.github' -e "sshpass -p 3LdeAuK7xG5oqNBdAazA ssh -o StrictHostKeyChecking=no" . corporate-advisory-dev-api@*************:/home/<USER>/htdocs/dev.api.corporate-advisory.cn
