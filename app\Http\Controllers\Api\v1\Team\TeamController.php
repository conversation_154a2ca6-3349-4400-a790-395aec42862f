<?php

namespace App\Http\Controllers\Api\v1\Team;

use App\Http\Controllers\Controller;
use App\Models\DocumentInfoModel;
use App\Models\EmailTemplateModel;
use App\Models\Firm;
use App\Models\ProfileInfoModel;
use App\Models\TeamInvite;
use App\Models\TeamProfile;
use App\Models\TeamRank;
use App\Rules\Mobile\CheckMobileRule;
use App\Rules\Register\CheckVerifyEmailRule;
use App\Rules\Register\CheckVerifyPhoneRule;
use App\Services\DocService;
use App\Services\EmailService;
use App\Services\SmsService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class TeamController extends Controller
{
    const DOC_CNA = 1; //文档，C&A简介 
    const DOC_CN_PARTNER = 2; //文档，中国合伙人加盟手册
    const DOC_FEE = 3; //文档，收费指南
    const DOC_TEAM = 4; //文档，组建合伙人团队
    const DOC_GREENSMART = 5; //文档，绿智地球业务
    const DOC_CN_SIGN = 6; //文档，服务合同
    const DOC_ARR = [
        self::DOC_CNA,
        self::DOC_CN_PARTNER,
        self::DOC_FEE,
        self::DOC_TEAM,
        self::DOC_GREENSMART,
        self::DOC_CN_SIGN,
    ];
    const ACTION = 'team_doc'; //场景
    const CNA_COMPANY_NAME = 'C&A 中国总公司';
    //邮件PPT下载地址 todo
    const PPT_LINK = [
        '[cna_ppt]' => 'https://api.corporate-advisory.cn/api/v1/team/downloadDoc/eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3MzI1MjIyMjUsImFjdGlvbiI6InRlYW1fZG9jIiwiZmlsZSI6ImZpbGVzL2hlbHAvcHJpdmFjeS5kb2N4In0.FdMxgXrLqvYph0pOKWeEHuXzyEG8VLg5UkD-yIGpLjU', //C&A简介
        '[partner_ppt]' => 'https://api.corporate-advisory.cn/api/v1/team/downloadDoc/eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3MzI1MjIyMjUsImFjdGlvbiI6InRlYW1fZG9jIiwiZmlsZSI6ImZpbGVzL2hlbHAvcHJpdmFjeS5kb2N4In0.FdMxgXrLqvYph0pOKWeEHuXzyEG8VLg5UkD-yIGpLjU', //联盟合伙人
        '[green_ppt]' => 'https://api.corporate-advisory.cn/api/v1/team/downloadDoc/eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3MzI1MjIyMjUsImFjdGlvbiI6InRlYW1fZG9jIiwiZmlsZSI6ImZpbGVzL2hlbHAvcHJpdmFjeS5kb2N4In0.FdMxgXrLqvYph0pOKWeEHuXzyEG8VLg5UkD-yIGpLjU', //绿智地球业务
    ];
    const INVITE_TYPE_TEAM = 1; //三三制邀请
    const INVITE_TYPE_NORMAL = 2; //普通合伙人邀请
    const INVITE_TYPE_FIRM = 3; //联号事务所邀请
    private $emailService;
    private $smsService;

    public function __construct(EmailService $emailService, SmsService $smsService)
    {
        $this->emailService = $emailService;
        $this->smsService = $smsService;
    }

    //校验推荐人手机号
    public function checkPhone(Request $request)
    {
        $phone = $request->post('phone');
        if (empty($phone)) {
            return responseFail(__('param error', ['param' => 'phone']));
        }
        if ($phone == env('CNA_PHONE')) {
            $rankMap = TeamRank::idMapName();
            $maxRank = count($rankMap);
            $profile = [
                'name' => self::CNA_COMPANY_NAME,
                'rank_id' => $maxRank + 1,
                'rank_name' => '',
                'son_rank_id' => $maxRank,
                'son_rank_name' => $rankMap[$maxRank],
                'grandson_rank_id' => $maxRank - 1,
                'grandson_rank_name' => $rankMap[$maxRank - 1],
            ];
            return responseSuccess($profile);
        }
        $profile = $this->checkRecommend($phone);
        if (is_string($profile)) {
            return responseFail($profile);
        }
        $rankMap = TeamRank::idMapName();
        $profile['rank_id'] = $profile['rank'];
        $profile['rank_name'] = $rankMap[$profile['rank_id']] ?? '';
        //下一级
        $profile['son_rank_id'] = $profile['rank_id'] - 1;
        $profile['son_rank_name'] = $rankMap[$profile['son_rank_id']] ?? '';
        //再下一级
        $profile['grandson_rank_id'] = $profile['son_rank_id'] - 1;
        $profile['grandson_rank_name'] = $rankMap[$profile['grandson_rank_id']] ?? '';

        return responseSuccess($profile);
    }

    //提交加入者资料
    public function add(Request $request)
    {
        $rules = [
            'pre_phone' => ['required'],
            'phone' => ['required', new CheckVerifyPhoneRule],
            'name' => ['required'],
            'email' => ['required', 'email', new CheckVerifyEmailRule],
        ];
        $message = [
            'pre_phone' => __('param error', ['param' => 'pre_phone']),
            'phone' => __('team phone exist'),
            'name' => __('param error', ['param' => 'name']),
            'email' => __('team email exist'),
        ];
        $validator = Validator::make($request->all(), $rules, $message);
        if ($validator->fails()) {
            $errors = $validator->errors()->all();
            return responseFail($errors[0]);
        }
        $body = $request->all();
        //检查加入者
        //手机号/邮箱是否存在
        $new = TeamProfile::where('phone', $body['phone'])->orWhere('email', $body['email'])->first();
        if ($new) {
            return responseFail(__('team phone/email exist'));
        }
        //推荐人是公司电话，建一个总司令
        if ($body['pre_phone'] == env('CNA_PHONE')) {
            $rankMap = TeamRank::idMapName();
            $maxRank = count($rankMap);
            $insert = [
                'rank' => $maxRank,
                'name' => $body['name'],
                'phone' => $body['phone'],
                'email' => $body['email'],
            ];
            $res = TeamProfile::create($insert);
            $res->group = $res->id;
            $res->save();
            return responseSuccess();
        }
        //检查推荐人
        $recommend = $this->checkRecommend($body['pre_phone']);
        if (is_string($recommend)) {
            return responseFail($recommend);
        }
        $insert = [
            'group' => $recommend['group'],
            'pre_id' => $recommend['id'],
            'rank' => $recommend['rank'] - 1,
            'name' => $body['name'],
            'phone' => $body['phone'],
            'email' => $body['email'],
        ];
        TeamProfile::create($insert);
        //发送邮件通知
        $rankRes = TeamRank::where('id', $recommend['rank'] - 1)->first();
        $content = $rankRes['email_body'];
        foreach (self::PPT_LINK as $k => $v) {
            $content = str_replace($k, $v, $content);
        }
        $subject = '恭贺您受推荐申请加入C&A全球合伙人大联盟，成为C&A中国区域联盟合伙人。';
        $this->emailService->sendEmail($body['email'], $subject, $content);

        return responseSuccess();
    }

    //检查推荐人是否符合
    private function checkRecommend($phone)
    {
        //手机号是否存在
        $profile = TeamProfile::where('phone', $phone)->first();
        if (!$profile) {
            return __('team phone error');
        }
        //是否最低级或下级满3人
        if ($profile['rank'] == 1) {
            return __('team son out of limit');
        }
        $underCount = TeamProfile::where('pre_id', $profile['id'])->count();
        if ($underCount >= 3) {
            return __('team son out of limit');
        }
        return $profile;
    }

    public function generateDocToken(Request $request)
    {
        $request->validate([
            'type' => ['required', Rule::in(self::DOC_ARR)],
        ]);
        $body = $request->all();
        if ($body['type'] == self::DOC_TEAM) {
            if (empty($body['profileID'])) {
                return responseFail(__('param error', ['param' => 'profileID']));
            }
            if (str_starts_with($body['profileID'], TeamInvite::CODE_PREFIX)) {
                //被邀请成为最高级的营运总裁
                $rankData = TeamRank::where('id', TeamRank::getTopRank())->first();
            } else {
                if (!is_numeric($body['profileID']) || $body['profileID'] <= 0) {
                    return responseFail();
                }
                $recommendUser = ProfileInfoModel::checkRecommendTeam($body['profileID']);
                if (!$recommendUser || !TeamRank::hasFileUnder($recommendUser)) {
                    return responseFail();
                }
                $rankData = TeamRank::where('id', $recommendUser['team_rank']-1)->first();
            }
            $path = $rankData['file'];
            $name = $rankData['job'] . __('Manual');
            $version = $rankData['file_version'];
        } else {
            $docInfo = DocumentInfoModel::where('documentID', $body['type'])->first();
            $path = $docInfo['documentFile'];
            $name = $docInfo['documentTitle'];
            $version = $docInfo['documentVersion'];
        }//todo 服务合同
        if ($version) {
            $name .= ' ' . $version;
        }
        $extPos = strrpos($path, '.');
        $ext = substr($path, $extPos);
        $token = DocService::generateToken($path, self::ACTION, 3600, $name.$ext);

        return responseSuccess($token);
    }

    public function downloadDoc(Request $request)
    {
        $token = $request->route('token');

        return DocService::download($token, self::ACTION);
    }

    //邀请合伙人获取token
    public function inviteToken(Request $request)
    {
        $user = $request->attributes->get('user');
        $type = $request->post('type', self::INVITE_TYPE_TEAM);
        if ($type == self::INVITE_TYPE_TEAM && $user['team_group']) {
            return responseSuccess(['token' => $user['profileContact']]);
        } else if ($type == self::INVITE_TYPE_NORMAL) {
            return responseSuccess(['token' => $user['profilePartnerCode']]);
        } else if ($type == self::INVITE_TYPE_FIRM && !$user['team_group']) {
            $firmCode = Firm::where('profile_id', $user['profileID'])->value('code');
            if (!empty($firmCode)) {
                return responseSuccess(['token' => $firmCode]);
            }
        }
        return responseFail();
    }

    //邀请合伙人发送邮件短信
    public function inviteSend(Request $request)
    {
        $rules = [
            'url' => ['required'],
            'email' => ['email', new CheckVerifyEmailRule],
            'phone' => [new CheckMobileRule],
        ];
        $message = [
            'url' => __('param error', ['param' => 'url']),
            'email.email' => __('please fill in the correct email'),
        ];
        $validator = Validator::make($request->all(), $rules, $message);
        if ($validator->fails()) {
            $errors = $validator->errors()->all();
            return responseFail($errors[0]);
        }
        $body = $request->all();
        if (empty($body['email']) && empty($body['phone'])) {
            return responseFail();
        }
        $user = $request->attributes->get('user');
        $template = $this->emailService->getTemplateData('', EmailTemplateModel::EVENT_CODE_TEAM_INVITE, $user['profileID']);
        $content = str_replace('[url]', $body['url'], $template['htmlBody']); //替换邀请链接
        $content = str_replace('[from]', $user['profileName'], $content); //替换邀请人姓名
        if (!empty($body['email'])) {
            $this->emailService->sendEmail($body['email'], $template['subject'], $content);
        } else {
            $templateParam = [
                'address' => $body['url'],
                'from' => $user['profileName'],
            ];
            //todo
            $this->smsService->sendSms(env('COUNTRY'), $body['phone'], 0, json_encode($templateParam, JSON_UNESCAPED_UNICODE));
        }
        return responseSuccess();
    }

    //获取推荐人信息
    public function getRecommendInfo(Request $request)
    {
        $body = $request->all();
        if (!empty($body['token'])) {
            $returnKey = ['profileID', 'profileName', 'profilePartnerCode', 'team_group', 'team_rank'];
            if (str_starts_with($body['token'], TeamInvite::CODE_PREFIX) && strlen($body['token']) == 11) {
                //被邀请成为最高级的营运总裁
                
                $code = substr($body['token'], strlen(TeamInvite::CODE_PREFIX));
                $record = TeamInvite::where('invite_code', $code)->where('status', TeamInvite::STATUS_NOT_USED)
                    ->first();
                if ($record) {
                    $user = [
                        'profileID' => $body['token'],
                        'profileName' => self::CNA_COMPANY_NAME,
                        'profilePartnerCode' => 'C&A',
                        'is_team' => 1,
                        'team_file' => 1,
                        'team_rank' => -1, //代表公司邀请
                    ];
                    return responseSuccess($user);
                }
                return responseFail(__('team recommend not found'));
            } else if (ctype_alpha(substr($body['token'], 0, 3))) {
                //被邀请成为普通合伙人/联号事务所
                if (str_starts_with($body['token'], Firm::CODE_PREFIX)) {
                    $profileId = Firm::where('code', $body['token'])->value('profile_id');
                    $user = ProfileInfoModel::select($returnKey)->where('profileID', $profileId)->first();
                    $isFirm = 1;
                } else {
                    $user = ProfileInfoModel::select($returnKey)->where('profilePartnerCode', $body['token'])->first();
                    $isFirm = 0;
                }
                if (empty($user)) {
                    return responseFail(__('team recommend not found'));
                }
                //已经有三三制
                if ($isFirm && $user['team_group']) {
                    $isFirm = 0;
                }
                $user['is_team'] = 0;
                $user['team_file'] = 0;
                $user['is_firm'] = $isFirm;
                return responseSuccess($user);
            } else {
                //被邀请成为三三制
                $user = ProfileInfoModel::select($returnKey)->where('profileContact', $body['token'])->first();
                if (empty($user)) {
                    return responseFail(__('team recommend not found'));
                }
                $user['is_team'] = 0;
                $user['team_file'] = 0;
                if ($user['team_group'] && $recommendUser = ProfileInfoModel::checkRecommendTeam($user['profileID'])) {
                    $user['is_team'] = 1;
                    if ($recommendUser && TeamRank::hasFileUnder($recommendUser)) {
                        $user['team_file'] = 1;
                    }
                }
                return responseSuccess($user);
            }
        } else if (!empty($body['input'])) {
            if (!preg_match('/^[a-zA-Z0-9]+$/', $body['input'])) {
                return responseFail(__('team recommend not found'));
            }
            $user = ProfileInfoModel::select('profileID', 'profileName', 'profilePartnerCode')
                ->where('profileContact', $body['input'])->orWhere('profilePartnerCode', $body['input'])->first();
            if (!$user) {
                return responseFail(__('team recommend not found'));
            }
            $user['is_team'] = 0;
            $user['team_file'] = 0;
            return responseSuccess($user);
        }
    }

    public function list(Request $request)
    {
        $request->validate([
            'page' => ['integer', 'min:1'],
            'pageSize' => ['integer', 'min:1'],
            'is_team' => ['required', 'integer', 'in:0,1'],
        ]);
        $body = $request->all();
        $pageSize = $body['pageSize'] ?? 10;
        $user = $request->attributes->get('user');
        $column = ['profileID', 'profileName', 'profilePartnerCode', 'profileAvatar', 'profileDesc', 'profileNationalityID'];
        $res = ProfileInfoModel::select($column)->where('pre_id', $user['profileID'])
            ->whereIn('status', [ProfileInfoModel::STATUS_VERIFYING, ProfileInfoModel::STATUS_ACTIVE]);
        if ($body['is_team']) {
            $res = $res->where('team_group', '>', 0)->get();
        } else {
            $res = $res->orderByDesc('profileID')->paginate($pageSize);
        }
        foreach ($res as &$item) {
            $item['profileAvatar'] = storageUrl($item['profileAvatar'] ?: ProfileInfoModel::AVATAR_PATH);
            $item['profilePartnerCode'] = $item['profilePartnerCode'] ?: '';
        }
        if ($body['is_team']) {
            return responseSuccess(['data' => $res]);
        }
        return responseSuccess($res);
    }

    //生成邀请最高级的营运总裁的邀请码
    public function inviteTopCode()
    {
        do {
            $code = random_int(10000000, 99999999);
        } while (TeamInvite::where('invite_code', $code)->first());
        TeamInvite::create(['invite_code' => $code]);

        return responseSuccess(['code' => TeamInvite::CODE_PREFIX.$code]);
    }

    public function getTeamRankContent(Request $request)
    {
        $request->validate([
            'rank' => 'required|integer|between:2,11',
        ]);

        $rank = $request->input('rank');

        $teamRanks = TeamRank::select('id', 'job', 'department')->get()->keyBy('id')->toArray();
    
        $job = $teamRanks[$rank]['job'] ?? '';
        $department = $teamRanks[$rank]['department'] ?? '';
        $juniorJob = $teamRanks[$rank - 1]['job'] ?? '';
        $juniorDepartment = $teamRanks[$rank - 1]['department'] ?? '';

        if ($rank == 2) {
            $desc = '“' . $job . '”的 KPI 职务指标是招募和管理至少三位“联盟合伙人”，并协助他们晋级“管理合伙人”组建合伙人小团队。组建成功后，您将有机会从统筹和支援旗下合伙人团队的业务工作中获得“' . $job . '”KPI 绩效营运津贴。（请参照中国区域' . $job . '手册）';
        } else {
            $rankMemberCount = TeamRank::$underRankMembersCount;
            $desc = '“' . $job . '”的 KPI 职务指标是招募和管理三位“' . $juniorJob . '”，并协助他们开办各自的“C&A ' . $juniorDepartment . '”。组建成功后，您将有机会从统筹和支援旗下 ' . $rankMemberCount[$rank] . '+ 位合伙人团队的业务工作中获得“' . $job . '”KPI 绩效营运津贴。（请参照中国区域' . $job . '手册）';
        }

        return responseSuccess([
            'job' => $job,
            'department' => $department,
            'junior_job' => $juniorJob,
            'junior_department' => $juniorDepartment,
            'desc' => $desc,
        ]);
    }
}
