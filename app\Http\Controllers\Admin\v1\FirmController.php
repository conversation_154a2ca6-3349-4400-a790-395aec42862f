<?php

namespace App\Http\Controllers\Admin\v1;

use App\Http\Controllers\Controller;
use App\Models\Firm;
use App\Services\OssService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class FirmController extends Controller
{

    /**
     * 列表页
     * @param Request $request
     * @return null
     */
    public function index(Request $request)
    {
        $user = $request->attributes->get('user');
        $pageSize = $request->get('page_size', 11);
        $page = $request->get("page", 1);
        $keyword = $request->get('keyword'); // 关键字


        $list = Firm::query()->orWhereHas('profileInfo', function ($query) use ($keyword) {
            $query->where('profileName', 'like', '%' . $keyword . '%');
        })->orderBy('created_at', 'desc')->paginate($pageSize);

        $items = collect($list->items())->map(function ($item) {

            $item->profileName  = $item->profileInfo->profileName;
            $item->profileEmail = $item->profileInfo->profileEmail;
            unset($item->profileInfo);
            return $item;
        });

        $paginate = [
            'page_size' => $pageSize,
            'total' => $list->total(),
            'total_page' => $list->lastPage(),
        ];

        return responseSuccess(compact('items', 'paginate'));
    }

    /**
     * 审核资料
     * @return void
     */
    public function check(Request $request)
    {
        $user = $request->attributes->get('user');
        $id = $request->input('id');
        $status = $request->input('status');

        if (empty($id)) {
            return responseFail(__('missing parameter', ['param' => 'id'])); // 参数缺失:id
        }

        if (empty($status)) {
            return responseFail(__('missing parameter', ['param' => 'status'])); // 审核状态不能为空
        }

        if (!in_array($status, [2, 3])) {
            return responseFail(__('param error', ['param' => 'status']));
        }

        // 获取详情
        $info = Firm::find($id);
        if (empty($info)) {
            return responseFail(__('info no exist')); // 信息不存在
        }

        if ($info['status'] == 2 || $info['status'] == 3) {
            return responseFail(__('param error', ['param' => 'status']));
        }

        if ($status == 2) {
            // 通过生成编码
            $info->code = Firm::createCode();
        }

        $info->status = $status;
        $info->save();

        return responseSuccess();
    }

    /**
     * doc.corporate-advisory 上的文件预览
     */
    public function fileResource(Request $request)
    {
        $request->validate([
            'type' => 'required|string|in:local,oss',
            'path' => 'required|string',
        ]);

        if ($request->type == 'local') {
            $fileUrl = viewFileUrl($request->path);
        } else {
            $fileUrl = OssService::link($request->path);
        }

        $fileContent = file_get_contents($fileUrl);

        if (!$fileContent) {
            return responseFail(__('info no exist'));
        }

        // 获取文件MIME类型
        $finfo = new \finfo(FILEINFO_MIME_TYPE);
        $mimeType = $finfo->buffer($fileContent);

        return response()->stream(function () use ($fileContent) {
            echo $fileContent;
        }, 200, ['Content-Type' => $mimeType]);
    }
}
