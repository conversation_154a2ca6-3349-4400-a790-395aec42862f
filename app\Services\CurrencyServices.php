<?php

namespace App\Services;

use App\Models\CurrencyRateLogModel;
use App\Models\CurrencyRatesModel;
use App\Models\CurrencyRateLog;
use Carbon\Carbon;
use App\Exceptions\AuthException;

class CurrencyServices
{
    protected $model;

    public function __construct(CurrencyRatesModel $model)
    {
        $this->model = $model;
    }

    public function getList($params = [])
    {
        return $this->model->select('id','currency','currency_code','is_default')
            ->when(!empty($params['currency']),function ($query) use ($params){
                echo 1;
                $query->where('currency', 'like', '%' . $params['currency'] . '%');
            })->when(!empty($params['currency_code']),function ($query) use ($params){
                echo 2;
                $query->where('currency_code', 'like', '%' . $params['currency_code'] . '%');
            })->when(!empty($params['is_default']),function ($query) use ($params){
                echo 3;
                $query->where('is_default', $params['is_default']);
            })->paginate($params['per_page'] ?? 15);
    }

    public function save($data, $id = null)
    {
        return $this->model->getConnection()->transaction(function () use ($data, $id) {
            // 检查货币名称是否重复（仅当currency字段存在时）
            if (isset($data['currency'])) {
                $currencyQuery = $this->model->where('currency', $data['currency']);
                if ($id) {
                    $currencyQuery->where('id', '!=', $id);
                }
                if ($currencyQuery->exists()) {
                    throw new AuthException('货币已存在');
                }
            }

            if ($id) {
                $currency = $this->model->findOrFail($id);
                $currency->update($data);
            } else {
                $currency = $this->model->create($data);
            }
            if( empty($currency) ){
                throw new AuthException('保存失败');
            }

            // 记录日志
            $this->log($currency->id, $data);

            return $currency;
        });
    }

    public function getInfo($id)
    {
        return $this->model->select('id','currency','currency_code','rate','is_default')->findOrFail($id);
    }

    protected function log($currencyId, $data)
    {
        return CurrencyRateLogModel::create([
            'currency_id' => $currencyId,
            'rate' => $data['rate'] ?? 0,
        ]);
    }
}