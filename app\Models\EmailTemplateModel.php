<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class EmailTemplateModel extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $table = 'cna_email_template';
    protected $guarded = [];
    protected $primaryKey = 'ID';

    const EVENT_CODE_CALENDAR_NOTIFY = 'CalendarNotify'; //日程提醒
    const EVENT_CODE_TEAM_INVITE = 'TeamInvite'; //邀请合伙人
}
