<?php
namespace App\Http\Controllers\Admin\v1;

use App\Exceptions\DefaultException;
use App\Http\Controllers\Controller;
use App\Models\ActiveLog;
use App\Models\DocumentFileDataModel;
use App\Models\DocumentFoldersModel;
use App\Models\DocumentInfoModel;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
// use Symfony\Component\HttpFoundation\Request;
use Illuminate\Http\Request;

class DocumentInfoController extends Controller
{
    private $documentInfoModel;

    public function __construct(DocumentInfoModel $documentInfoModel)
    {
        $this->documentInfoModel = $documentInfoModel;
    }

    /**
     * 资料列表
     * @param Request $request
     * @return void
     */
    public function index(Request $request) {
        $pageSize = $request->get('page_size', 10);
        $keyword = $request->get('keyword'); // 关键字

        // 查询列表
        $list = DocumentInfoModel::query()->when($keyword != '', function ($query) use ($keyword) {
                $query->orWhere('documentTitle', 'like', '%' . $keyword . '%')
                    ->orWhere('documentVersion', 'like', '%' . $keyword . '%')
                    ->orWhere('documentLanguage', 'like', '%' . $keyword . '%');
                })->orderBy('createTime', 'desc')->paginate($pageSize);

        $items = $list->items();

        $paginate = [
            'page_size' => $pageSize,
            'total' => $list->total(),
            'total_page' => $list->lastPage(),
        ];

        return responseSuccess(compact('items', 'paginate'));
    }

    /**
     * 上传资料
     * @param Request $request
     * @return void
     */
    public function store(Request $request) {
        $validator = Validator::make($request->all(), [
            'documentTitle' => 'required',
            'documentLanguage' => 'required',
            'documentFile' => 'required',
            'documentVersion' => 'required',
            'documentValidity' => 'required',  // 有效期
            'folderId'           => 'required'  // 所属文件夹
        ]);
        // 验证失败
        if ($validator->fails()) {
            return responseFail($validator->errors()->all());
        }

        $documentTitle      = $request->documentTitle;
        $documentLanguage   = $request->documentLanguage;
        $documentFile       = $request->file('documentFile');
        $documentValidity   = $request->documentValidity;
        $documentVersion    = $request->documentVersion;
        $folderId           = $request->folderId;
        $order              = $request->order;
        $user = $request->attributes->get('user');

        // 查看文件夹是否存在
        $folderInfo = DocumentFoldersModel::query()->where('id', $folderId)->first();
        if (empty($folderInfo)) {
            return responseFail(__('data no exist', ['param' =>'folders']));// 信息不存在
        }


        $resource = $documentFile->storeAs('images/document', generateUploadFilename($documentFile->getClientOriginalExtension()), 'public');


        try {
            // 开启事务
            DB::beginTransaction();


            // step1 添加资料表
            $result = DocumentInfoModel::query()->create([
                'documentTitle'     => $documentTitle,
                'documentLanguage'  => $documentLanguage,
                'documentValidity'  => $documentValidity,
                'folderId'          => $folderId,
                'order'             => $order,
                'createUser'        => $user['profileID'],
                'createTime'        => date('Y-m-d H:i:s'),
                'createRole'        => $user['profileRole']
            ]);

            // step2 添加版本表
            DocumentFileDataModel::query()->create([
                'documentVersion'   => $documentVersion,
                'documentFile'      => $resource,
                'documentID'        => $result->documentID,
                'isShow'            => 1,
            ]);


            // 提交
            DB::commit();

            // 记录日志
            ActiveLog::log($user['profileID'], ActiveLog::ADMIN_API_V1_CREATE, ActiveLog::ADMIN_API_V1_DOCUMENT,
                $result->documentID, $documentTitle, ActiveLog::SYSTEM_CNA_ADMIN);

            return responseSuccess();

        } catch (\Exception $e) {
            DB::rollBack();
            return responseFail(__('save failed'));
        }

    }

    /**
     * 资料详情
     * @param Request $request
     * @param $id
     * @return void
     */
    public function edit(Request $request, $id)
    {
        if (empty($id)) {
            return responseFail(__('param error', ['param' => 'id']));// 参数缺失:id
        }

        // 获取详情
        $info = DocumentInfoModel::find($id);
        if (empty($info)) {
            return responseFail(__('info no exist'));// 信息不存在
        }

        return responseSuccess($info);
    }

    /**
     * 编辑资料
     * @param Request $request
     * @param $id
     * @return void
     */
    public function update(Request $request, $id){

        $validator = Validator::make($request->all(), [
            'documentTitle' => 'required',
            'documentLanguage' => 'required',
            'documentVersion' => 'required',
           /* 'documentFile' => 'required',*/
            'documentValidity' => 'required',  // 有效期
            'folderId'           => 'required'  // 所属文件夹
        ]);

        // 验证失败
        if ($validator->fails()) {
            return responseFail($validator->errors()->all());
        }

        if (empty($id)) {
            return responseFail(__('param error', ['param' => 'id']));// 参数缺失:id
        }

        // 获取详情
        $info = DocumentInfoModel::find($id);
        if (empty($info)) {
            return responseFail(__('info no exist'));
        }

        $documentTitle      = $request->documentTitle;
        $documentLanguage   = $request->documentLanguage;
        $documentFile       = $request->file('documentFile');;
        $documentValidity   = $request->documentValidity;
        $versionid          = $request->versionid;
        $documentVersion    = $request->documentVersion;
        $folderId           = $request->folderId;
        $order              = $request->order;
        $user = $request->attributes->get('user');

        // 查看文件夹是否存在
        $folderInfo = DocumentFoldersModel::query()->where('id', $folderId)->first();
        if (empty($folderInfo)) {
            return responseFail(__('data no exist', ['param' =>'folders']));// 信息不存在
        }

        if ($documentFile) {
            $resource = $documentFile->storeAs('images/document', generateUploadFilename($documentFile->getClientOriginalExtension()), 'public');
        }


        try {
            // 开启事务
            DB::beginTransaction();

            // step1 更新资料表
            $info->update([
                'documentTitle' => $documentTitle,
                'documentLanguage' => $documentLanguage,
                'documentValidity' => $documentValidity,
                'folderId'        => $folderId,
                'order'           => $order,
                'editUser'        => $user['profileID'],
                'editTime'        => date('Y-m-d H:i:s'),
                'editRole'        => $user['profileRole']
            ]);

            if ($documentFile) {
                DocumentFileDataModel::where('documentID', $id)->update(['isShow' => 0]);
                // step2 新增版本表
                DocumentFileDataModel::query()->create([
                    'documentVersion'   => $documentVersion,
                    'documentFile'      => $resource,
                    'documentID'        => $id,
                    'isShow'            => 1,
                ]);
            }

            // 更新版本
            if ($versionid) {
                DocumentFileDataModel::where('documentID', $id)->update(['isShow' => 0]);
                DocumentFileDataModel::where('documentXID', $versionid)->update(['isShow' => 1]);
            }

            // 提交
            DB::commit();

            // 记录日志
            ActiveLog::log($user['profileID'], ActiveLog::ADMIN_API_V1_EDIT, ActiveLog::ADMIN_API_V1_DOCUMENT,
                $id, $documentTitle, ActiveLog::SYSTEM_CNA_ADMIN);

            return responseSuccess();

        } catch (\Exception $e) {
            DB::rollBack();
            return responseFail(__('edit failed'));
        }

    }

    /**
     * 显示指定版本
     * @param Request $request
     * @param $id
     * @return null
     */
    public function showFile(Request $request, $id)
    {
        $fileId = $request->get('fileId');
        $user = $request->attributes->get('user');

        if (empty($id)) {
            return responseFail(__('param error', ['param' => 'id']));// 参数缺失:id
        }

        // 获取详情
        $info = DocumentInfoModel::find($id);
        if (empty($info)) {
            return responseFail(__('info no exist'));// 信息不存在
        }

        $file = DocumentFileDataModel::where('documentXID', $fileId)->where('documentID', $id)->first();
        if (empty($file)) {
            return responseFail(__('info no exist'));// 信息不存在
        }

        // 更新
        try {
            // 开启事务
            DB::beginTransaction();

            DocumentFileDataModel::where('documentID', $id)->update(['isShow' => 0]);

            DocumentFileDataModel::where('documentXID', $fileId)->update(['isShow' => 1]);

            // 提交
            DB::commit();

            // 记录日志
            ActiveLog::log($user['profileID'], ActiveLog::ADMIN_API_V1_EDIT, ActiveLog::ADMIN_API_V1_DOCUMENT,
                $id, $info['documentTitle'].'-'.$file['documentVersion'], ActiveLog::SYSTEM_CNA_ADMIN);

            return responseSuccess();

        } catch (\Exception $e) {
            DB::rollBack();
            return responseFail(__('edit failed'));
        }

    }

    /**
     * 删除资料
     * @param Request $request
     * @param $id
     * @return void
     */
    public function destroy(Request $request, $id) {
        if (empty($id)) {
            return responseFail(__('param error', ['param' => 'id']));// 参数缺失:id
        }

        // 获取详情
        $info = DocumentInfoModel::find($id);
        if (empty($info)) {
            return responseFail(__('info no exist'));// 账单信息不存在
        }

        $user = $request->attributes->get('user');
        $documentTitle = $info['documentTitle'];

        // 删除
        $result =  $info->delete();
        if ($result !== false) {

            // 记录日志
            ActiveLog::log($user['profileID'], ActiveLog::ADMIN_API_V1_DELETE, ActiveLog::ADMIN_API_V1_DOCUMENT,
                $id, $documentTitle, ActiveLog::SYSTEM_CNA_ADMIN);

            return responseSuccess();
        } else {
            return responseFail(__('delete failed'));
        }
    }

    /**
     * 获取文件验签
     * @param Request $request
     * @return null
     */
    public function generateDocumentToken(Request $request)
    {
        $documentID = $request->get('documentID');

        if (!$documentID) {
            return responseFail(__('param error', ['param' => 'id']));
        }

        $documentRecord = $this->documentInfoModel->getRecord($documentID);
        if (!$documentRecord) {
            return responseFail(__('info no exist'));
        }

        // 下载最新版本
        $documentFile = DocumentFileDataModel::where('documentID', $documentID)->orderBy('documentXID', 'desc')->value('documentFile');
        $payload = [
            'exp' => time() + 3600,
            'action' => 'document_verify',
            'file' => $documentFile
        ];

        $token = JWT::encode($payload, env('ADMIN_JWT_KEY'), 'HS256');

        return responseSuccess($token);
    }

    public function generateDocumentTokenByID($id)
    {
        $documentDataInfo = DocumentFileDataModel::find($id);
        if (!$documentDataInfo) {
            return responseFail(__('param error', ['param' => 'id']));
        }

        $documentFile = $documentDataInfo->documentFile;
        $payload = [
            'exp' => time() + 3600,
            'action' => 'document_verify',
            'file' => $documentFile,
        ];

        $token = JWT::encode($payload, env('ADMIN_JWT_KEY'), 'HS256');
        return responseSuccess($token);
    }

    /**
     * 预览文件
     * @param Request $request
     * @return never|\Symfony\Component\HttpFoundation\StreamedResponse
     * @throws DefaultException
     */
    public function previewDocument(Request $request)
    {
        $token = $request->route('token');
        $file = $this->decodeToken($token);

        if (!Storage::disk('public')->exists($file)) {
            return abort(404, 'File not found.');
        }

        $mimeType = Storage::disk('public')->mimeType($file);

        return response()->stream(function () use ($file) {
            echo Storage::disk('public')->get($file);
        }, 200, ['Content-Type' => $mimeType]);

    }

    /**
     * 下载文件
     * @param Request $request
     * @return never|\Symfony\Component\HttpFoundation\StreamedResponse
     * @throws DefaultException
     */
    public function downloadDocument(Request $request)
    {
        $token = $request->route('token');
        $file = $this->decodeToken($token);

        if (!Storage::disk('public')->exists($file)) {
            return abort(404, 'File not found.');
        }

        return Storage::disk('public')->download($file);
    }

    private function decodeToken($token)
    {
        try {
            $decodeToken = JWT::decode($token, new Key(env('ADMIN_JWT_KEY'), 'HS256'));
            if (!isset($decodeToken->action) || !isset($decodeToken->file)) {
                throw new DefaultException('非法操作');
            }
            if ($decodeToken->action != 'document_verify') {
                throw new DefaultException('非法操作');
            }
            return $decodeToken->file;
        } catch (\Exception $e) {
            throw new DefaultException('非法操作');
        }

    }
}