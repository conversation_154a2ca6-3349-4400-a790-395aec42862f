<?php
namespace App\Console\Commands;

use App\Models\ProfileInfoModel;
use App\Models\WechatOrderModel;
use Illuminate\Console\Command;

class WechatCheckOrder extends Command
{
    const NOTIFY_EVENT_CODE = 'WechatCheckOrder';
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'wechat_check_order';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '查询微信订单';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->register(); //测试环境注册检查订单是否支付
    }

    /**
     * Execute console command.
     *
     * @return mixed
     */
    public function register()
    {
        if (env('APP_ENV') == 'product') {
            return;
        }
        $todayTime = strtotime('today');
        $startDate = date('Y-m-d 00:00:00', $todayTime - 86400);
        $users = ProfileInfoModel::where('status', ProfileInfoModel::STATUS_PAYING)
            ->where('created_at', '>', $startDate)
            ->pluck('profileID');
        if (empty($users)) {
            return;
        }
        foreach ($users as $profileId) {
            WechatOrderModel::checkRegisterOrder($profileId);
        }
    }
}

