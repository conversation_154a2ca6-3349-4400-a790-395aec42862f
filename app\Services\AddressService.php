<?php

namespace App\Services;

use App\Models\DivisionCn;

class AddressService {
    
    //获取中国所有省市区
    public static function getDivisionCn()
    {
        $data = DivisionCn::where('status', 1)->get()->toArray();
        $state = $city = $district = [];
        foreach ($data as $item) {
            if ($item['type'] == DivisionCn::TYPE_STATE) {
                $item['children'] = [];
                $state[$item['id']] = $item;
            } else if ($item['type'] == DivisionCn::TYPE_CITY) {
                $item['children'] = [];
                $city[$item['id']] = $item;
            } else if ($item['type'] == DivisionCn::TYPE_DISTRICT) {
                $district[$item['id']] = $item;
            }
        }
        foreach ($district as $item) {
            if (isset($city[$item['pre_id']])) {
                $city[$item['pre_id']]['children'][] = [
                    'id' => $item['id'],
                    'name' => $item['name'],
                ];
            }
        }
        foreach ($city as $item) {
            if (isset($state[$item['pre_id']])) {
                $state[$item['pre_id']]['children'][] = [
                    'id' => $item['id'],
                    'name' => $item['name'],
                    'children' => $item['children'],
                ];
            }
        }
        return array_values($state);
    }
}