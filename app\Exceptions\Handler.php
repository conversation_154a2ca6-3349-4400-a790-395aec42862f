<?php

namespace App\Exceptions;

use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Validation\ValidationException;
use Throwable;

class Handler extends ExceptionHandler
{
    /**
     * The list of the inputs that are never flashed to the session on validation exceptions.
     *
     * @var array<int, string>
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     */
    public function register(): void
    {
        $this->reportable(function (Throwable $e) {
            //
        });
    }

    // public function render($request, Throwable $e)
    // {
    //     return responseFail('Line:'.$e->getLine().', Message:'.$e->getMessage());
    // }

    public function render($request, Throwable $exception)
    {
        // 统一处理 ValidationException 异常 422
        if ($exception instanceof ValidationException) {
            $errors = $exception->errors();
            $firstField = array_key_first($errors); // 获取第一个字段名
            return responseFail($errors[$firstField][0]);
        }

        return parent::render($request, $exception);
    }
}
