<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ProfileUpgradeQuestionsModel extends Model
{
    use SoftDeletes;
    protected $table = 'cna_profile_upgrade_questions';

    const TYPE_RADIO = 0;
    const TYPE_CHECKBOX = 1;

    const TYPE_ALL = [self::TYPE_RADIO, self::TYPE_CHECKBOX];

    const TYPE_REMARK = [
        self::TYPE_RADIO => '单选题',
        self::TYPE_CHECKBOX => '多选题',
    ];

    protected $fillable = ['type', 'question', 'option'];
}
