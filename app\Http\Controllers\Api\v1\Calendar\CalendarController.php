<?php

namespace App\Http\Controllers\Api\v1\Calendar;

use App\Http\Controllers\Controller;
use App\Models\ActiveLog;
use Illuminate\Http\Request;
use App\Models\Calendar;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class CalendarController extends Controller
{
    public function list(Request $request)
    {
        $rules = [
            'start_time' => 'integer|min:1',
            'end_time' => 'integer|min:1',
        ];
        $message = [
            'start_time' => __('param error', ['param' => 'start_time']),
            'end_time' => __('param error', ['param' => 'end_time']),
        ];
        $validator = Validator::make($request->all(), $rules, $message);
        if ($validator->fails()) {
            $errors = $validator->errors()->all();
            return responseFail($errors[0]);
        }
        $validated = $validator->validated();
        //开始和结束时间为空，默认赋值本月开始结束
        if (empty($validated['start_time']) && empty($validated['end_time'])) {
            $validated['start_time'] = strtotime('first day of this month 00:00:00');
            $validated['end_time'] = strtotime('last day of this month 23:59:59');
        }
        if (empty($validated['start_time']) || empty($validated['end_time'])
            || $validated['start_time'] >= $validated['end_time']) {
            return responseFail(__('param error', ['param' => 'start_time or end_time']));
        }
        $user = $request->attributes->get('user');
        $res = Calendar::where('profile_id', $user['profileID'])
            ->whereBetween('start_time', [$validated['start_time'], $validated['end_time']])
            ->orderBy('start_time', 'asc')
            ->get();

        return responseSuccess($res);
    }

    public function add(Request $request)
    {
        $rules = [
            'id' => ['integer', 'min:1'],
            'title' => 'required|string',
            'desc' => ['string'],
            'start_time' => 'required|integer|min:1',
            'end_time' => 'required|integer|min:1',
            'notify_type' => ['required', 'integer', Rule::in(Calendar::NOTIFY_TYPE_ARR)]
        ];
        $message = [
            'id' => __('param error', ['param' => 'id']),
            'title' => __('param error', ['param' => 'title']),
            'desc' => __('param error', ['param' => 'desc']),
            'start_time' => __('param error', ['param' => 'start_time']),
            'end_time' => __('param error', ['param' => 'end_time']),
            'notify_type' => __('param error', ['param' => 'notify_type']),
        ];
        $validator = Validator::make($request->all(), $rules, $message);
        if ($validator->fails()) {
            $errors = $validator->errors()->all();
            return responseFail($errors[0]);
        }
        $validated = $validator->validated();
        $user = $request->attributes->get('user');
        if (isset($validated['id'])) {
            Calendar::where('id', $validated['id'])->update($validated);
            $recordId = $validated['id'];
        } else {
            $validated['profile_id'] = $user['profileID'];
            $res = Calendar::create($validated);
            $recordId = $res->id;
        }
        //记录活动日志
        ActiveLog::log($user['profileID'], ActiveLog::ACTIVE_CREATE, ActiveLog::API_V1_CALENDAR_ADD, $recordId, $validated);
        
        return responseSuccess(['id' => $recordId]);
    }

    public function del(Request $request)
    {
        $id = $request->post('id');
        if (!$id || !is_numeric($id) || $id <= 0) {
            return responseFail();
        }
        $user = $request->attributes->get('user');
        Calendar::where('id', $id)->where('profile_id', $user['profileID'])->delete();

        return responseSuccess();
    }
}
