<?php

namespace App\Http\Controllers\Api\v1\Setting;

use App\Http\Controllers\Controller;
use App\Models\ActiveLog;
use App\Models\Setting;
use Illuminate\Http\Request;

class NotificationController extends Controller
{
    public function notifyType()
    {
        $return = [];
        foreach (Setting::NOTIFY_TYPE as $k => $v) {
            $return[] = [
                'id' => $k,
                'name' => $v,
            ];
        }

        return responseSuccess($return);
    }

    public function update(Request $request)
    {
        $body = $request->validate([
            //'settingNotifyType' => ['required'],
            'settingNotifyEmergency' => ['required', 'in:0,1'],
            'settingNotifySuspiciousOperation' => ['required', 'in:0,1'],
            'settingNotifySafeUpdated' => ['required', 'in:0,1'],
            'settingNotifyRecPrivateMsg' => ['required', 'in:0,1'],
            'settingNotifyImportanceUpdate' => ['required', 'in:0,1'],
            'settingNotifySystemUpdate' => ['required', 'in:0,1'],
            'settingNotifyJoinInvestigate' => ['required', 'in:0,1'],
        ]);
        $user = $request->attributes->get('user');
        $update = [
            'notify_emergency' => $body['settingNotifyEmergency'],
            'notify_suspicious_operation' => $body['settingNotifySuspiciousOperation'],
            'notify_safe_updated' => $body['settingNotifySafeUpdated'],
            'notify_rec_private_msg' => $body['settingNotifyRecPrivateMsg'],
            'notify_importance_update' => $body['settingNotifyImportanceUpdate'],
            'notify_system_update' => $body['settingNotifySystemUpdate'],
            'notify_join_investigate' => $body['settingNotifyJoinInvestigate'],
        ];
        $res = Setting::where('profile_id', $user['profileID'])->update($update);
        if ($res) {
            //记录活动日志
            ActiveLog::log($user['profileID'], ActiveLog::ACTIVE_UPDATE, ActiveLog::API_V1_SETTING_NOTIFICATION_UPDATE,
                $user['profileID'], $update);
            return responseSuccess();
        }
        return responseFail();
    }
}
