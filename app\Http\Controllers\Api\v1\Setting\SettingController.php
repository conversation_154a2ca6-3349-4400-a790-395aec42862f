<?php

namespace App\Http\Controllers\Api\v1\Setting;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\ProfileInfoModel;

class SettingController extends Controller
{
    private $request;
    private $profileInfoModel;

    public function __construct(Request $request, ProfileInfoModel $profileInfoModel)
    {
        $this->request = $request;
        $this->profileInfoModel = $profileInfoModel;
    }

}
