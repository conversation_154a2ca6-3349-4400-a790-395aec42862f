<?php

namespace App\Http\Requests\Register;

use App\Rules\Register\NationalityRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use App\Exceptions\DefaultException;
use App\Rules\Mobile\CheckMobilePrefixRule;
use App\Rules\Mobile\CheckMobileRule;
use App\Rules\Register\RegConfirmEmailRule;
use App\Rules\Register\RegConfirmPhoneRule;
use App\Rules\User\CheckBirthDateRule;
use App\Rules\User\CheckCountryRule;
use App\Rules\User\CheckNricRule;

class RegisterConfirm extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name'            => ['required'],
            //'idNumber'        => ['required', new CheckNricRule],
            'gender'          => ['required', 'in:M,F'],
            'birthDate'       => ['required', new CheckBirthDateRule],
            'nationalityID'   => ['required', new NationalityRule],
            'prefixID'        => ['required', new CheckMobilePrefixRule],
            'phone'           => ['required', new CheckMobileRule, new RegConfirmPhoneRule],
            'email'           => ['required', 'email', new RegConfirmEmailRule],
            'addressUnit'     => ['required'],
            'addressStreet'   => ['required'],
            //'addressDistrict' => ['required'],
            //'addressCity'     => ['required'],
            //'addressState'    => ['required'],
            'addressDistrictId' => ['required', 'integer', 'min:1'],
            'addressCityId'     => ['required', 'integer', 'min:1'],
            'addressStateId'    => ['required', 'integer', 'min:1'],
            //'addressPostcode' => ['required'],
            'addressCountry'  => ['required', new CheckCountryRule],
            'is_team'         => ['integer', 'in:0,1'],
            'profession_id' => ['required', 'integer', 'min:1'],
            'position' => ['required'],
            'work_year' => ['required'],
        ];
    }

    public function messages()
    {
        return [
            'name.required'            => __('please fill in the name'),
            //'idNumber.required'        => __('please fill in the ID number'),
            'gender.required'          => __('please select gender'),
            'birthDate.required'       => __('please fill in the birthdate'),
            'nationalityID.required'   => __('please select country'),
            'prefixID.required'        => __('please select phone area code'),
            'phone.required'           => __('please fill in the phone'),
            'email.required'           => __('please fill in the correct email'),
            'email.email'              => __('please fill in the correct email'),
            'addressUnit.required'     => __('please fill in the unit'),
            'addressStreet.required'   => __('please fill in the street'),
            //'addressDistrict.required' => __('please fill in the district'),
            //'addressCity.required'     => __('please fill in the city'),
            //'addressState.required'    => __('please fill in the state'),
            'addressDistrictId.required' => __('please fill in the district'),
            'addressCityId.required'     => __('please fill in the city'),
            'addressStateId.required'    => __('please fill in the state'),
            //'addressPostcode.required' => __('please fill in the postcode'),
            'addressCountry.required'  => __('please select address - country'),
            'is_team'                  => __('param error', ['param' => 'is_team']),
            'profession_id' => __('param error', ['param' => 'profession_id']),
            'position' => __('param error', ['param' => 'position']),
            'work_year' => __('param error', ['param' => 'work_year']),
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        $error= $validator->errors()->all();
        throw new DefaultException($error[0]);
    }
}
