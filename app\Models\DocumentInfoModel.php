<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class DocumentInfoModel extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $table = 'cna_document_info';
    protected $guarded = [];
    protected $primaryKey = 'documentID';

    protected $appends = [
        'documentVersion',
        'documentFile',
        'createUserName',
        'allFiles'
    ];


    public function getRecord($id)
    {
        return self::where('documentID', $id)->first();
    }

    public function createUserInfo()
    {
        return $this->belongsTo(ProfileInfoModel::class,'createUser');
    }

    public function documentFiles()
    {
        return $this->hasMany(DocumentFileDataModel::class, 'documentID');
    }

    public function getCreateUserNameAttribute()
    {
        return $this->createUserInfo()->value('profileName');
    }

    public function getDocumentFileAttribute()
    {
        $showFile = $this->documentFiles()->where('isShow', 1)->first();
        return $showFile ? $showFile->documentFile : '';
    }

    public function getDocumentVersionAttribute()
    {
        $showFile = $this->documentFiles()->where('isShow', 1)->first();
        if ($showFile) {
            $showFile->documentFile = storageUrl($showFile->documentFile);
        }
        return $showFile ? $showFile->documentVersion : '';
    }

    public function getAllFilesAttribute()
    {
        $files = $this->documentFiles()->get()->toArray();
        if ($files) {
            foreach ($files as $key=>$val) {
                $val['documentFile'] = storageUrl($val['documentFile']);
                $files[$key] = $val;
            }
        }

        return $files;
    }


}
