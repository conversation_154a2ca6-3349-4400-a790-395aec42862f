<?php

namespace App\Http\Controllers\Api\v1\Sms;

use App\Http\Controllers\Controller;
use App\Http\Requests\Register\CommitVerifySms;
use Illuminate\Http\Request;
use App\Http\Requests\Register\VerifyPhone;
use App\Models\CountryModel;
use App\Services\SmsService;

class SmsController extends Controller
{
    private $request;
    private $smsService;

    public function __construct(Request $request, SmsService $smsService)
    {
        $this->request = $request;
        $this->smsService = $smsService;
    }
    
    /**
     * Method 发送验证码
     *
     * @param VerifyPhone $request [explicite description]
     *
     * @return void
     */
    public function sendCode(VerifyPhone $request)
    {
        $body = $request->all();
        $phone = $body['phone'];
        $prefixID = $body['prefixID'];
        $mobilePrefixModel = new CountryModel;

        //整合区号+手机号
        $resetPhoneData = $mobilePrefixModel->resetPhone($prefixID, $phone);
        $resetPhone = $resetPhoneData['phone'];
        $resetCountryCode = $resetPhoneData['countryCode'];
        $user = $request->attributes->get('user');

        return $this->smsService->sendVerificationCodeSms($resetPhone, $body['scene'], $resetCountryCode,
            $user['profileID']);
    }
    
    /**
     * Method 检查验证码
     *
     * @param CommitVerifySms $request [explicite description]
     *
     * @return void
     */
    public function verifyCode(CommitVerifySms $request)
    {
        $body = $request->all();
        $user = $request->attributes->get('user');
        $this->smsService->verifySms($body['phone'], $body['code'], $body['scene'], $user['profileID']);

        return responseSuccess();
    }

}
