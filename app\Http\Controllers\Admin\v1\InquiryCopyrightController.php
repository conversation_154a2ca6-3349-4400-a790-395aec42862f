<?php
namespace App\Http\Controllers\Admin\v1;

use App\Http\Controllers\Controller;
use App\Models\ActiveLog;
use App\Models\CompanyModel;
use App\Models\InquiryCopyrightModel;
use App\Models\InquiryReportModel;
use Illuminate\Support\Facades\Validator;
use Symfony\Component\HttpFoundation\Request;

class InquiryCopyrightController extends Controller
{
    /**
     * 添加版权信息
     * @param Request $request
     * @return void
     */
    public function store(Request $request)
    {
        $param = $request->all();

        $result = InquiryCopyrightModel::query()->create([
            'register_number'    => $param['register_number'],
            'software_name'      => $param['software_name'],
            'author'             => $param['author'],
            'rights'             => $param['rights'],
            'version'            => $param['version'],
            'register_date'      => $param['register_date'],
            'acquisition'        => $param['acquisition'],
        ]);

        if ($result) {
            return responseSuccess();
        } else {
            return responseFail(__('save failed'));
        }
    }
}