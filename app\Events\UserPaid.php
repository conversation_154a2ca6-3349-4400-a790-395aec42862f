<?php

namespace App\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class UserPaid
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $profileId;
    public $amount;

    /**
     * Create a new event instance.
     */
    public function __construct($profileId, $amount)
    {
        $this->profileId = $profileId;
        $this->amount = $amount;
    }
}
