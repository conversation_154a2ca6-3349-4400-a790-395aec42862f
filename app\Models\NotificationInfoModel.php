<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class NotificationInfoModel extends Model
{
    use HasFactory;

    protected $table = 'cna_notification_info';
    protected $guarded = [];
    protected $primaryKey = 'notificationID'; // 自定义主键
    //通知类型
    const EVENT_CODE_SYSTEM = 1; //系统通知
    const EVENT_CODE_PROMOTION = 2; //晋级通知
    const EVENT_CODE_PAY = 3; //出入款通知
    const EVENT_CODE_INFO = 4; //信息公告
    const EVENT_CODE = [
        self::EVENT_CODE_SYSTEM,
        self::EVENT_CODE_PROMOTION,
        self::EVENT_CODE_PAY,
        self::EVENT_CODE_INFO,
    ];

    public static function getNotificationDepartment($type)
    {
        $typename = '';
        switch ($type) {
            case 1:
                $typename = '合伙人';
                break;
            case 2:
                $typename = '行政';
                break;
            case 3:
                $typename = '律师';
                break;
            case 4:
                $typename = '绿智账号';
                break;
            default:
                $typename = '全部';

        }

        return $typename;
    }


    /**
     * 查看用户所有通知
     * @param $user_id
     * @return mixed
     */
    public function allNotice($user)
    {
        $currentDate = date('Y-m-d');
        $all = $this->where(function ($query) use ($currentDate, $user ) {
            $query->orWhere('notificationTarget', $user['profileID'])
                ->orWhere(function ($query) use ($currentDate, $user) {
                    if ($user['profileName'] !=  'admin') {
                        $query->whereIn('notificationDepartment', [$user['profileRole'], 'all'])
                            ->where('notificationValidity', '>=', $currentDate)
                            ->where('createTime', '<=', $currentDate);
                    } else{ //管理员查看所有
                        $query->where('notificationValidity', '>=', $currentDate)
                            ->where('createTime', '<=', $currentDate);
                    }

                });
        })->pluck('notificationID')->toArray();
        //
        return $all;
    }
}
