<?php

namespace App\Rules\Mobile;

use App\Models\CountryModel;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class CheckMobilePrefixRule implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $model = new CountryModel();
        if (!$model->getRecord($value)) {
            $fail('请选择手机区号');
        }
    }
}
