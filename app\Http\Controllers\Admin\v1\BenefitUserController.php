<?php

namespace App\Http\Controllers\Admin\v1;

use App\Http\Controllers\Controller;
use App\Models\ActiveLog;
use App\Models\BenefitDataModel;
use App\Models\NotificationInfoModel;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpFoundation\Request;

class BenefitUserController extends Controller
{
    private $notificationInfoModel;

    public function __construct(NotificationInfoModel $notificationInfoModel)
    {
        $this->notificationInfoModel = $notificationInfoModel;
    }
    /**
     * 申请列表
     * @return void
     */
    public function index(Request $request)
    {
        $pageSize = $request->get('page_size', 10);
        $keyword = $request->get('keyword'); // 关键字
        $profileID = $request->get('profileID');

        $list = BenefitDataModel::query()->with(['benefitInfo', 'profileInfo'])
            ->when($profileID != '', function ($query) use ($profileID) { // 合伙人
                return $query->where('userID', $profileID);
            })
            ->when($keyword != '', function ($query) use ($keyword) {
                $query->orWhere('profileInfo.profileName', 'like', '%' . $keyword . '%')
                    ->orWhere('benefitInfo.benefitTitleEN', 'like', '%' . $keyword . '%')
                    ->orWhere('benefitInfo.benefitTitleMS', 'like', '%' . $keyword . '%')
                    ->orWhere('benefitInfo.benefitTitleZH', 'like', '%' . $keyword . '%')
                    ->orWhere('benefitInfo.benefitTitleZT', 'like', '%' . $keyword . '%');
            })->orderBy('createTime', 'desc')->paginate($pageSize);

        $items = collect($list->items())->map(function ($item) {

            $item->profileName  = $item->profileInfo->profileName;
            $item->profileEmail = $item->profileInfo->profileEmail;
            unset($item->profileInfo);
            return $item;
        });

        $paginate = [
            'page_size' => $pageSize,
            'total' => $list->total(),
            'total_page' => $list->lastPage(),
        ];

        return responseSuccess(compact('items', 'paginate'));
    }

    /**
     * 审核福利
     * @return void
     */
    public function check(Request $request)
    {
        $id = $request->id;
        $status = $request->status;
        $benefitRemark = $request->benefitRemark;
        $user = $request->attributes->get('user');

        if (empty($id)) {
            return responseFail(__('missing parameter', ['param' => 'id']));// 参数缺失:id
        }

        if (empty($status)) {
            return responseFail(__('missing parameter', ['param' => 'status']));// 审核状态不能为空
        }

        if (!in_array($status, [1,2])) {
            return responseFail(__('param error', ['param' => 'status']));// 审核状态不能为空
        }

        // 获取详情
        $info = BenefitDataModel::find($id);
        if (empty($info)) {
            return responseFail(__('info no exist'));// 信息不存在
        }

        // 查看当前状态
        if ($info['benefitStatus'] == 'Approved' || $info['benefitStatus'] == 'Rejected') {
            return responseFail(__('param error', ['param' => 'status']));// 审核状态不能为空
        }

        // 更新状态
        if ($status == 1) {
            $benefitStatus = 'Approved';
        } else if ($status == 2) {
            $benefitStatus = 'Rejected';
        }

        $desc = $info->profileInfo->profileName.'-'.$info->benefitInfo->benefitTitleZH;

        try {
            // 开启事务
            DB::beginTransaction();

            // step1 更改状态
            $info->update([
                'benefitStatus' => $benefitStatus,
                'benefitRemark' => $benefitRemark,
                'editUser' => $user['profileID'],
                'editRole' => $user['profileRole'],
                'editTime' => date('Y-m-d H:i:s'),
            ]);

            // step2 发站内通知
            $data = [
                'targetUser' =>  $info['userID'],
            ];
            if ($status == 1) {
                \App\Jobs\SendNotice::dispatch($data, 'BenefitUserPass', $user)->onQueue('SendNoticeJob');
            } else if ($status == 2){
                \App\Jobs\SendNotice::dispatch($data, 'BenefitUserRefuse', $user)->onQueue('SendNoticeJob');
            }

            // 提交
            DB::commit();

            // 记录日志
            ActiveLog::log($user['profileID'], ActiveLog::ADMIN_API_V1_CHECK, ActiveLog::ADMIN_API_V1_WELFARE,
                $id, $desc, ActiveLog::SYSTEM_CNA_ADMIN);

            return responseSuccess();

        } catch (\Exception $e) {
            DB::rollBack();
            return responseFail(__('edit failed'));
        }


    }
}