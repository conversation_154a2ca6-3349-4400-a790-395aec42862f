<?php

namespace App\Http\Controllers\Admin\v1;

use App\Http\Controllers\Controller;
use App\Models\AiManFile;
use App\Models\CompanyGsp;
use App\Models\CompanyGspReport;
use App\Models\CountryModel;
use App\Models\GspOption;
use App\Services\DocService;
use App\Services\OssService;
use App\Services\ZipService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class GspController extends Controller
{
    const ACTION = 'gsp_form'; //场景

    /**
     * 绿智地球入驻申请列表
     * @param Request $request
     * @return null
     */
    public function index(Request $request)
    {
        $keyword = $request->get('keyword', '');
        $status = $request->get('status', '');
        $pageSize = $request->get('page_size', 10);

        $list = CompanyGsp::with(['profile:profileID,profileName'])
            ->when($keyword != '', function ($q) use ($keyword) {
                $q->whereHas('profile', function ($qu) use ($keyword) {
                    $qu->where('profileName', 'like', '%' . $keyword . '%');
                })->orWhere('name', 'like', '%' . $keyword . '%')
                ->orWhere('contact_name', 'like', '%' . $keyword . '%')
                ->orWhere('phone', $keyword)
                ->orWhere('email', $keyword)
                ->orWhere('credit_code', $keyword);
            })
            ->when($status != '', function ($q) use ($status) {
                $q->where('status', $status);
            })
            ->paginate($pageSize);

        $items = $list->items();

        $paginate = [
            'page_size' => $pageSize,
            'total' => $list->total(),
            'total_page' => $list->lastPage(),
        ];

        return responseSuccess(compact('items', 'paginate'));
    }

    // 生成用户上传预审表格token
    public function generateGspFileToken(Request $request)
    {
        $id = $request->input('id');
        $type = $request->input('type');

        $validator = Validator::make($request->all(), [
            'id' => 'required',
            'type' => 'required',
        ]);

        // 验证失败
        if ($validator->fails()) {
            return responseFail($validator->errors()->all());
        }

        $record = CompanyGsp::find($id);
        if (empty($record)) {
            return responseFail();
        }

        if ($type == 1) { // 用户盖章上传预审表格
            if (empty($record['form_complete'])) return responseFail();
            $file = $record['form_complete'];
        } else if ($type == 2) { // 签约合同
            if (empty($record['contract_url'])) return responseFail();
            $file = $record['contract_url'];
        }

        $token = DocService::generateToken($file, self::ACTION, 3600);

        return responseSuccess($token);
    }

    /**
     * 预览文件
     * @param Request $request
     * @return never|\Symfony\Component\HttpFoundation\StreamedResponse
     */
    public function previewFile(Request $request)
    {
        $token = $request->route('token');

        return DocService::preview($token, self::ACTION);
    }

    /**
     * 下载文件
     * @param Request $request
     * @return never|\Symfony\Component\HttpFoundation\StreamedResponse
     */
    public function downloadForm(Request $request)
    {
        $token = $request->route('token');

        return DocService::download($token, self::ACTION);
    }

    /**
     * 审核预审表格
     * @return void
     */
    public function checkForm(Request $request)
    {
        $id = $request->input('id');
        $status = $request->input('status');
        $form_reject_reason = $request->input('form_reject_reason', '');

        if (empty($id)) {
            return responseFail(__('missing parameter', ['param' => 'id']));
        }

        if (empty($status)) {
            return responseFail(__('missing parameter', ['param' => 'status']));
        }


        // 获取详情
        $info = CompanyGsp::find($id);
        if (empty($info)) { // 信息不存在
            return responseFail(__('info no exist'));
        }

        if ($info['status'] != 1) {
            return responseFail(__('param error', ['param' => 'status']));
        }

        if ($status == 3 && empty($form_reject_reason)) {
            return responseFail(__('missing parameter', ['param' => 'form_reject_reason']));
        }

        // 更新
        $info->status = $status;
        if ($status == 3) {  // 审核驳回填写原因
            $info->form_reject_reason = $form_reject_reason;
            $info->form_complete = '';
        }
        $info->save();

        // todo 通知用户
        return responseSuccess();
    }

    /**
     * 审核合同
     * @return void
     */
    public function checkContract(Request $request)
    {
        $id = $request->input('id');
        $status = $request->input('status');
        $contract_reject_reason = $request->input('contract_reject_reason', '');

        if (empty($id)) {
            return responseFail(__('missing parameter', ['param' => 'id']));
        }

        if (empty($status)) {
            return responseFail(__('missing parameter', ['param' => 'status']));
        }

        // 获取详情
        $info = CompanyGsp::find($id);
        if (empty($info)) { // 信息不存在
            return responseFail(__('info no exist'));
        }

        if (empty($info['contract_url'])) {
            return responseFail(__('missing parameter', ['param' => 'contract file']));
        }

        if ($info['status'] != 4) {
            return responseFail(__('param error', ['param' => 'status']));
        }

        if ($status == 6 && empty($contract_reject_reason)) {
            return responseFail(__('missing parameter', ['param' => 'contract_reject_reason']));
        }

        // 更新
        $info->status = $status;
        if ($status == 6) {  // 审核驳回填写原因
            $info->contract_reject_reason = $contract_reject_reason;
            $info->contract_url = '';
        }
        $info->save();

        // todo 通知用户
        return responseSuccess();
    }


    /**
     * 查看尽调报告
     * @param Request $request
     * @return void
     */
    public function reportDetail(Request $request)
    {
        $gsp_id = $request->input('id');
        if (empty($gsp_id)) {
            return responseFail(__('missing parameter', ['param' => 'id']));
        }

        // 查看尽调报告文件
        $data = CompanyGspReport::where('gsp_id', $gsp_id)->get();
        $result = [];
        if ($data) {
            foreach ($data as $val) {
                $result['file'.$val['file_id']]['file'][] = [
                    'file_path' => $val['file_path'],
                    'file_owner'=> $val['file_owner'],
                    'created_at'=> $val['created_at']
                ];
                $result['file'.$val['file_id']]['status'] = $val['status'];
            }
        }

        return responseSuccess($result);
    }

    // 审核尽调文件
    public function checkReportFile(Request $request)
    {
        $gsp_id = $request->input('id');
        $status = $request->input('status');
        $file_name = $request->input('file_name');
        if (empty($gsp_id)) {
            return responseFail(__('missing parameter', ['param' => 'id']));
        }

        if (!in_array($status, [0,1])) {
            return responseFail(__('missing parameter', ['param' => 'status']));
        }

        if (empty($file_name)) {
            return responseFail(__('missing parameter', ['param' => 'file_name']));
        }

        // 查看尽调报告文件
        $data = CompanyGspReport::where('gsp_id', $gsp_id)->get();
        if (empty($data)) { // 信息不存在
            return responseFail(__('info no exist'));
        }

        $file_id = str_replace('file', '', $file_name);

        CompanyGspReport::where('file_id', $file_id)->where('gsp_id', $gsp_id)->update(['status' => $status]);

        return responseSuccess();

    }

    /**
     * 审核尽调报告
     * @return void
     */
    public function checkReport(Request $request)
    {
        $id = $request->input('id');
        $status = $request->input('status');
        $report_reject_reason = $request->input('report_reject_reason', '');

        if (empty($id)) {
            return responseFail(__('missing parameter', ['param' => 'id']));
        }

        if (empty($status)) {
            return responseFail(__('missing parameter', ['param' => 'status']));
        }

        // 获取详情
        $info = CompanyGsp::find($id);
        if (empty($info)) { // 信息不存在
            return responseFail(__('info no exist'));
        }

        if ($info['status'] != 7) {
            return responseFail(__('param error', ['param' => 'status']));
        }

        if (!in_array($status, [8, 9])) {
            return responseFail(__('param error', ['param' => 'status']));
        }

        if ($status == 9 && empty($report_reject_reason)) {
            return responseFail(__('missing parameter', ['param' => 'report_reject_reason']));
        }

        // 更新
        $info->status = $status;
        if ($status == 9) {  // 审核驳回填写原因
            $info->report_reject_reason = $report_reject_reason;
        }
        $info->save();

        // todo 通知用户
        return responseSuccess();
    }

    /**
     * 打包资料
     * @return void
     */
    public function zipFile(Request $request)
    {
        $user = $request->attributes->get('user');
        $id = $request->input('id');

        if (empty($id)) {
            return responseFail(__('missing parameter', ['param' => 'id'])); // 参数缺失:id
        }

        // 获取详情
        $info = CompanyGspReport::where('gsp_id', $id)->first();
        if (empty($info)) {
            return responseFail(__('info no exist')); // 信息不存在
        }

        // 打包文件
        $files = [];
        if ($info) {
            foreach ($info as $file) {
                $files[] = OssService::link($file['file_path']);
            }
        }

        if ($files) {
            $result = ZipService::createZip($files, $info['profile_id'] . '_GspCompanyReport.zip');
            if ($result) return responseSuccess($result);
        }

        return responseFail();
    }

    /**
     * 下载用户上传的数字人文件
     */
    public function download(Request $request)
    {
        $request->validate([
            'type' => 'required|string|in:local,oss',
            'file_path' => 'required|string',
        ]);

        if ($request->type == 'local') {
            $fileUrl = env('APP_URL') . '/storage/' . $request->file_path; // local 上传文件的basename
        } else {
            $fileUrl = OssService::link($request->file_path);
        }

        // 获取文件名
        $fileName = basename($fileUrl);

        // 读取 OSS 文件
        $client = new \GuzzleHttp\Client();
        $response = $client->get($fileUrl, ['stream' => true]);

        // 流式返回文件内容
        return response()->streamDownload(function () use ($response) {
            $body = $response->getBody();
            while (!$body->eof()) {
                echo $body->read(1024);
            }
        }, $fileName);
    }

    /**
     * 绿智地球申请表选项
     */
    public function selectOptions()
    {
        $gspOptions = GspOption::all();
        // 经济行为
        $economics = $gspOptions->where('type', GspOption::TYPE_ECONOMIC_BEHAVIOR)->select('title', 'id')->values();
        // 行业群体
        $industries = $gspOptions->where('type', GspOption::TYPE_INDUSTRY_GROUP)->select('title', 'id')->values();
        // 目标国家
        $countries = (new CountryModel())->getData();

        return responseSuccess(compact('economics', 'industries', 'countries'));
    }


    // 当前进度
    public function progress(Request $request)
    {
        $id =  $request->get('id');
        $record = CompanyGsp::where('id', $id)->first();

        $progress = $this->checkStep($record);
        return responseSuccess($progress);
    }

    // 判断当前进行中的步骤
    public function checkStep($record)
    {
        $progress = [
            1 => [  // 第一步判断
                'createForm' => 0,  // 生成预审表格
                'uploadForm' => 0,  // 提交预审表格
                'payOne'     => 0,  // 已付款(申请费和预审保证金)
                'commitCheck'=> 0,  // 提交审核
            ],
            2 => [ // 第二步判断
                'formCheck' => 0,  // 预审表审核中
            ],
            3 => [ // 第三步判断
                'printContract'  => 0,  // 打印合同
                'uploadContract' => 0,  // 上传签署合同
                'payTwo'         => 0, // 已付款(企业开户费)
                'nextStep'       => 0 // 下一步
            ],
            4 => [ // 第四步判断
                'contractCheck' => 0,  // 合同审核中
            ],
            5 => [ // 第五步判断
                'postReport' => 0,  // 提交尽调报告
            ],
            6 => [ // 第六步判断
                'reportCheck'  => 0,
            ],
            7 => [ // 第七步判断
                'payThree' => 0, // 已付款(AI账号开通费)
                'success'  => 0
            ],
        ];

        if ($record) {
            // 第一步判断
            if ($record['form']) { // 已生成预审表格
                $progress[1]['createForm'] = 1;
            }

            if ($record['form_complete']) { // 已上传预审表格
                $progress[1]['uploadForm'] = 1;
            }

            if ($record['pay_one'] == 1) { // 已付款第一步
                $progress[1]['payOne'] = 1;
            }

            if ($record['status'] == 1) { // 第一步提交审核
                $progress[1]['commitCheck'] = 1;
            }

            // 第二步判断
            if ($record['status'] == 3) { // 预审不通过
                $progress[2]['formFail'] = 0;
                $progress[2]['formCheck'] = 1;
                // 是否退款
                if ($record['pay_one_return'] == 1) {
                    $progress[2]['payOneReturn'] = 0;
                }
            } else if ($record['status'] == 2) { // 审核通过
                //$progress[2]['formSuccess'] = 0;
                $progress[2]['formCheck'] = 1;
                $progress[1]['commitCheck'] = 1;

            }
            if ($record['status'] > 3)  {
                $progress[2]['formCheck'] = 1;
                $progress[1]['commitCheck'] = 1;
            }

            // 第三步判断
            if ($record['contract_url']) {
                $progress[3]['uploadContract'] = 1; // 上传签署合同
            }

            if ($record['is_print_contract'] == 1) { // 打印合同
                $progress[3]['printContract'] = 1;
            }

            if ($record['pay_two'] == 1) { // 已付款(企业开户费)
                $progress[3]['payTwo'] = 1;
            }

            if ($record['status'] == 4) { // 下一步审核
                $progress[3]['nextStep'] = 1;
            }

            // 第四步判断
            if ($record['status'] == 5) { // 审核通过
                $progress[3]['nextStep'] = 1;
                $progress[4]['contractCheck'] = 1;
                //$progress[4]['contractSuccess'] = 0;
            } else if ($record['status'] == 6) { // 审核失败
                $progress[4]['contractFail'] = 0;
                $progress[4]['contractCheck'] = 1;
            }
            if ($record['status'] > 6)  {
                $progress[4]['contractCheck'] = 1;
                $progress[3]['nextStep'] = 1;
            }

            // 第五步判断
            if ($record['status'] == 7) {
                $progress[5]['postReport'] = 1;
            }

            // 第六步判断
            if ($record['status'] == 8) { // 尽调审核通过
                $progress[6]['reportCheck'] = 1;
                $progress[5]['postReport'] = 1;
            } else if ($record['status'] == 9) { // 尽调审核不通过
                $progress[6]['reportCheck'] = 1;
                $progress[6]['reportFail'] = 0;
            }

            // 第七步
            if ($record['pay_three'] == 1) {
                $progress[7]['payThree'] = 1;
            }
        }

        // 过滤掉已设置步骤
        foreach ($progress as $key=>$val) {
            $progress[$key] = array_keys(array_filter($val, function($value){
                return $value==0;
            }));
            if(empty($progress[$key])) unset($progress[$key]);
        }

        return $progress;
    }
}
