<?php
namespace App\Http\Controllers\Admin\v1;

use App\Http\Controllers\Controller;
use App\Models\ActiveLog;
use App\Models\CompanyModel;
use App\Models\InquiryReportModel;
use Illuminate\Support\Facades\Validator;
use Symfony\Component\HttpFoundation\Request;

class InquiryReportController extends Controller
{

    /**
     * 添加公司基础信息
     * @param Request $request
     * @return void
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'companyName' => 'required',
            'creditCode' => 'required',
            'registeredAddress' => 'required',
            'legalPerson' => 'required',
            'registeredCapital' => 'required',
            'businessScope' => 'required',
            'foundedDate' => 'required',
        ]);

        // 验证失败
        if ($validator->fails()) {
            return responseFail($validator->errors()->all());
        }

        $param = $request->all();

        // 查询reportID
        $company = CompanyModel::query()->where('companyName', $param['companyName'])->first();
        if (empty($company)) {
            return responseFail('公司系统不存在!');
        }

        $result = InquiryReportModel::query()->create([
            'profileID'     => $company['companyProfileID'],
            'companyID'     => $company['companyID'],
            'companyName'   => $param['companyName'],
            'companyNameUsed'   => $param['companyNameUsed'],
            'creditCode'        => $param['creditCode'],
            'registeredAddress' => $param['registeredAddress'],
            'legalPerson'       => $param['legalPerson'],
            'registeredCapital' => $param['registeredCapital'],
            'actualCapital'     => $param['actualCapital'],
            'businessScope'     => $param['businessScope'],
            'companyType'       => $param['companyType'],
            'foundedDate'       => $param['foundedDate'],
            'businessTerm'      => $param['businessTerm'],
            'shareholderContributions' => $param['shareholderContributions'],
            'submitYearly'             => $param['submitYearly'],
        ]);

        if ($result) {
            return responseSuccess();
        } else {
            return responseFail(__('save failed'));
        }

    }
}