<?php

namespace App\Http\Requests\Register;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use App\Exceptions\DefaultException;

class CommitRegSetProfileRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'file' => ['required', 'file', 'mimes:jpg,jpeg,png', 'max:'.(env('ALLOW_FILE_SIZE')*1024)]
        ];
    }

    public function messages()
    {
        return [
            'file.required' => __('incorrect format img'),
            'file.file'     => __('incorrect format img'),
            'file.mimes'    => __('incorrect format img'),
            'file.max'      => __('exceed size img', ['limit' => env('ALLOW_FILE_SIZE').'M'])
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        $error= $validator->errors()->all();
        throw new DefaultException($error[0]);
    }
}
