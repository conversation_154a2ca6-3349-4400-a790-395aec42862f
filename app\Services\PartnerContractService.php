<?php

namespace App\Services;

use Intervention\Image\ImageManagerStatic as Image;

class PartnerContractService
{
    private $publicBase;
    private $moduleBase;
    private $fontColor;
    private $fontSize;
    private $companyNamePosition;
    private $companyRegisterCodePosition;

    const BASE_PATH = 'app/public/files/partnerContract/';
    const RELATIVE_BASE_PATH = 'files/partnerContract/';
    const FONT_COLOR = '#3A3A3A';
    const FONT_SIZE = 24;
    //签名页坐标
    const SIGN_POS_NRIC_NAME = [274, 826];
    const SIGN_POS_NRIC_NUMBER = [274, 861];
    const SIGN_POS_SIGN_NAME = [771, 924];
    //附件一坐标
    const ATACHMENT_POS_DATE = [598, 336];
    const ATTACHMENT_POS_NAME = [598, 438];
    const ATTACHMENT_POS_NRIC_NUMBER = [598, 532];
    const ATTACHMENT_POS_ADDRESS = [598, 623];
    const ATTACHMENT_POS_PHONE = [598, 725];
    const ATTACHMENT_POS_EMAIL = [598, 819];
    const ATTACHMENT_POS_BANK = [598, 907];

    public function __construct()
    {
        $this->publicBase = storage_path('app/public/files/project/progressOne/temp/'); //公共目录
        $this->moduleBase = storage_path('app/public/files/project/progressOne/'); //模块目录
        $this->fontColor  = '#3A3A3A'; //字体颜色
        $this->fontSize   = 24; //字体大小
        $this->companyNamePosition = [148, 910]; //企业名称位置
        $this->companyRegisterCodePosition = [1380, 910]; //企业注册号码位置
    }
    
    public function createSignForm($params)
    {
        $params = [
            'nric_name' => '詹姆斯',
            'nric_number' => '440111199902026789',
            'sign_name' => '詹姆斯',
        ];
        $formImagePath = self::BASE_PATH . 'form.png'; //表格图片
        $fangSongFontPath = self::BASE_PATH . 'fangsong.ttf'; //仿宋字体

        $image = Image::make($formImagePath); // 加载图像
        $image->text($params['nric_name'], self::SIGN_POS_NRIC_NAME[0], self::SIGN_POS_NRIC_NAME[1], function ($font) use ($fangSongFontPath) {
            $font->file($fangSongFontPath);
            $font->size($this->fontSize);
            $font->color($this->fontColor);
        });
        $image->text($params['nric_number'], self::SIGN_POS_NRIC_NUMBER[0], self::SIGN_POS_NRIC_NUMBER[1], function ($font) use ($fangSongFontPath) {
            $font->file($fangSongFontPath);
            $font->size($this->fontSize);
            $font->color($this->fontColor);
        });
        $image->text($params['sign_name'], self::SIGN_POS_SIGN_NAME[0], self::SIGN_POS_SIGN_NAME[1], function ($font) use ($fangSongFontPath) {
            $font->file($fangSongFontPath);
            $font->size($this->fontSize);
            $font->color($this->fontColor);
        });

        //插入选中样式icon
        $formImageTempPath = $this->createImagePath(); //pdf临时图片，转为pdf后删除 

        if ($companyCategory == 1) {
            $position = [155, 990];
        } elseif ($companyCategory == 2) {
            $position = [273, 990];
        } elseif ($companyCategory == 3) {
            $position = [412, 990];
        } elseif ($companyCategory == 4) {
            $position = [550, 990];
        }
        $image->save($formImageTempPath);

        //创建pdf实例
        $pdf = new \FPDF();

        //创建页面
        $pdf->AddPage();

        //设置pdf内容位置
        $pdf->Image($formImageTempPath, 0, 0, 210);

        //下载pdf
        $pdfPath = $this->createPdfPath();
        $pdf->Output('F', $pdfPath['fullPath']); 

        //删除pdf临时图片
        @unlink($formImageTempPath);

        return $pdfPath['subPath'];
    }
    
    /**
     * Method 创建图片资源路径
     *
     * @return void
     */
    private function createImagePath():string
    {
        $filename = md5(uniqid().microtime(true).'images').'.png';
        return self::BASE_PATH.'temp/'.$filename;
    }
    
    /**
     * Method 创建pdf资源路径
     *
     * @return void
     */
    private function createPdfPath()
    {
        $filename = md5(uniqid().microtime(true).'pdf').'.pdf';
        $fullPath = self::BASE_PATH.'download/'.$filename;
        $subPath = self::RELATIVE_BASE_PATH.'download/'.$filename;
        return compact('fullPath', 'subPath');
    }
}