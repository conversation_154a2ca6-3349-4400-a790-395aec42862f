<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        //生成token用到的action定义，账号相关操作
        define('ACTION_PROFILE_AUTH', 'auth'); //已认证
        define('ACTION_PROFILE_REGISTER', 'register'); //注册
        define('ACTION_PROFILE_EXPIRE', 'expire'); //账号过期
    }
}
