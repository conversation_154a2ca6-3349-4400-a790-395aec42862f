<?php

namespace App\Jobs;

use App\Models\NotificationInfoModel;
use App\Models\NotificationTemplateModel;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class SendNotice implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $data;
    public $type;
    protected $user;
    private $notificationInfoModel;

    /**
     * Create a new job instance.
     */
    public function __construct($data, $type, $user = [])
    {
        $this->data = $data;
        $this->type = $type;
        // 操作用户ID
        $this->user = $user;
    }

    /**
     * Execute the job.
     */
    public function handle(): bool
    {
        // 通知模板表
        $templateData = NotificationTemplateModel::where('eventCode', $this->type)->first();
        if (empty($templateData)) return false;

        if ($this->type == 'CompanyProgressOnePass') { // 预审表格审核通过
            $templateData = $this->getTemplateData($templateData);

        } else if ($this->type == 'CompanyProgressOneRefuse') { // 预审表格审核失败
            $templateData = $this->getTemplateData($templateData);

        } else if ($this->type == 'CompanyProgressTwoPass') { // 付款表格审核通过
            $templateData = $this->getTemplateData($templateData);

        } else if ($this->type == 'CompanyProgressTwoRefuse') { // 付款表格审核失败
            $templateData = $this->getTemplateData($templateData);
        } else if ($this->type == 'ChildRegister') { // 三三制推荐成功
            $templateData = $this->getChildRegisterData($templateData);
        } else if ($this->type == 'ManagePartner2') { // 有合伙人加盟
            $templateData = $this->getManagePartner2Data($templateData);
        } else if ($this->type == 'ChildSuccessKPI') { // 三三制完成KPI
            $templateData = $this->getChildSuccessKPIData($templateData);
        }

        return $this->sendMessage($templateData);
    }

    /**
     * 发送消息通知
     * @param $messageTitle
     * @param $messageBody
     * @return bool
     */
    public function sendMessage($templateData)
    {
        // 发送通知
        $noticeData = [
            'notificationTitleEN'       => $templateData['notificationTitleEN'],
            'notificationTitleMS'       => $templateData['notificationTitleMS'],
            'notificationTitleZH'       => $templateData['notificationTitleZH'],
            'notificationTitleZT'       => $templateData['notificationTitleZT'],
            'notificationDescriptionEN' => $templateData['notificationDescriptionEN'],
            'notificationDescriptionMS' => $templateData['notificationDescriptionMS'],
            'notificationDescriptionZH' => $templateData['notificationDescriptionZH'],
            'notificationDescriptionZT' => $templateData['notificationDescriptionZT'],
            'notificationTarget'        => $this->data['targetUser'],
            'notificationDepartment'    => 0, // 权限
            'createUser'                => $this->user['profileID'] ?? 0,
            'createRole'                => $this->user['profileRole'] ?? 0,
            'createTime'                => date('Y-m-d H:i:s'),
        ];

        $result = NotificationInfoModel::query()->create($noticeData);
        if ($result) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 替换动态变量值
     * @param $templateData
     * @return mixed
     */
    public function getTemplateData($templateData)
    {
        $templateData['notificationTitleEN'] = str_replace("{{companyName}}", $this->data['companyName'], $templateData['notificationTitleEN']);
        $templateData['notificationTitleMS'] = str_replace("{{companyName}}", $this->data['companyName'], $templateData['notificationTitleMS']);
        $templateData['notificationTitleZH'] = str_replace("{{companyName}}", $this->data['companyName'], $templateData['notificationTitleZH']);
        $templateData['notificationTitleZT'] = str_replace("{{companyName}}", $this->data['companyName'], $templateData['notificationTitleZT']);

        return $templateData;
    }


    /**
     * 替换动态变量值
     * @param $templateData
     * @return mixed
     */
    public function getManagePartner2Data($templateData)
    {

        $templateData['notificationTitleEN'] = str_replace("{{profileName}}", $this->data['profileName'], $templateData['notificationTitleEN']);
        $templateData['notificationTitleMS'] = str_replace("{{profileName}}", $this->data['profileName'], $templateData['notificationTitleMS']);
        $templateData['notificationTitleZH'] = str_replace("{{profileName}}", $this->data['profileName'], $templateData['notificationTitleZH']);
        $templateData['notificationTitleZT'] = str_replace("{{profileName}}", $this->data['profileName'], $templateData['notificationTitleZT']);

        $templateData['notificationDescriptionEN'] = str_replace("{{profileName}}", $this->data['profileName'], $templateData['notificationDescriptionEN']);
        $templateData['notificationDescriptionMS'] = str_replace("{{profileName}}", $this->data['profileName'], $templateData['notificationDescriptionMS']);
        $templateData['notificationDescriptionZH'] = str_replace("{{profileName}}", $this->data['profileName'], $templateData['notificationDescriptionZH']);
        $templateData['notificationDescriptionZT'] = str_replace("{{profileName}}", $this->data['profileName'], $templateData['notificationDescriptionZT']);

        return $templateData;
    }

    /**
     * 替换动态变量值
     * @param $templateData
     * @return mixed
     */
    public function getChildRegisterData($templateData)
    {

        $templateData['notificationTitleEN'] = str_replace(["{{profileName}}", "{{teamName}}"], [$this->data['profileName'],$this->data['teamName']], $templateData['notificationTitleEN']);
        $templateData['notificationTitleMS'] = str_replace(["{{profileName}}", "{{teamName}}"], [$this->data['profileName'],$this->data['teamName']], $templateData['notificationTitleMS']);
        $templateData['notificationTitleZH'] = str_replace(["{{profileName}}", "{{teamName}}"], [$this->data['profileName'],$this->data['teamName']], $templateData['notificationTitleZH']);
        $templateData['notificationTitleZT'] = str_replace(["{{profileName}}", "{{teamName}}"], [$this->data['profileName'],$this->data['teamName']], $templateData['notificationTitleZT']);

        $templateData['notificationDescriptionEN'] = str_replace(["{{profileName}}", "{{teamName}}"], [$this->data['profileName'],$this->data['teamName']], $templateData['notificationDescriptionEN']);
        $templateData['notificationDescriptionMS'] = str_replace(["{{profileName}}", "{{teamName}}"], [$this->data['profileName'],$this->data['teamName']], $templateData['notificationDescriptionMS']);
        $templateData['notificationDescriptionZH'] = str_replace(["{{profileName}}", "{{teamName}}"], [$this->data['profileName'],$this->data['teamName']], $templateData['notificationDescriptionZH']);
        $templateData['notificationDescriptionZT'] = str_replace(["{{profileName}}", "{{teamName}}"], [$this->data['profileName'],$this->data['teamName']], $templateData['notificationDescriptionZT']);

        return $templateData;
    }

    public function getChildSuccessKPIData($templateData)
    {
        $templateData['notificationTitleEN'] = str_replace("{{teamName}}", $this->data['teamName'], $templateData['notificationTitleEN']);
        $templateData['notificationTitleMS'] = str_replace("{{teamName}}", $this->data['teamName'], $templateData['notificationTitleMS']);
        $templateData['notificationTitleZH'] = str_replace("{{teamName}}", $this->data['teamName'], $templateData['notificationTitleZH']);
        $templateData['notificationTitleZT'] = str_replace("{{teamName}}", $this->data['teamName'], $templateData['notificationTitleZT']);

        $templateData['notificationDescriptionEN'] = str_replace("{{teamName}}", $this->data['teamName'], $templateData['notificationDescriptionEN']);
        $templateData['notificationDescriptionMS'] = str_replace("{{teamName}}", $this->data['teamName'], $templateData['notificationDescriptionMS']);
        $templateData['notificationDescriptionZH'] = str_replace("{{teamName}}", $this->data['teamName'], $templateData['notificationDescriptionZH']);
        $templateData['notificationDescriptionZT'] = str_replace("{{teamName}}", $this->data['teamName'], $templateData['notificationDescriptionZT']);

        return $templateData;
    }
}

