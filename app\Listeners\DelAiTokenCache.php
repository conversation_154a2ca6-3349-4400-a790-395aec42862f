<?php

namespace App\Listeners;

use App\Events\UpdateUserAvatar;
use App\Events\UpdateUserEmail;
use Illuminate\Support\Facades\Cache;

class DelAiTokenCache
{
    public function handleUpdateUserEmail(UpdateUserEmail $event): void
    {
        $this->delCache($event->profileId);
    }

    public function handleUpdateUserAvatar(UpdateUserAvatar $event): void
    {
        $this->delCache($event->profileId);
    }

    private function delCache($profileId)
    {
        $cacheConfig = config('cachekey.key_ai_token');
        $key = sprintf($cacheConfig['key'], $profileId);
        Cache::delete($key);
    }
}
