<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class TeamRank extends Model
{
    protected $table = 'cna_team_rank';
    protected $guarded = [];

    // 三三制等级下的管理的人数
    public static $underRankMembersCount = [
        3 => 12,
        4 => 39,
        5 => 120,
        6 => 363,
        7 => 1092,
        8 => 3279,
        9 => 9840,
        10 => 29523,
        11 => 88572,
    ];

    public static function idMapName()
    {
        $data = self::select('id', 'name')->get()->toArray();
        return array_column($data, 'name', 'id');
    }

    public static function rankOptions()
    {
        return self::selectRaw('CAST(id AS CHAR) as value, name as label')->orderBy('id', 'desc')->get()->toArray();
    }

    //根据推荐人信息，判断下级是否有三三制介绍文件
    public static function hasFileUnder($recommendUser)
    {
        if ($recommendUser['team_rank'] > 2) {
            return true;
        }
        return false;
    }

    //根据本人信息，判断是否有三三制介绍文件
    public static function hasFile($user)
    {
        if ($user['team_rank'] > 1) {
            return true;
        }
        return false;
    }

    //获取最高等级的id
    public static function getTopRank()
    {
        return self::max('id');
    }
}
