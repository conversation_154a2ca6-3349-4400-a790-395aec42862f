<?php

namespace App\Http\Controllers\Api\v1\Company;

use App\Http\Controllers\Controller;
use App\Http\Requests\Company\CreateCompany;
use App\Models\ActiveLog;
use Illuminate\Http\Request;
use App\Models\CompanyCategoryModel;
use App\Models\CompanyModel;
use App\Models\ProjectCategoryModel;

class CompanyController extends Controller
{ 
    const COMPANY_STATE = [
        '1' => 'project.edit.submit_form',
        '2' => 'project.edit.submit_pay',
        '3' => 'project.edit.account_setting',
        '4' => 'project.edit.prepare_report',
        '5' => 'project.edit.complete',
    ];
    
    /**
     * Method 企业类别列表
     *
     * @return void
     */
    public function companyCategoryList()
    {
        $data = CompanyCategoryModel::get();
        return responseSuccess($data);
    }
    
    /**
     * Method 创建企业
     *
     * @param CreateCompany $request [explicite description]
     *
     * @return void
     */
    public function createCompany(CreateCompany $request)
    {
        $user = $request->attributes->get('user');
        $body = $request->all();
        $insert = [
            'companyProjectID' => $body['projectID'],
            'companyName' => $body['companyName'],
            'companyRegisterCode' => $body['companyRegisterCode'],
            'companyCategoriesID' => $body['companyCategoriesID'],
            'companyCountryID' => $body['countryID'],
            'companyEmail' => $body['email'],
            'companyMobilePrefixID' => $body['mobilePrefixID'],
            'companyMobile' => $body['phone'],
            'companyProfileID' => $user['profileID']
        ];
        $res = CompanyModel::create($insert);
        if ($res) {
            //记录活动日志
            ActiveLog::log($user['profileID'], ActiveLog::ACTIVE_CREATE, ActiveLog::API_V1_COMPANY_CREATECOMPANY,
                $res->companyID, $insert);
            return responseSuccess();
        } else {
            return responseFail();
        }   
    }
    
    /**
     * Method 获取企业信息
     *
     * @param Request $request [explicite description]
     *
     * @return void
     */
    public function getCompanyInfo(Request $request)
    {
        $user = $request->attributes->get('user');
        $companyID = $request->input('companyID', 0);
        
        if (!$companyID) {
            return responseFail();
        }

        
        $data = CompanyModel::with(['projectCategory' => function ($query) {
            $query->select('projectCategoriesID', 'projectCategoriesNameZH', 'projectCategoriesNameZT',
                'projectCategoriesNameEN', 'projectCategoriesNameMS');
        }])->where('companyID', $companyID)
            ->where('companyProfileID', $user['profileID'])
            ->first();

        if (!$data) {
            return responseFail();
        }
        
        $resData = [
            'companyID' => $data->companyID,
            'companyName' => $data->companyName,
            'companyState' => $data->companyState,
            'companyProgressState' => $data->companyProgressState,
            'companyReason' => $data->companyReason,
            'projectCategoriesNameZH' => $data->projectCategory->projectCategoriesNameZH,
            'projectCategoriesNameZT' => $data->projectCategory->projectCategoriesNameZT,
            'projectCategoriesNameEN' => $data->projectCategory->projectCategoriesNameEN,
            'projectCategoriesNameMS' => $data->projectCategory->projectCategoriesNameMS,
            'progressTxt' => self::COMPANY_STATE[$data->companyProgressState],
        ];

        return responseSuccess($resData);
    }
    
    /**
     * Method 项目列表
     *
     * @param Request $request [explicite description]
     *
     * @return void
     */
    public function projects(Request $request)
    {
        $user = $request->attributes->get('user');
        $result = CompanyModel::with(['projectCategory' => function ($query) {
            $query->select('projectCategoriesID');
        }])->where('companyProfileID', $user['profileID'])
            ->orderByDesc('created_at')
            ->get();

        $groupProjectCategory = ProjectCategoryModel::groupProjectCategory();

        if ($result) {
            foreach ($result as $v) {

                if (!isset($groupProjectCategory[$v->projectCategory->projectCategoriesID])) {
                    continue;
                }
                $groupProjectCategory[$v->projectCategory->projectCategoriesID]['projectItem'][] = [
                    'companyID' => $v->companyID,
                    'companyName' => $v->companyName,
                    'companyState' => self::COMPANY_STATE[$v->companyProgressState]
                ];
            }
            $groupProjectCategory = array_values($groupProjectCategory);
        } 

        return responseSuccess($groupProjectCategory);
    }

}
