<?php

namespace App\Http\Controllers\Admin\v1\Web;

use App\Http\Controllers\Controller;
use App\Models\ActiveLog;
use App\Models\StructureModel;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class StructureController extends Controller
{
    public function index(Request $request)
    {
        $keyword = $request->input('keyword', '');
        $pageSize = $request->input('pageSize', 10);

        $structures = StructureModel::when($keyword != '', function ($query) use ($keyword) {
            $query->where(function ($q) use ($keyword) {
                $q->where('structureCode', 'like', '%' . $keyword . '%')
                    ->orWhere('structureEN', 'like', '%' . $keyword . '%')
                    ->orWhere('structureMS', 'like', '%' . $keyword . '%')
                    ->orWhere('structureZH', 'like', '%' . $keyword . '%')
                    ->orWhere('structureZT', 'like', '%' . $keyword . '%');
            });
        })
        ->paginate($pageSize);

        $items = $structures->items();

        $paginate = [
            'page_size' => $pageSize,
            'current_page' => $structures->currentPage(),
            'total' => $structures->total(),
            'total_page' => $structures->lastPage(),
        ];

        return responseSuccess(compact('items', 'paginate'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'structureCode' => 'required|string|max:255|unique:cna_web_structure,structureCode',
            'structureEN' => 'required|string|max:255',
            'structureMS' => 'required|string|max:255',
            'structureZH' => 'required|string|max:255',
            'structureZT' => 'required|string|max:255'
        ]);

        $user = $request->attributes->get('user');

        try {
            DB::beginTransaction();

            $newStructure = StructureModel::create($request->all());

            ActiveLog::log(
                $user['profileID'],
                ActiveLog::ADMIN_API_V1_CREATE,
                ActiveLog::ADMIN_API_V1_OA,
                $newStructure->structureID,
                $request->structureCode,
                ActiveLog::SYSTEM_CNA_ADMIN
            );

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            return responseFail($e->getMessage());
        }

        return responseSuccess();
    }

    public function update(Request $request, $id)
    {
        $request->validate([
            'structureCode' => 'required|string|max:255|unique:cna_web_structure,structureCode,' . $id . ',structureID',
            'structureEN' => 'required|string|max:255',
            'structureMS' => 'required|string|max:255',
            'structureZH' => 'required|string|max:255',
            'structureZT' => 'required|string|max:255'
        ]);

        $structure = StructureModel::findOrFail($id);

        $user = $request->attributes->get('user');

        try {
            DB::beginTransaction();

            $structure->update($request->all());

            ActiveLog::log(
                $user['profileID'],
                ActiveLog::ADMIN_API_V1_EDIT,
                ActiveLog::ADMIN_API_V1_OA,
                $id,
                $structure->structureCode,
                ActiveLog::SYSTEM_CNA_ADMIN
            );

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            return responseFail($e->getMessage());
        }

        return responseSuccess();
    }
}
