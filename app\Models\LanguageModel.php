<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class LanguageModel extends Model
{
    use HasFactory;

    protected $table = 'cna_web_language';
    protected $guarded = [];

    public function getData()
    {
        $data = self::select('languageType', 'languageCode')->where('languageStatus', 'Published')->get();
        return $data;
    }

    public function getAll()
    {
        return self::select('languageType', 'languageCode')->get();
    }
}
