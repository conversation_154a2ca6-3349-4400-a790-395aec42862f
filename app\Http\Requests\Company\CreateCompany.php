<?php

namespace App\Http\Requests\Company;

use App\Rules\Company\CheckCompanyCategoryRule;
use App\Rules\User\CheckCountryRule;
use App\Rules\Mobile\CheckMobilePrefixRule;
use App\Rules\Project\CheckProjectCategoryRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use App\Exceptions\DefaultException;

class CreateCompany extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'projectID'           => ['required', new CheckProjectCategoryRule],
            'companyName'         => ['required'],
            'companyRegisterCode' => ['required'],
            'companyCategoriesID' => ['required', new CheckCompanyCategoryRule],
            'countryID'           => ['required', new CheckCountryRule],
            'email'               => ['required', 'email'],
            'mobilePrefixID'      => ['required', new CheckMobilePrefixRule],
            'phone'               => ['required'],
        ];
    }

    public function messages()
    {
        return [
            'projectID.required'           => __('please select project'),
            'companyName.required'         => __('please fill in the company name'),
            'companyRegisterCode.required' => __('please fill in the enterprise registration number'),
            'companyCategoriesID.required' => __('please select company category'),
            'countryID.required'           => __('please select country'),
            'email.required'               => __('please fill in the correct email'),
            'email.email'                  => __('please fill in the correct email'),
            'mobilePrefixID.required'      => __('please select phone area code'),
            'phone.required'               => __('please fill in the phone'),
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        $error= $validator->errors()->all();
        throw new DefaultException($error[0]);
    }
}
