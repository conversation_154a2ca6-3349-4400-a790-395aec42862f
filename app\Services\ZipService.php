<?php
namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use ZipArchive;

class ZipService
{
    /**
     * 打包文件
     * @param $files 要打包的文件
     * @param $zipFileName 打包文件名
     * @return string
     */
    public static function createZip($files, $zipFileName)
    {

        // 定义输出的 ZIP 文件路径
        $zipFilePath = storage_path('app/public/' . $zipFileName);

        // 创建 ZIP 归档
        $zip = new ZipArchive();
        if ($zip->open($zipFilePath, ZipArchive::CREATE | ZipArchive::OVERWRITE) === TRUE) {
            foreach ($files as $url) {
                // 下载远程文件内容
                $fileContent = file_get_contents($url);

                if ($fileContent === false) {

                    continue;
                }

                // 获取文件名
                $fileName = basename($url);

                // 将文件内容添加到 ZIP 归档
                $zip->addFromString($fileName, $fileContent);
            }
            $zip->close();
            return $zipFilePath;
        } else {
            return false;
        }
    }

    
}