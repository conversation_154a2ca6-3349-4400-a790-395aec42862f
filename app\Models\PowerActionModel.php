<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PowerActionModel extends Model
{
    use HasFactory;

    protected $table = 'cna_power_action';
    protected $guarded = [];
    protected $primaryKey = 'powerActionID';

    protected $appends = [
        'structureEN',
        'structureMS',
        'structureZH',
        'structureZT'
    ];


    public function powerInfo()
    {
        return $this->belongsTo(PowerInfoModel::class, 'powerID');
    }

    public function structureInfo()
    {
        return $this->hasOne(StructureModel::class,'structureCode', 'structureCode');
    }

    public function getStructureENAttribute()
    {
        return $this->structureInfo()->value('structureEN');
    }

    public function getStructureMSAttribute()
    {
        return $this->structureInfo()->value('structureMS');
    }

    public function getStructureZHAttribute()
    {
        return $this->structureInfo()->value('structureZH');
    }

    public function getStructureZTAttribute()
    {
        return $this->structureInfo()->value('structureZT');
    }


}