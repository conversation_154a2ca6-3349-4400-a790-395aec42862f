<?php

namespace App\Http\Requests\Company\ProgressTwo;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use App\Exceptions\DefaultException;
use App\Rules\Company\ProgressTwo\CheckBankRule;
use App\Rules\Company\ProgressTwo\CheckPaymentRule;

class ProgressTwoCommitRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'companyID'         => ['required'],
            'bankID'            => ['required', new CheckBankRule],
            'paymentCategoryID' => ['required', new CheckPaymentRule],
            'paymentCode'       => ['required'],
            'file'              => ['required', 'file', 'mimes:jpg,jpeg,png,pdf', 'max:'.(env('ALLOW_FILE_SIZE')*1024)],
        ];
    }

    public function messages()
    {
        return [
            'companyID.required'         => __('operation failed'),
            'bankID.required'            => __('please select bank'),
            'paymentCategoryID.required' => __('please select payment method'),
            'paymentCode.required'       => __('please fill in the payment number'),
            'file.required'              => __('please upload payment proof'),
            'file.file'                  => __('please upload payment proof'),
            'file.mimes'                 => __('incorrect format file'),
            'file.max'                   => __('exceed size file', ['limit' => env('ALLOW_FILE_SIZE').'M']),
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        $error= $validator->errors()->all();
        throw new DefaultException($error[0]);
    }
}
