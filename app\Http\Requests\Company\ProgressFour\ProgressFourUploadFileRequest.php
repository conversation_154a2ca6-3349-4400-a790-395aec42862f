<?php

namespace App\Http\Requests\Company\ProgressFour;

use App\Rules\Company\ProgressFour\CheckFileRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use App\Exceptions\DefaultException;

class ProgressFourUploadFileRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'fileID' => ['required', new CheckFileRule], 
            'file'   => ['required', 'file', 'mimes:jpg,jpeg,png,pdf', 'max:'.(env('ALLOW_FILE_SIZE')*1024)]
        ];
    }

    public function messages()
    {
        return [
            'fileID.required' => __('please upload file'),
            'file.required'   => __('please upload file'),
            'file.file'       => __('please upload file'),
            'file.mimes'      => __('incorrect format file'),
            'file.max'        => __('exceed size file', ['limit' => env('ALLOW_FILE_SIZE').'M'])
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        $error= $validator->errors()->all();
        throw new DefaultException($error[0]);
    }
}
