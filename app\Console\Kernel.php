<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        // 定期清理过期通知用户数据
        $schedule->command('notifiy_validity_clean')->everyMinute()->withoutOverlapping();
        $schedule->command('calendar_notify')->everyMinute()->withoutOverlapping();
        //$schedule->command('nric_expire_notify')->dailyAt('1:00')->withoutOverlapping();
        //$schedule->command('bank_card_expire_notify')->dailyAt('1:00')->withoutOverlapping();
        //$schedule->command('user_expire_notify')->dailyAt('1:00')->withoutOverlapping();
        $schedule->command('wechat_check_order')->everyMinute()->withoutOverlapping();
        $schedule->command('profile_setting_notify')->everyMinute()->withoutOverlapping();
        $schedule->command('sync_user')->everyMinute()->withoutOverlapping();
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
