<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>计算公式</title>
</head>
<body>



<div style="display: flex; align-items: center; flex-direction: column">
    <h2>计算公式</h2>
    <div class="form">
        <form action="/api/v1/admin/countFee" method="post">

            <!-- 下拉选择框 -->
            <div class="row">
                <label for="roleid">角&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;色:</label>
                <select id="roleid" name="roleid">
                    <option value="1" @if (isset($param['roleid']) && $param['roleid']==1) selected @endif >联盟合伙人</option>
                    <option value="2" @if (isset($param['roleid']) && $param['roleid']==2) selected @endif>管理合伙人</option>
                    <option value="3" @if (isset($param['roleid']) && $param['roleid']==3) selected @endif>三三制</option>
                    <!-- 更多选项 -->
                </select>
            </div>

            <div id="grade"   style="display: none">
                <label for="rank" style="margin-right: 18px">三三制等级:</label>
                <select id="rank" name="rank">
                    <option value="10" @if (isset($param['rank']) && $param['rank']=='10') selected @endif>总-营运总裁</option>
                    <option value="9" @if (isset($param['rank']) && $param['rank']=='9') selected @endif>军-战略总监</option>
                    <option value="8" @if (isset($param['rank']) && $param['rank']=='8') selected @endif>师-策划总监</option>
                    <option value="7" @if (isset($param['rank']) && $param['rank']=='7') selected @endif>旅-行政总监</option>
                    <option value="6" @if (isset($param['rank']) && $param['rank']=='6') selected @endif>团-市场总监</option>
                    <option value="5" @if (isset($param['rank']) && $param['rank']=='5') selected @endif>营-执行总监</option>
                    <option value="4" @if (isset($param['rank']) && $param['rank']=='4') selected @endif>连-监督主任</option>
                    <option value="3" @if (isset($param['rank']) && $param['rank']=='3') selected @endif>排-咨询主任</option>
                    <option value="2" @if (isset($param['rank']) && $param['rank']=='2') selected @endif>班-指导主任</option>
                    <option value="1" @if (isset($param['rank']) && $param['rank']=='1') selected @endif>组-业务主任</option>
                </select>
            </div>


            <div class="row">
                <label for="case">服务项目:</label>
                <select id="case" name="case">
                    <option value="A" @if (isset($param['case']) && $param['case']=='A') selected @endif>A-专业服务费</option>
                    <option value="B" @if (isset($param['case']) && $param['case']=='B') selected @endif>B-专项服务费</option>
                    <option value="C" @if (isset($param['case']) && $param['case']=='C') selected @endif>C-开户服务费</option>
                    <option value="D" @if (isset($param['case']) && $param['case']=='D') selected @endif>D-年度服务费</option>
                    <option value="E" @if (isset($param['case']) && $param['case']=='E') selected @endif>E-成功交易费</option>
                    <!-- 更多选项 -->
                </select>
            </div>

            <div class="row">
                <label for="amount">金&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;额:</label>
                <input type="text" id="amount" name="amount" required @if (isset($param['amount'])) value="{{$param['amount']}}" @endif>
            </div>



            <!-- 提交按钮 -->
            <div>
                <input type="submit" value="提交">
            </div>

        </form>
    </div>

    <div class="content">

        <div class="left">
            <table border="1" width="700">
                <tr>
                    <td>职位</td>
                    <td>A-专业服务费</td>
                    <td>B-专项服务费</td>
                    <td>C-开户服务费</td>
                    <td>D-年度服务费</td>
                    <td>E-成功交易费</td>
                </tr>
                @foreach ($teamData_init as $key => $item)
                    <tr>
                        <td>{{$item['name']}}</td>
                        <td>{{$item['caseA']}}%</td>
                        <td>{{$item['caseB']}}%</td>
                        <td>{{$item['caseC']}}%</td>
                        <td>{{$item['caseD'] }}%</td>
                        <td>{{$item['caseE'] }}%</td>
                    </tr>
                @endforeach
                @foreach ($data_init as $key => $item)
                    <tr>
                        <td>{{$item['name']}}</td>
                        <td>{{$item['caseA']}}%</td>
                        <td>{{$item['caseB']}}%</td>
                        <td>{{$item['caseC']}}%</td>
                        <td>{{$item['caseD'] }}%</td>
                        <td>{{$item['caseE'] }}%</td>
                    </tr>
                @endforeach

            </table>
        </div>

        <div class="right">
            {{--三三制分开显示--}}
          {{--  @if (isset($param['roleid']) && $param['roleid']==3)

                <table border="1" width="600">
                    <tr>
                        <td>服务项目</td>
                        <td>津贴</td>
                        <td>级别</td>
                        <td>金额</td>
                        <td>比例</td>
                        <td>收入</td>
                    </tr>

                    @foreach ($teamData as $key => $item)
                        <tr>
                            <td>{{$param['case_name']}}</td>
                            <td>{{$item['jintie']}}</td>
                            <td>{{$item['name']}}</td>
                            <td>{{$param['amount_name']}}</td>
                            <td>{{ $item['key'] }}%</td>
                            <td>{{ $item['value'] }}元</td>
                        </tr>
                    @endforeach

                </table>
                <br><br>
            @endif--}}


            @if(isset($data))

                <table border="1" width="700">
                    <tr>
                        <td>服务项目</td>
                        <td>津贴</td>
                        <td>级别</td>
                        <td>金额</td>
                        <td>比例</td>
                        <td>收入</td>
                    </tr>
                    @if (isset($param['roleid']) && $param['roleid']==3)
                        @foreach ($teamData as $key => $item)
                            <tr>
                                <td>{{$param['case_name']}}</td>
                                <td>{{$item['jintie']}}</td>
                                <td>{{$item['name']}}</td>
                                <td>{{$param['amount_name']}}</td>
                                <td>{{ $item['key'] }}%</td>
                                <td>{{ $item['value'] }}元</td>
                            </tr>
                        @endforeach

                    @endif
                    @foreach ($data as $key => $item)
                        <tr>
                            <td>{{$param['case_name']}}</td>
                            <td>{{$item['jintie']}}</td>
                            <td>{{$item['name']}}</td>
                            <td>{{$param['amount_name']}}</td>
                            <td>{{ $item['key'] }}%</td>
                            <td>{{ $item['value'] }}元</td>
                        </tr>
                    @endforeach

                </table>
            @endif
        </div>




    </div>

</div>


</body>
<style>
    .form{}
    .row{
        margin: 15px 0;
        display: flex;
    }
    .row label{
        width: 100px;
        display: block;
    }

    .content{
        margin-top:50px;

    }

    .content .left{

        margin-right: 20px;
        margin-bottom: 50px;
    }

    .content .left table td, .content .right table td{
        text-align: center;
    }

    .content .right{
        margin-bottom: 50px;
    }
</style>
<script>
    // 获取 select 元素和显示选择结果的元素
    var selectElement = document.getElementById('roleid');
    var selectedValueElement = document.getElementById('roleid');
    var selectedOption = selectElement.value;
    if (selectedOption == 3) {
        document.getElementById('grade').style.display = 'inline-flex'
    } else {

        document.getElementById('grade').style.display = 'none'
    }

    // 为 select 元素添加 change 事件监听器
    selectElement.addEventListener('change', function() {
        // 获取选中的选项值
        selectedOption = this.value;
        if (selectedOption == 3) {
            document.getElementById('grade').style.display = 'inline-flex'
        } else {

            document.getElementById('grade').style.display = 'none'
        }
    });
</script>
</html>