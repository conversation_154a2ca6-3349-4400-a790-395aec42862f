<?php

namespace App\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class UpdateUserAvatar
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $profileId;

    /**
     * Create a new event instance.
     */
    public function __construct($profileId)
    {
        $this->profileId = $profileId;
    }
}
