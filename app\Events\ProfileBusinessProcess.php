<?php

namespace App\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;

class ProfileBusinessProcess
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $orderId;
    public $totalAmount;

    /**
     * Create a new event instance.
     */
    public function __construct($orderId, $totalAmount)
    {
        $this->orderId = $orderId;
        $this->totalAmount = $totalAmount;
    }
}
