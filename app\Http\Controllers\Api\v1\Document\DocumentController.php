<?php

namespace App\Http\Controllers\Api\v1\Document;

use App\Http\Controllers\Controller;
use App\Models\DocumentFileDataModel;
use App\Services\DocService;
use Illuminate\Http\Request;
use App\Models\DocumentInfoModel;
use App\Models\TeamRank;
use App\Models\ProfileContractModel;

class DocumentController extends Controller
{

    private $request;
    private $documentInfoModel;
    const ACTION = 'document_verify'; //场景

    public function __construct(Request $request, DocumentInfoModel $documentInfoModel)
    {
        $this->request = $request;
        $this->documentInfoModel = $documentInfoModel;
    }

    public function documentList()
    {
        $user = $this->request->attributes->get('user');
        $pageSize = $this->request->input('pageSize', 10);
        $page     = $this->request->input('page', 1);
        $keyword = $this->request->input('keyword', '');
        $where = [];

        if ($keyword) {
            $where = [['documentTitle', 'like', '%'.$keyword.'%']];
        }

        $data = $this->documentInfoModel::select('documentID', 'documentTitle', 'type')->where($where)
            ->orderBy('order', 'desc')->orderBy('documentID', 'asc')->get()->toArray();

        $unset = [];

        foreach ($data as $k => &$item) {
            if ($item['type'] ==  DocService::TYPE_TEAM) {
                if (TeamRank::hasFile($user)) {
                    $rankFile = TeamRank::find($user['team_rank']);
                    $item['documentTitle'] = $rankFile['job'] . __('Manual');
                    $item['documentVersion'] = $rankFile['file_version'];
                } else {
                    $unset[] = $k;
                }
            } else if ($item['type'] ==  DocService::TYPE_CN_SIGN) {
                //todo
            }
        }

        foreach ($unset as $k) {
            unset($data[$k]);
        }

        $document = array_values($data);

        // 分页参数
        $total = count($document);
        $currentPage = $page;
        $perPage = $pageSize;
        $totalRecord = $total;
        $totalPage = ceil($total/$pageSize);

        $paginate = [
            'currentPage' => $currentPage,
            'perPage' => $perPage,
            'totalRecord' => $totalRecord,
            'totalPage' => $totalPage
        ];

        // 当前页
        $offset = ($currentPage - 1) * $perPage;
        $document =  array_slice($document, $offset, $perPage);

        $compact = compact('document', 'paginate');

        return responseSuccess($compact);
    }

    public function generateDocumentToken()
    {
        $user = $this->request->attributes->get('user');
        $documentID = $this->request->input('documentID', 0);
        if ($documentID == 0) { // 团队文件
            $documentFile = TeamRank::find($user['team_rank']);
            $version = $documentFile['file_version'];
            $file = $documentFile['file'];
            $name = $documentFile['job'] . '手册';
        } else {
            $documentRecord = $this->documentInfoModel->getRecord($documentID);
            if (!$documentRecord) {
                return responseFail();
            }

            // 下载最新版本
            $documentFile = DocumentFileDataModel::where('documentID', $documentID)->orderBy('documentXID', 'desc')->first();
            if (empty($documentFile)) {
                return responseFail();
            }
            $version = $documentFile['documentVersion'];
            $file = $documentFile['documentFile'];
            $name = $documentRecord['documentTitle'];
        }

        if ($version) {
            $name .= ' ' . $version;
        }
        // 特别处理，当documentID=6时，即合伙人加盟合同，就直接显示用户签署的合同
        if ($documentID == 6) {
            $file = ProfileContractModel::where('profileID', $user->profileID)->value('contract');
        }
        $extPos = strrpos($file, '.');
        $ext = substr($file, $extPos);
        $token = DocService::generateToken($file, self::ACTION, 3600, $name.$ext);
        return responseSuccess($token);
    }

    public function previewDocument()
    {
        $token = $this->request->route('token');

        return DocService::preview($token, self::ACTION);
    }

    public function downloadDocument()
    {
        $token = $this->request->route('token');

        return DocService::download($token, self::ACTION);
    }
}
