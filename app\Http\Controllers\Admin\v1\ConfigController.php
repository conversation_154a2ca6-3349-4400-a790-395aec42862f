<?php
namespace App\Http\Controllers\Admin\v1;

use App\Http\Controllers\Controller;
use App\Models\BankModel;
use App\Models\CountryModel;
use App\Models\CurrencyModel;
use App\Models\DateModel;
use App\Models\LanguageModel;
use App\Models\StructureModel;
use App\Models\TimeModel;
use App\Models\TimezoneModel;
use Illuminate\Support\Facades\Validator;
use Symfony\Component\HttpFoundation\Request;

class ConfigController extends Controller
{
    /**
     * 多语言翻译列表
     * @return null
     */
    public function structure()
    {
        $data = StructureModel::select(
            'structureCode',
            'structureEN',
            'structureMS',
            'structureZH',
            'structureZT'
        )->get();

        return responseSuccess($data);
    }

    /**
     * 国家列表
     * @return null
     */
    public function countryList()
    {
        $data = CountryModel::select('countryID', 'countryZH')->get();
        return responseSuccess($data);
    }

    /**
     * 手机号前缀
     * @return null
     */
    public function mobilePrefixList()
    {
        $data = CountryModel::select('countryID AS prefixID', 'countryZH AS country', 'countryCode AS prefixCode', 'countryISOCode2')
            // 国家的电话代码 暂时只需要中国，香港，马来西亚，新加坡，英国
            ->whereIn('countryZH', ['中国', '香港', '马来西亚', '新加坡', '英国'])
            ->get();
        return responseSuccess($data);
    }

    /**
     * 银行列表
     * @return null
     */
    public function bankList()
    {
        $data = BankModel::select('ID', 'bankName', 'bankCountry', 'bankSwiftCode')->get();
        return responseSuccess($data);
    }

    /**
     * 国家列表代码
     * @return null
     */
    public function countries()
    {
        $data = CountryModel::select('countryID', 'countryEN', 'countryMS', 'countryZH', 'countryZT', 'countryCode', 'countryISOCode2')->get();
        return responseSuccess($data);
    }

    /**
     * 语言与地区配置
     * @return null
     */
    public function configurations()
    {
        $languageModel = new LanguageModel;
        $timezoneModel = new TimezoneModel;
        $currencyModel = new CurrencyModel;
        $dateModel = new DateModel;
        $timeModel = new TimeModel;

        $languageList = $languageModel->getData();
        $timezoneList = $timezoneModel->getData();
        $currencyList = $currencyModel->getData();
        $dateFormatList = $dateModel->getData();
        $timeFormatList = $timeModel->getData();

        $data = compact('languageList','timezoneList', 'currencyList', 'dateFormatList', 'timeFormatList');

        return responseSuccess($data);
    }

}