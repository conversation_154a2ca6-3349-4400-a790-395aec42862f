<?php

namespace App\Http\Controllers\Admin\v1;

use App\Http\Controllers\Controller;
use App\Jobs\SendEmail;
use App\Models\ProfileInfoModel;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class AssociateController extends Controller
{
    public function index(Request $request)
    {
        $status = $request->get('status', '');
        $keyword = $request->get('keyword', '');
        $page = $request->get('page', 1);
        $pageSize = $request->get('page_size', 10);

        $associates = ProfileInfoModel::with(['professionals', 'skills', 'nationality'])
            ->when($status != '', function ($q) use ($status) {
                $q->where('status', $status);
            })
            ->when($keyword != '', function ($q) use ($keyword) {
                $q->where(function ($q) use ($keyword) {
                    $q->where('profileName', 'like', '%' . $keyword . '%')
                        ->orWhere('profileEmail', 'like', '%' . $keyword . '%')
                        ->orWhere('profileNRIC', 'like', '%' . $keyword . '%')
                        ->orWhere('profileContact', 'like', '%' . $keyword . '%');
                });
            })
            ->paginate($pageSize);

        $items = $associates->items();

        $items = collect($items)->map(function ($item) {
            $item->profileAvatar = $item->profileAvatar ? storageUrl($item->profileAvatar) : null;
            return $item;
        });

        $paginate = [
            'page' => $page,
            'pageSize' => $pageSize,
            'total' => $associates->total(),
            'totalPage' => $associates->lastPage(),
        ];

        return responseSuccess(compact('items', 'paginate'));
    }

    public function updateProfile(Request $request)
    {
        $request->validate([
            'id' => 'required|exists:cna_profile_info,profileID',
            'profileAddressStreet' => 'required',
            'profileAddressUnit' => 'required',
            'profileAddressDistrict' => 'required',
            'profileAddressCity' => 'required',
            'profileAddressPostcode' => 'required',
            'profileAddressCountry'  => 'required',
            'profileAddressState'    => 'required',
            'professionals' => 'required|json',
            'skills' => 'required|json',
            'experiences' => 'required|json',
        ]);

        // 更新数据
        $profile = ProfileInfoModel::find($request->id);

        try {
            DB::beginTransaction();

            $profile->update([
                'profileAddressDistrict' => $request->profileAddressDistrict,
                'profileAddressStreet' => $request->profileAddressStreet,
                'profileAddressUnit' => $request->profileAddressUnit,
                'profileAddressCity' => $request->profileAddressCity,
                'profileAddressPostcode' => $request->profileAddressPostcode,
                'profileAddressCountry' => $request->profileAddressCountry,
                'profileAddressState' => $request->profileAddressState
            ]);
    
            // 专业
            $professionals = [];
            foreach (json_decode($request->professionals, true) as $professional) {
                $professionals[$professional['id']] = [
                    'professionalDescription' => $professional['desc'],
                ];
            }
            $profile->professionals()->sync($professionals);
    
            // 技能
            $skills = [];
            foreach (json_decode($request->skills, true) as $skill) {
                $skills[$skill['id']] = [
                   'skillDescription' => $skill['desc'],
                ];
            }
            $profile->skills()->sync($skills);
    
            // 经验
            $experiences = [];
            foreach (json_decode($request->experiences, true) as $experience) {
                $experiences[$experience['id']] = [
                    'experienceDescription' => $experience['desc'],
                ];
            }
            $profile->experiences()->sync($experiences);
    
            // 记录日志
            activity('edited_profiles')
                ->causedBy($request->user())
                ->performedOn($profile)
                ->tap(function ($activity) use ($request) {
                    $activity->description = '修改合伙人资料';
                    $activity->properties = $request->all();
                })
                ->event('修改合伙人资料')
                ->log('修改合伙人资料');

            DB::commit();

            // 发送邮件
            // dispatch(new SendEmail());

            // 站内通知

            return responseSuccess();
        } catch (\Exception $e) {
            DB::rollBack();
            return responseFail($e->getMessage());
        }
    }

    public function updatePassword(Request $request)
    {
        $request->validate([
            'id' => 'required|exists:cna_profile_info,profileID',
            'password' => 'required|alphanum|min:6',
            'password_confirmation' => 'required|same:password',
        ]);

        // 更新数据
        $profile = ProfileInfoModel::find($request->id);

        try {
            DB::beginTransaction();

            $profile->update([
                'profilePassword' => hashPassword(hash('sha256', $request->password)),
            ]);

            // 记录日志
            activity('edited_profiles')
                ->causedBy($request->user())
                ->performedOn($profile)
                ->tap(function ($activity) use ($request) {
                    $activity->description = '修改合伙人密码';
                    $activity->properties = $request->all();
                })
                ->event('修改合伙人密码')
                ->log('修改合伙人密码');

            DB::commit();

            // 发送邮件
            // dispatch(new SendEmail());

            // 站内通知

            return responseSuccess();
        } catch (\Exception $e) {
            DB::rollBack();
            return responseFail($e->getMessage());
        }
    }

    // 审核
    public function review(Request $request)
    {
        $request->validate([
            'id' => 'required|exists:cna_profile_info,profileID',
            'status' => 'required|in:1,2',
            'review_content' => 'required_if:status,2',
        ]);

        // 更新数据
        $profile = ProfileInfoModel::find($request->id);

        // 记录日志
        activity('edited_profiles')
            ->causedBy($request->user())
            ->performedOn($profile)
            ->tap(function ($activity) use ($request) {
                $activity->description = '审核合伙人信息';
                $activity->properties = $request->all();
            })
            ->event('审核合伙人信息')
            ->log('审核合伙人信息');

        // 发送邮件
        // dispatch(new SendEmail());

        // 站内通知

        return responseSuccess();
    }
}
