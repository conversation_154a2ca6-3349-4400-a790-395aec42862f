<?php

namespace App\Http\Requests\Register;

use App\Rules\Register\NationalityRule;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use App\Exceptions\DefaultException;
use App\Rules\Mobile\CheckMobilePrefixRule;

class Register extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'email'         => ['required', 'email'],
            'nationalityID' => ['required', new NationalityRule],
            'prefixID'      => ['required', new CheckMobilePrefixRule],
            'phone'         => ['required']
        ];
    }

    public function messages()
    {
        return [
            'email.required'         => __('please fill in the correct email'), 
            'email.email'            => __('please fill in the correct email'), 
            'nationalityID.required' => __('please select country'),
            'prefixID.required'      => __('please select phone area code'),
            'phone.required'         => __('please fill in the phone')
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        $error= $validator->errors()->all();
        throw new DefaultException($error[0]);
    }
}
